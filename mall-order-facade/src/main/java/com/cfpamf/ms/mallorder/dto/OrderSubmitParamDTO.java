package com.cfpamf.ms.mallorder.dto;

import com.cfpamf.ms.mallorder.enums.OrderPlaceUserRole;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NonNull;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 订单提交前后端参数传递类
 * 用于：
 * 1.点击结算按钮、立即购买跳转提交订单页
 * 2.提交订单页，修改优惠券、修改积分、修改地址，更新页面数据
 * 3.提交订单，保存订单及相关数据表
 */
@Data
public class OrderSubmitParamDTO implements Serializable {
    private static final long serialVersionUID = -2964136902144210150L;

    @ApiModelProperty(value = "下单渠道：H5-浏览器H5，APP-乡助APP，WE_CHAT-微信浏览器，MINI_PRO-小程序")
    private String channel;

    @ApiModelProperty(value = "分享下单用户usrNo")
    private String usrNo;

    @ApiModelProperty("下单会员id")
    private Integer memberId;

    @ApiModelProperty(value = "1==立即购买、去结算;2==提交订单页，修改优惠券、修改积分、修改地址，更新页面数据;3==提交订单", required = true)
    private Integer source;

    @ApiModelProperty(value = "订单来源1、pc；2、H5；3、Android；4、IOS; 5-微信小程序； 提交订单必传")
    private Integer orderFrom;

    @ApiModelProperty(value = "会员收货地址id； 提交订单必传")
    private Integer addressId;

    @ApiModelProperty(value = "自提点ID；自提订单必传")
    private Long pointId;

    @ApiModelProperty(value = "店铺id")
    private String storeId;

    @ApiModelProperty(value = "使用多张平台优惠券编码")
    private List<String> platformCouponCodeList;

    @ApiModelProperty(value = "使用积分数量")
    private Integer integral;

    @ApiModelProperty(value = "店铺信息")
    private List<StoreInfo> storeInfoList;

    @ApiModelProperty(value = "地区编码")
    private String areaCode;

    @ApiModelProperty(value = "是否购物车下单,默认为true,立即下单、活动下单时为false", required = true)
    private Boolean isCart = true;

    @ApiModelProperty("商品业务类型：1.C端商品 2.采购商品")
    private Integer productType = 1;

    @ApiModelProperty(value = "是否单独购买,默认为false（暂时拼团使用）")
    private Boolean isAloneBuy = false;

    @ApiModelProperty("拼团团队id（参团使用）")
    private Integer spellTeamId = 0;

    @ApiModelProperty(value = "邀请人分享码")
    private String shareCode;

    @ApiModelProperty(value = "订单模式：C端店铺街：1，B端采购中心: 2")
    private Integer orderPattern = 1;

    @ApiModelProperty(value = "经销商编码")
    private String dealerCode;

    @ApiModelProperty("拼单标识：0-不参与 1-未拼单 2-已拼单")
    private Integer groupBuyingTag = 0;

    //非购物车下单信息
    @ApiModelProperty(value = "skuId，非购物车下单必传")
    private Long productId;

    @ApiModelProperty(value = "购买数量，非购物车下单必传")
    private Integer number;

    @ApiModelProperty(value = "订单号，尾款订单必传")
    private String orderSn;

    @ApiModelProperty(value = "支付单号")
    private String pno;

    @ApiModelProperty(value = "验证码、支付金额为0时必传")
    private String verifyCode;

    @ApiModelProperty(value = "用户选中乡助卡券码集合")
    private List<String> cardCodeList;

    @ApiModelProperty(value = "金融规则编号")
    private String financeRuleCode;

    @ApiModelProperty(value = "发货要求：1：随时发货 2：10至20个自然日之间")
    private Integer deliveryRequirements;

    @ApiModelProperty(hidden = true, value = "收货地址详情")
    private OrderAddressDTO orderAddress;

    @ApiModelProperty(value = "采购订单收货地址信息")
    private OrderAddressDTO purchaseOrderAddress;

    @ApiModelProperty(hidden = true, value = "是否渠道订单")
    private boolean isChannelOrder = false;
    
    @ApiModelProperty(hidden = true, value = "是否银行转账提交")
    private boolean bankTransferable = false;
    
    @ApiModelProperty(hidden = true, value = "订单类型")
    private OrderTypeEnum orderType;

    @ApiModelProperty(value = "活动相关信息参数，暂时存放的预售信息")
    private OrderPromotionParamDTO orderPromotionInfo;

    @ApiModelProperty(value = "货品idList-仅限辅助下单使用（多个商品无购物车）")
    private List<OrderProductInfo> orderProductInfoList;

    @ApiModelProperty(hidden = true, value = "是否分享下单")
    private boolean shareResource = false;

    @ApiModelProperty(value = "订单确认页单个店铺多个商品标记（无购物车）")
    private boolean multiCommodity = false;

    @ApiModelProperty("货品idList-订单确认页单个店铺多个商品使用（无购物车）")
    private List<MultiProductInfo> multiProductInfoList;

    @ApiModelProperty("代客下单标记（组合商品、卡券商品接口使用）")
    private boolean placingCombinationFlag = false;

    @ApiModelProperty(value = "下单用户角色:本人：SELF，客户经理（BAPP客户经理代客下单传入）：CUSTOMER_MANAGER，站长：STATION_MASTER")
    private OrderPlaceUserRole orderPlaceUserRole;

    @ApiModelProperty(value = "贷款支付人：SELF-本人(社员);STATION_MASTER-站长;下单渠道为乡信APP或乡信小程序则必填")
    private String loanPayer;

    @ApiModelProperty(value = "贷款确认方式：READ-完成订单阅读确认(客户端);FACE_DETECTION-完成订单人脸识别（站长端）;下单渠道为乡信APP或乡信小程序则必填")
    private String loanConfirmMethod;

    @ApiModelProperty(value = "附件（代客下单使用）")
    private List<String> attachmentUrls;




    /**
     * 根据店铺id获取店铺信息
     *
     * @param storeId
     * @return
     */
    public StoreInfo getStoreInfoByStoreId(Long storeId) {
        if (!CollectionUtils.isEmpty(this.storeInfoList)) {
            for (StoreInfo storeInfo : storeInfoList) {
                if (storeId.equals(storeInfo.getStoreId())) {
                    return storeInfo;
                }
            }
        }
        return null;
    }

    @Data
    public static class StoreInfo implements Serializable {
        private static final long serialVersionUID = 79094261106390951L;
        @ApiModelProperty(value = "店铺id", required = true)
        private Long storeId;
        @ApiModelProperty(value = "会员发票id")
        private Integer invoiceId;
        @ApiModelProperty(value = "使用多张店铺优惠券编码")
        private List<String> storeCouponCodeList;
        @ApiModelProperty(value = "给商家留言")
        private String remark;
    }

    @Data
    public static class OrderProductInfo implements Serializable {
        private static final long serialVersionUID = 79094261106390951L;
        @ApiModelProperty(value = "货品Id", required = true)
        private Long productId;
        @ApiModelProperty(value = "购买数量")
        private Integer number;
        @ApiModelProperty(value = "地区编码")
        private String areaCode;
        @ApiModelProperty(value = "店铺id", hidden = true)
        private Long storeId;
        @ApiModelProperty(value = "金融规则编号")
        private String financeRuleCode;

    }

    @Data
    public static class MultiProductInfo implements Serializable{
        private static final long serialVersionUID = -4095568075719514574L;

        @ApiModelProperty(value = "货品Id", required = true)
        private Long productId;
        @ApiModelProperty(value = "购买数量")
        private Integer number;

    }
}
