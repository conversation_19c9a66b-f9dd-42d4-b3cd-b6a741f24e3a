<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.cfpamf.mall</groupId>
		<artifactId>mall-order</artifactId>
		<version>1.0.0-SNAPSHOT</version>
	</parent>
	<artifactId>mall-order-biz</artifactId>
	<packaging>jar</packaging>
	<name>mall-order-biz</name>

	<properties>
		<java.version>1.8</java.version>
		<powermock.version>2.0.5</powermock.version>
	</properties>

    <dependencies>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>spring-boot-starter-ahas-sentinel-client</artifactId>
            <version>1.9.6</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.ms.framework</groupId>
            <artifactId>cfpamf-framework-web-starter</artifactId>
            <exclusions>
            	<exclusion>
            		<groupId>org.springframework.boot</groupId>
            		<artifactId>spring-boot-devtools</artifactId>
            	</exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.ms.framework</groupId>
            <artifactId>cfpamf-framework-mybatisplus-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.ms.framework</groupId>
            <artifactId>cfpamf-framework-redis-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.ms.framework</groupId>
            <artifactId>cfpamf-framework-job-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cdfinance</groupId>
            <artifactId>hrms-facade</artifactId>
            <version>1.0.5-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.mall</groupId>
            <artifactId>mall-core</artifactId>
            <version>1.1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml-schemas</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                	<groupId>org.apache.poi</groupId>
                	<artifactId>poi-ooxml</artifactId>
                </exclusion>
                <exclusion>
                	<groupId>org.apache.poi</groupId>
                	<artifactId>poi</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.ms.framework</groupId>
            <artifactId>cfpamf-framework-jdbc-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.ms.framework</groupId>
            <artifactId>cfpamf-framework-excle-starter</artifactId>
            <exclusions>
            	<exclusion>
            		<groupId>com.alibaba</groupId>
            		<artifactId>easyexcel</artifactId>
            	</exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.13</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>html2pdf</artifactId>
            <version>2.1.4</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf.tool</groupId>
            <artifactId>xmlworker</artifactId>
            <version>5.5.8</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.3.0</version>
        </dependency>
        <!--spring rabbitMQ -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <!-- plus代码生成器 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>3.4.0</version>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.29</version>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.mall</groupId>
            <artifactId>mall-account-facade</artifactId>
            <version>1.0.20-SNAPSHOT</version>
        </dependency>
        <!-- fastJson -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <!-- 糊涂工具类-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.5.1</version>
        </dependency>
        <!-- okhttp -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>3.8.1</version>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.ms</groupId>
            <artifactId>core-trade-service-facade</artifactId>
            <version>1.0.35-SNAPSHOT</version>
        </dependency>
        <!-- powermock单测 -->
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <!--   注册中心     -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!--seata-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-seata</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>seata-all</artifactId>
                    <groupId>io.seata</groupId>
                </exclusion>
                <!--排除依赖seata-spring-boot-starter 不然或报错 SeataDataSourceBeanPostProcessor异常-->
                <exclusion>
                    <groupId>io.seata</groupId>
                    <artifactId>seata-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.seata</groupId>
            <artifactId>seata-all</artifactId>
            <version>1.4.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.seata</groupId>
            <artifactId>seata-spring-boot-starter</artifactId>
            <version>1.4.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>seata-all</artifactId>
                    <groupId>io.seata</groupId>
                </exclusion>
            </exclusions>
        </dependency>

		<!-- 自定义 mall mq配置 -->
		<dependency>
			<groupId>com.cfpamf.mall</groupId>
			<artifactId>mq-mall-starter</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>

        <dependency>
            <groupId>com.cdfinance.ms</groupId>
            <artifactId>ms-contract-service-facade</artifactId>
            <version>1.0.20240927.01-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
            <version>2.0.7</version>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.smartid</groupId>
            <artifactId>smartid-client</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.cdfinance.gray</groupId>
            <artifactId>nacos-gray-router-starter</artifactId>
            <version>1.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.dingtalk.open</groupId>
            <artifactId>taobao-sdk-java-auto</artifactId>
            <version>1.0.0.20190630</version>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.mall</groupId>
            <artifactId>mall-order-facade</artifactId>
            <version>20250620-SNAPSHOT</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>smartid-client</artifactId>
                    <groupId>com.cfpamf.smartid</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.cfpamf.ms.mallgoods</groupId>
	        <artifactId>mall-goods-facade</artifactId>
	        <version>202502260952-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.cfpamf.ms</groupId>
            <artifactId>ms-loanservice-facade</artifactId>
            <version>1.0.0.2024072417.07-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.ms</groupId>
            <artifactId>bizconfigservice-facade</artifactId>
            <version>1.0.0.20210621.01-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.cdfinance.ms</groupId>
            <artifactId>card-service-facade</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.ms</groupId>
            <artifactId>bms-service-facade</artifactId>
            <version>1.0.1.20211013-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.ms.mallmember</groupId>
            <artifactId>mall-member-facade</artifactId>
            <version>1.0.6-1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.cfpamf.ms.mallshop</groupId>
            <artifactId>mall-shop-facade</artifactId>
            <version>20250527-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.cfpamf.ms.mallpromotion</groupId>
            <artifactId>mall-promotion-facade</artifactId>
            <version>1.0.33-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.cfpamf.ms</groupId>
            <artifactId>promotion-facade</artifactId>
            <version>1.18.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.cfpamf.ms.mallsystem</groupId>
            <artifactId>mall-system-facade</artifactId>
            <version>1.0.32-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.cfpamf</groupId>
            <artifactId>mall-payment-client</artifactId>
            <version>1.1.11-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.ms</groupId>
            <artifactId>custservice-facade</artifactId>
            <version>2.8.8-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.xuxueli</groupId>
                    <artifactId>xxl-job-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-codec-http</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-transport-native-epoll</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.cfpamf.mall</groupId>
            <artifactId>mall-settlement-facade</artifactId>
            <version>1.0.19-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.cdfinance</groupId>
            <artifactId>mall-facade</artifactId>
            <version>1.0.20-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.cfpamf</groupId>
            <artifactId>dts-facade</artifactId>
            <version>1.0.7-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.cfpamf.mall</groupId>
            <artifactId>mall-logistic-facade</artifactId>
            <version>1.0.02-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.75_noneautotype</version>
        </dependency>

        <dependency>
            <groupId>com.github.jsonzou</groupId>
            <artifactId>jmockdata</artifactId>
            <version>4.3.0</version>
            <scope>test</scope>
        </dependency>
		<dependency>
		  <groupId>com.cfpamf.ms</groupId>
		  <artifactId>mall-file-center-facade</artifactId>
		  <version>20250313-SNAPSHOT</version>
        </dependency>
        <dependency>
		  <groupId>com.cdfinance.inf</groupId>
		  <artifactId>nacos-elegant-offline-spring-boot-starter</artifactId>
		  <version>0.0.1-SNAPSHOT</version>
		</dependency>

        <dependency>
            <groupId>com.cfpamf.ms.mallstock</groupId>
            <artifactId>mall-stock-facade</artifactId>
            <version>1.0.8-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>runtime</scope>
        </dependency>


        <dependency>
            <groupId>com.cfpamf.ms</groupId>
            <artifactId>messagepush-service-facade</artifactId>
            <version>1.2.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.cfpamf.common</groupId>
                    <artifactId>ms-common</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>ms-common</artifactId>
                    <groupId>com.cfpamf</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.cfpamf.inf</groupId>
            <artifactId>ares-trade-facade</artifactId>
            <version>20250512-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.cfpamf.inf</groupId>
            <artifactId>ares-after-sale-facade</artifactId>
            <version>0.0.4-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.cdfinance.cipherer</groupId>
            <artifactId>cipherer-spring-boot-starter</artifactId>
            <version>1.1.0</version>
        </dependency>

        <!-- depoove poi -->
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.9.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>poi</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml-schemas</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>



    <dependencyManagement>
        <dependencies>
            <dependency>
	            <groupId>com.alibaba</groupId>
	            <artifactId>easyexcel</artifactId>
	            <version>3.0.5</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>2.2.1.RELEASE</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

	<!-- yml配置指向 -->
	<profiles>
        <profile>
            <id>local</id>
            <activation>
				<activeByDefault>true</activeByDefault>
			</activation>
            <properties>
            	<nacos.config.enable>false</nacos.config.enable>
        		<package.environment>local</package.environment>
        		<config-server>mse-b81bd222-nacos-ans.mse.aliyuncs.com</config-server>
        		<config-namespace>6a137026-68f4-4887-9ac6-4084187e9a98</config-namespace>
        		<password></password>
        		<username></username>
      		</properties>
        </profile>
		<profile>
			<id>dev</id>
			<properties>
				<nacos.config.enable>true</nacos.config.enable>
				<package.environment>dev</package.environment>
				<config-server>mse-b01f8ee2-nacos-ans.mse.aliyuncs.com</config-server>
				<config-namespace>d53f3903-f759-4b15-b202-3ab1ee7c36e7</config-namespace>
				<password></password>
				<username></username>
			</properties>
		</profile>
		<profile>
      		<id>test</id>
      		<properties>
      		    <nacos.config.enable>true</nacos.config.enable>
        		<package.environment>test</package.environment>
        		<config-server>mse-b81bd222-nacos-ans.mse.aliyuncs.com</config-server>
        		<config-namespace>6a137026-68f4-4887-9ac6-4084187e9a98</config-namespace>
        		<password></password>
        		<username></username>
      		</properties>
 		</profile>
        <profile>
            <id>uat</id>
            <properties>
                <nacos.config.enable>true</nacos.config.enable>
                <package.environment>uat</package.environment>
                <config-server>http://nacoco.usg.cfpamf.com</config-server>
                <config-namespace>e62c68b6-7ec3-4176-a222-8e3a4a0bae5b</config-namespace>
                <password>nacos</password>
                <username>nacos</username>
            </properties>
        </profile>
		<profile>
			<id>staging</id>
			<properties>
			    <nacos.config.enable>true</nacos.config.enable>
				<package.environment>staging</package.environment>
		        <config-server>mse-d67aed82-nacos-ans.mse.aliyuncs.com</config-server>
		        <config-namespace>04c41245-14cc-4546-b742-3b08b5d1879a</config-namespace>
		        <password></password>
		        <username></username>
			</properties>
		</profile>
		<profile>
			<id>prod</id>
			 <properties>
			    <nacos.config.enable>true</nacos.config.enable>
		        <package.environment>prod</package.environment>
                 <config-server>mse-c04b0392-nacos-ans.mse.aliyuncs.com</config-server>
		        <config-namespace>e819c0f6-d14f-4899-959b-ea1d25be6579</config-namespace>
		        <password></password>
		        <username></username>
		      </properties>
		</profile>
	</profiles>

	<build>
		<finalName>mall-order-biz</finalName>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
            </plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.2.RELEASE</version>
				<configuration>
					<fork>true</fork>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<!-- jacoco版本不应该低于0.8.2 -->
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>0.8.7</version>
				<!-- 配置代码覆盖率排除项(这里建议仅去除POJO相关内容),如果要排除指定类,后缀应该是.class -->
				<configuration>
					<excludes>
						<exclude>**/po/**</exclude>
						<exclude>**/vo/**</exclude>
						<exclude>**/dto/**</exclude>
					</excludes>
				</configuration>
				<!-- 默认执行 prepare-agent -->
				<executions>
					<execution>
						<id>prepare-agent</id>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<!-- 绑定 test 后生成聚合报告 -->
					<execution>
						<id>report</id>
						<phase>test</phase>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>docx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pdf</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
		</plugins>
		<resources>
            <!-- maven打包配置 -->
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>font/</exclude>
                    <exclude>word/</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>font/</include>
                    <include>word/</include>
                </includes>
            </resource>
			<resource>
				<directory>src/main/resources/profile/${package.environment}</directory>
			</resource>
		</resources>
	</build>
</project>
