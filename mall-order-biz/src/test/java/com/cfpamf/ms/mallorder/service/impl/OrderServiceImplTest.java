package com.cfpamf.ms.mallorder.service.impl;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdfinance.ms.card.facade.model.response.cardUse.CardTryPayResponse;
import com.cfpamf.common.ms.result.ErrorContext;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.mallorder.common.mq.msg.PromotionDiscountMsg;
import com.cfpamf.ms.mallorder.dto.OrderSubmitDTO;
import com.cfpamf.ms.mallorder.integration.pay.CardPayIntegration;
import com.cfpamf.ms.mallorder.po.OrderExtendFinancePO;
import com.cfpamf.ms.mallorder.service.ICommonMqEventService;
import com.cfpamf.ms.mallorder.service.IOrderAmountStateRecordService;
import com.cfpamf.ms.mallorder.service.IOrderExtendFinanceService;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallorder.vo.OrderPresellVO;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;

import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @Create 2021-11-02 09:45
 * @Description :
 */
public class OrderServiceImplTest {
    @Mock
    CardPayIntegration cardPayIntegration;
    @InjectMocks
    OrderServiceImpl orderServiceImpl;
    @Mock
    OrderPresellService orderPresellService;
    @Mock
    IOrderExtendFinanceService financeService;
    @Mock
    BaseMapper baseMapper;
    @Mock
    IOrderAmountStateRecordService orderAmountRecordService;
    @Mock
    ICommonMqEventService commonMqEventService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }


    @Test
    public void testProcessOrderPlanDiscount() throws Exception {
        PromotionDiscountMsg promotionDiscountMsg = new PromotionDiscountMsg();
        promotionDiscountMsg.setBranchCode("");
        promotionDiscountMsg.setCustomerCode("");
        promotionDiscountMsg.setCustomerName("");
        promotionDiscountMsg.setCustomerIdNo("");
        promotionDiscountMsg.setPlanDiscountAmount(new BigDecimal("0"));
        promotionDiscountMsg.setTradeNo(0L);
        promotionDiscountMsg.setLoanNo("");
        promotionDiscountMsg.setLoanOpenDate(new Date());

        OrderPresellVO orderPresellVO = new OrderPresellVO();
        orderPresellVO.setId(0L);
        orderPresellVO.setOrderSn("");
        orderPresellVO.setPaySn("");
        orderPresellVO.setPayNo("");
        orderPresellVO.setTotalAmount(new BigDecimal("0"));
        orderPresellVO.setAmount(new BigDecimal("0"));
        orderPresellVO.setDiscountAmount(new BigDecimal("0"));
        orderPresellVO.setPayAmount(new BigDecimal("0"));
        orderPresellVO.setChannelServiceFee(new BigDecimal("0"));
        orderPresellVO.setChannelServiceRate("");
        orderPresellVO.setDeadTime("");
        orderPresellVO.setType(0);
        orderPresellVO.setPaymentCode("");
        orderPresellVO.setPaymentName("");
        orderPresellVO.setPayStatus(0);
        orderPresellVO.setPayTime("");

        OrderExtendFinancePO orderExtendFinancePO = new OrderExtendFinancePO();
        orderExtendFinancePO.setFinanceId(0L);
        orderExtendFinancePO.setOrderSn("");
        orderExtendFinancePO.setDeliverMethod(0);
        orderExtendFinancePO.setAutoDeliverTime(new Date());
        orderExtendFinancePO.setAutoReceiveDays(0);
        orderExtendFinancePO.setInterestWay(0);
        orderExtendFinancePO.setPlanDiscountType(0);
        orderExtendFinancePO.setPlanInterestStartDays(0);
        orderExtendFinancePO.setPlanDiscountStoreRate(new BigDecimal("0"));
        orderExtendFinancePO.setPlanDiscountUpperLimit(new BigDecimal("0"));
        orderExtendFinancePO.setCouponBatch("");
        orderExtendFinancePO.setInterestStartType(0);
        orderExtendFinancePO.setInterestStartDays(0);
        orderExtendFinancePO.setEmployeeInterestStartDate(new Date());
        orderExtendFinancePO.setPlanLoanDate(new Date());
        orderExtendFinancePO.setFinanceRuleCode("");
        orderExtendFinancePO.setCreateBy("");
        orderExtendFinancePO.setUpdateBy("");
        orderExtendFinancePO.setCreateTime(new Date());
        orderExtendFinancePO.setUpdateTime(new Date());
        orderExtendFinancePO.setEnabledFlag(0);

        Result<Void> voidResult = new Result<>();
        voidResult.setSuccess(true);
        voidResult.setErrorContext(new ErrorContext());
        voidResult.setCode("");
        voidResult.setMessage("");


        when(orderPresellService.findByPayNo(any())).thenReturn(orderPresellVO);
        Mockito.when(baseMapper.selectOne(Mockito.any())).thenReturn(orderExtendFinancePO);
        // Mockito.when(orderAmountRecordService.modifyOrderAmountState(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(voidResult);
        Mockito.when(commonMqEventService.saveEvent(Mockito.any(), Mockito.any())).thenReturn(1L);

        try {
            orderServiceImpl.processOrderPlanDiscount(promotionDiscountMsg);
        } catch (Exception e) {
        }
    }

    @Test
    public void testCalculateCard() throws Exception {
        /**
         * 乡助卡  60
         *
         * 店铺1：总  10  运费 8
         *        商品1 8
         *        商品2 2
         * 店铺2：总  80  运费0
         *        商品1 70
         *        商品2 10
         */

        //店铺1
        OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo = new OrderSubmitDTO.OrderInfo.OrderProductInfo();
        orderProductInfo.setMoneyAmount(new BigDecimal("8"));
        orderProductInfo.setProductPrice(new BigDecimal("8"));
        orderProductInfo.setBuyNum(1);
        OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo2 = new OrderSubmitDTO.OrderInfo.OrderProductInfo();
        orderProductInfo2.setMoneyAmount(new BigDecimal("2"));
        orderProductInfo2.setProductPrice(new BigDecimal("2"));
        orderProductInfo2.setBuyNum(1);


        OrderSubmitDTO.OrderInfo orderInfo = new OrderSubmitDTO.OrderInfo();
        orderInfo.setOrderProductInfoList(Lists.newArrayList(orderProductInfo,orderProductInfo2));
        orderInfo.setTotalAmount(new BigDecimal("10"));
        orderInfo.setExpressFee(new BigDecimal("8"));

        //店铺2
        OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo3 = new OrderSubmitDTO.OrderInfo.OrderProductInfo();
        orderProductInfo3.setMoneyAmount(new BigDecimal("70"));
        orderProductInfo3.setProductPrice(new BigDecimal("70"));
        orderProductInfo3.setBuyNum(1);
        OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo4 = new OrderSubmitDTO.OrderInfo.OrderProductInfo();
        orderProductInfo4.setMoneyAmount(new BigDecimal("10"));
        orderProductInfo4.setProductPrice(new BigDecimal("10"));
        orderProductInfo4.setBuyNum(1);


        OrderSubmitDTO.OrderInfo orderInfo2 = new OrderSubmitDTO.OrderInfo();
        orderInfo2.setOrderProductInfoList(Lists.newArrayList(orderProductInfo3,orderProductInfo4));
        orderInfo2.setTotalAmount(new BigDecimal("80"));
        orderInfo2.setExpressFee(new BigDecimal("0"));


        OrderSubmitDTO orderSubmitDTO = new OrderSubmitDTO();
        orderSubmitDTO.setOrderInfoList(Lists.newArrayList(orderInfo,orderInfo2));
        orderSubmitDTO.setTotalAmount(new BigDecimal("90"));
        orderSubmitDTO.setExpressFee(new BigDecimal("8"));

        CardTryPayResponse cardTryPayResponse = new CardTryPayResponse();
        cardTryPayResponse.setCardPayAmount(new BigDecimal("60"));


        when(cardPayIntegration.tryPay(any(), anyString())).thenReturn(cardTryPayResponse);

        OrderSubmitDTO result = orderServiceImpl.calculateCard(orderSubmitDTO, Arrays.asList("test", "test2"), "ZHNXtest");
    }

}
