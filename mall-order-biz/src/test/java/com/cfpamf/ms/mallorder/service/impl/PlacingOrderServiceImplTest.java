package com.cfpamf.ms.mallorder.service.impl;

import com.cfpamf.framework.autoconfigure.redis.lock.SlodonLock;
import com.cfpamf.ms.mallgoods.facade.api.GoodsFeignClient;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.common.util.OrderSubmitUtil;
import com.cfpamf.ms.mallorder.common.util.PromotionUtils;
import com.cfpamf.ms.mallorder.common.util.ValidUtils;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.enums.OrderPlaceUserRole;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.integration.pay.PayIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.model.CartModel;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.po.Cart;
import com.cfpamf.ms.mallorder.po.CartPO;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallpromotion.api.PromotionCommonFeignClient;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.smartid.client.utils.sharding.SeqEnum;
import com.cfpamf.smartid.client.utils.sharding.ShardingId;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.mock.web.MockHttpServletRequest;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PlacingOrderServiceImplTest {

    @Mock
    private MockHttpServletRequest mockRequest;
    @Mock
    private OrderSubmitUtil mockOrderSubmitUtil;
    @Mock
    private MemberFeignClient mockMemberFeignClient;
    @Mock
    private GoodsFeignClient mockGoodsFeignClient;
    @Mock
    private StoreFeignClient mockStoreFeignClient;
    @Mock
    private OrderModel mockOrderModel;
    @Mock
    private SlodonLock mockSlodonLock;
    @Mock
    private StringRedisTemplate mockStringRedisTemplate;
    @Mock
    private PromotionCommonFeignClient mockPromotionCommonFeignClient;
    @Mock
    private ITaskQueueService mockTaskQueueService;
    @Mock
    private PayIntegration mockPayIntegration;
    @Mock
    private OrderMapper mockOrderMapper;
    @Mock
    private ICartService mockCartService;
    @Mock
    private ShardingId mockShardingId;
    @Mock
    private IOrderService mockIOrderService;
    @Mock
    private IPayMethodService mockPayMethodService;
    @Mock
    private OrderLocalUtils mockOrderLocalUtils;
    @Mock
    private PromotionUtils mockPromotionUtils;
    @Mock
    private CartModel mockCartModel;
    @Mock
    private ValidUtils mockValidUtils;
    @Mock
    private OrderCreateHelper mockOrderCreateHelper;
    @Mock
    private EmployeeService mockEmployeeService;
    @Mock
    private OrderOfflineService mockOrderOfflineService;

    @InjectMocks
    private PlacingOrderServiceImpl placingOrderServiceImplUnderTest;

    @Test
    public void testSubmitChannelOrder() throws Exception {
        // Setup
        final ChannelOrderSubmitDTO dto = new ChannelOrderSubmitDTO();
        dto.setOrderType(OrderTypeEnum.NORMAL);
        dto.setChannel("channel");
        dto.setUserNo("userNo");
        dto.setOutBizSource("outBizSource");
        dto.setOutBizId("outBizId");

//        when(mockShardingId.next(SeqEnum.PNO, "userId")).thenReturn(0L);

        // Run the test
        final ChannelOrderSubmitVO result = placingOrderServiceImplUnderTest.submitChannelOrder(dto);

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testCreateOrder() throws Exception {
        // Setup
        final OrderSubmitMqConsumerDTO req = new OrderSubmitMqConsumerDTO();
        final OrderSubmitParamDTO paramDTO = new OrderSubmitParamDTO();
        paramDTO.setChannel("channel");
        paramDTO.setSource(0);
        paramDTO.setAddressId(0);
        paramDTO.setAreaCode("areaCode");
        paramDTO.setProductType(0);
        paramDTO.setIsAloneBuy(false);
        paramDTO.setOrderPattern(0);
        paramDTO.setProductId(0L);
        paramDTO.setNumber(0);
        paramDTO.setPno("pno");
        paramDTO.setVerifyCode("verifyCode");
        paramDTO.setCardCodeList(Arrays.asList("value"));
        final OrderAddressDTO orderAddress = new OrderAddressDTO();
        orderAddress.setReceiverName("receiverName");
        orderAddress.setReceiverMobile("receiverMobile");
        orderAddress.setProvince("province");
        orderAddress.setCity("city");
        orderAddress.setCityCode("cityCode");
        orderAddress.setDistrict("district");
        orderAddress.setTown("town");
        orderAddress.setDetailAddress("detailAddress");
        paramDTO.setOrderAddress(orderAddress);
        final OrderAddressDTO purchaseOrderAddress = new OrderAddressDTO();
        purchaseOrderAddress.setReceiverName("receiverName");
        purchaseOrderAddress.setReceiverMobile("receiverMobile");
        purchaseOrderAddress.setProvince("province");
        purchaseOrderAddress.setCity("city");
        purchaseOrderAddress.setCityCode("cityCode");
        purchaseOrderAddress.setDistrict("district");
        purchaseOrderAddress.setTown("town");
        purchaseOrderAddress.setDetailAddress("detailAddress");
        paramDTO.setPurchaseOrderAddress(purchaseOrderAddress);
        paramDTO.setChannelOrder(false);
        req.setParamDTO(paramDTO);
        req.setMemberId(0);
        req.setUserNo("userNo");
        req.setPaySn("paySn");
        final PreOrderDTO preOrderDTO = new PreOrderDTO();
        preOrderDTO.setIsCalculateDiscount(false);
        preOrderDTO.setOrderType(0);
        req.setPreOrderDTO(preOrderDTO);
        req.setAreaCode("areaCode");
        final OrderSkuInfoDTO skuInfoDTO = new OrderSkuInfoDTO();
        req.setSkuInfoList(Arrays.asList(skuInfoDTO));
        req.setOrderType(OrderTypeEnum.NORMAL);
        final ChannelOrderSubmitDTO channelOrderSubmitDTO = new ChannelOrderSubmitDTO();
        channelOrderSubmitDTO.setChannel("channel");
        channelOrderSubmitDTO.setOutBizSource("outBizSource");
        channelOrderSubmitDTO.setOutBizId("outBizId");
        req.setChannelOrderSubmitDTO(channelOrderSubmitDTO);
        req.setSimulationShoppingCart(false);
        req.setFinanceRuleCode("financeRuleCode");

        // Configure ICartService.buildCartList(...).
        final CartPO cartPO = new CartPO();
        cartPO.setCartId(0);
        cartPO.setMemberId(0);
        cartPO.setStoreId(0L);
        cartPO.setStoreName("storeName");
        cartPO.setGoodsId(0L);
        final List<CartPO> cartPOList = Arrays.asList(cartPO);
        final OrderSkuInfoDTO skuInfoDTO1 = new OrderSkuInfoDTO();
        skuInfoDTO1.setProductId(0L);
        skuInfoDTO1.setNumber(0);
        skuInfoDTO1.setProductPrice(new BigDecimal("0.00"));
        skuInfoDTO1.setLandingPrice(new BigDecimal("0.00"));
        skuInfoDTO1.setProductDiscountAmount(new BigDecimal("0.00"));
        final List<OrderSkuInfoDTO> skuInfoList = Arrays.asList(skuInfoDTO1);
        final OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName("receiverName");
        addressDTO.setReceiverMobile("receiverMobile");
        addressDTO.setProvince("province");
        addressDTO.setCity("city");
        addressDTO.setCityCode("cityCode");
        addressDTO.setDistrict("district");
        addressDTO.setTown("town");
        addressDTO.setDetailAddress("detailAddress");
//        when(mockCartService.buildCartList(skuInfoList, OrderTypeEnum.NORMAL, 0, "areaCode", "financeRuleCode",
//                "channel", addressDTO)).thenReturn(cartPOList);

        // Configure OrderSubmitUtil.getOrderSubmitDTOV2(...).

        final OrderSubmitParamDTO submitParamDTO = new OrderSubmitParamDTO();
        submitParamDTO.setChannel("channel");
        submitParamDTO.setSource(0);
        submitParamDTO.setAddressId(0);
        submitParamDTO.setAreaCode("areaCode");
        submitParamDTO.setProductType(0);
        submitParamDTO.setIsAloneBuy(false);
        submitParamDTO.setOrderPattern(0);
        submitParamDTO.setProductId(0L);
        submitParamDTO.setNumber(0);
        submitParamDTO.setPno("pno");
        submitParamDTO.setVerifyCode("verifyCode");
        submitParamDTO.setCardCodeList(Arrays.asList("value"));
        final OrderAddressDTO orderAddress1 = new OrderAddressDTO();
        orderAddress1.setReceiverName("receiverName");
        orderAddress1.setReceiverMobile("receiverMobile");
        orderAddress1.setProvince("province");
        orderAddress1.setCity("city");
        orderAddress1.setCityCode("cityCode");
        orderAddress1.setDistrict("district");
        orderAddress1.setTown("town");
        orderAddress1.setDetailAddress("detailAddress");
        submitParamDTO.setOrderAddress(orderAddress1);
        final OrderAddressDTO purchaseOrderAddress1 = new OrderAddressDTO();
        purchaseOrderAddress1.setReceiverName("receiverName");
        purchaseOrderAddress1.setReceiverMobile("receiverMobile");
        purchaseOrderAddress1.setProvince("province");
        purchaseOrderAddress1.setCity("city");
        purchaseOrderAddress1.setCityCode("cityCode");
        purchaseOrderAddress1.setDistrict("district");
        purchaseOrderAddress1.setTown("town");
        purchaseOrderAddress1.setDetailAddress("detailAddress");
        submitParamDTO.setPurchaseOrderAddress(purchaseOrderAddress1);
        submitParamDTO.setChannelOrder(false);
        final Cart cart = new Cart();
        cart.setStoreId(1L);
        cart.setIsVirtualGoods(1);
        cart.setCartId(0);
        cart.setMemberId(0);
        cart.setGoodsId(0L);
        cart.setGoodsName("goodsName");
        cart.setFundsBorrowable(0);
        cart.setProductPrice(BigDecimal.ONE);
        cart.setBuyNum(10);
        final OrderSubmitDTO orderSubmitDTO = new OrderSubmitDTO(Arrays.asList(cart), submitParamDTO);
        final CartPO cartPO1 = new CartPO();
        cartPO1.setCartId(0);
        cartPO1.setMemberId(0);
        cartPO1.setStoreId(0L);
        cartPO1.setStoreName("storeName");
        cartPO1.setGoodsId(0L);
        final List<CartPO> cartPOList1 = Arrays.asList(cartPO1);
        final OrderSubmitParamDTO dto = new OrderSubmitParamDTO();
        dto.setChannel("channel");
        dto.setSource(0);
        dto.setAddressId(0);
        dto.setAreaCode("areaCode");
        dto.setProductType(0);
        dto.setIsAloneBuy(false);
        dto.setOrderPattern(0);
        dto.setProductId(0L);
        dto.setNumber(0);
        dto.setPno("pno");
        dto.setVerifyCode("verifyCode");
        dto.setCardCodeList(Arrays.asList("value"));
        final OrderAddressDTO orderAddress2 = new OrderAddressDTO();
        orderAddress2.setReceiverName("receiverName");
        orderAddress2.setReceiverMobile("receiverMobile");
        orderAddress2.setProvince("province");
        orderAddress2.setCity("city");
        orderAddress2.setCityCode("cityCode");
        orderAddress2.setDistrict("district");
        orderAddress2.setTown("town");
        orderAddress2.setDetailAddress("detailAddress");
        dto.setOrderAddress(orderAddress2);
        final OrderAddressDTO purchaseOrderAddress2 = new OrderAddressDTO();
        purchaseOrderAddress2.setReceiverName("receiverName");
        purchaseOrderAddress2.setReceiverMobile("receiverMobile");
        purchaseOrderAddress2.setProvince("province");
        purchaseOrderAddress2.setCity("city");
        purchaseOrderAddress2.setCityCode("cityCode");
        purchaseOrderAddress2.setDistrict("district");
        purchaseOrderAddress2.setTown("town");
        purchaseOrderAddress2.setDetailAddress("detailAddress");
        dto.setPurchaseOrderAddress(purchaseOrderAddress2);
        dto.setChannelOrder(false);
//        when(mockOrderSubmitUtil.getOrderSubmitDTOV2(cartPOList1, dto, 0, false)).thenReturn(orderSubmitDTO);

        // Configure OrderSubmitUtil.getOrderSubmitDTO(...).
        final Cart cart1 = new Cart();
        cart1.setStoreId(1L);
        cart1.setIsVirtualGoods(1);
        cart1.setCartId(0);
        cart1.setMemberId(0);
        cart1.setGoodsId(0L);
        cart1.setGoodsName("goodsName");
        cart1.setFundsBorrowable(0);
        cart1.setProductPrice(BigDecimal.ONE);
        cart1.setBuyNum(10);
        final OrderSubmitParamDTO submitParamDTO1 = new OrderSubmitParamDTO();
        submitParamDTO1.setChannel("channel");
        submitParamDTO1.setSource(0);
        submitParamDTO1.setAddressId(0);
        submitParamDTO1.setAreaCode("areaCode");
        submitParamDTO1.setProductType(0);
        submitParamDTO1.setIsAloneBuy(false);
        submitParamDTO1.setOrderPattern(0);
        submitParamDTO1.setProductId(0L);
        submitParamDTO1.setNumber(0);
        submitParamDTO1.setPno("pno");
        submitParamDTO1.setVerifyCode("verifyCode");
        submitParamDTO1.setCardCodeList(Arrays.asList("value"));
        final OrderAddressDTO orderAddress3 = new OrderAddressDTO();
        orderAddress3.setReceiverName("receiverName");
        orderAddress3.setReceiverMobile("receiverMobile");
        orderAddress3.setProvince("province");
        orderAddress3.setCity("city");
        orderAddress3.setCityCode("cityCode");
        orderAddress3.setDistrict("district");
        orderAddress3.setTown("town");
        orderAddress3.setDetailAddress("detailAddress");
        submitParamDTO1.setOrderAddress(orderAddress3);
        final OrderAddressDTO purchaseOrderAddress3 = new OrderAddressDTO();
        purchaseOrderAddress3.setReceiverName("receiverName");
        purchaseOrderAddress3.setReceiverMobile("receiverMobile");
        purchaseOrderAddress3.setProvince("province");
        purchaseOrderAddress3.setCity("city");
        purchaseOrderAddress3.setCityCode("cityCode");
        purchaseOrderAddress3.setDistrict("district");
        purchaseOrderAddress3.setTown("town");
        purchaseOrderAddress3.setDetailAddress("detailAddress");
        submitParamDTO1.setPurchaseOrderAddress(purchaseOrderAddress3);
        submitParamDTO1.setChannelOrder(false);
        final OrderSubmitDTO orderSubmitDTO1 = new OrderSubmitDTO(Arrays.asList(cart1), submitParamDTO1);
        final OrderSubmitParamDTO dto1 = new OrderSubmitParamDTO();
        dto1.setChannel("channel");
        dto1.setSource(0);
        dto1.setAddressId(0);
        dto1.setAreaCode("areaCode");
        dto1.setProductType(0);
        dto1.setIsAloneBuy(false);
        dto1.setOrderPattern(0);
        dto1.setProductId(0L);
        dto1.setNumber(0);
        dto1.setPno("pno");
        dto1.setVerifyCode("verifyCode");
        dto1.setCardCodeList(Arrays.asList("value"));
        final OrderAddressDTO orderAddress4 = new OrderAddressDTO();
        orderAddress4.setReceiverName("receiverName");
        orderAddress4.setReceiverMobile("receiverMobile");
        orderAddress4.setProvince("province");
        orderAddress4.setCity("city");
        orderAddress4.setCityCode("cityCode");
        orderAddress4.setDistrict("district");
        orderAddress4.setTown("town");
        orderAddress4.setDetailAddress("detailAddress");
        dto1.setOrderAddress(orderAddress4);
        final OrderAddressDTO purchaseOrderAddress4 = new OrderAddressDTO();
        purchaseOrderAddress4.setReceiverName("receiverName");
        purchaseOrderAddress4.setReceiverMobile("receiverMobile");
        purchaseOrderAddress4.setProvince("province");
        purchaseOrderAddress4.setCity("city");
        purchaseOrderAddress4.setCityCode("cityCode");
        purchaseOrderAddress4.setDistrict("district");
        purchaseOrderAddress4.setTown("town");
        purchaseOrderAddress4.setDetailAddress("detailAddress");
        dto1.setPurchaseOrderAddress(purchaseOrderAddress4);
        dto1.setChannelOrder(false);
        final OrderAddressDTO addressDTO1 = new OrderAddressDTO();
        addressDTO1.setReceiverName("receiverName");
        addressDTO1.setReceiverMobile("receiverMobile");
        addressDTO1.setProvince("province");
        addressDTO1.setCity("city");
        addressDTO1.setCityCode("cityCode");
        addressDTO1.setDistrict("district");
        addressDTO1.setTown("town");
        addressDTO1.setDetailAddress("detailAddress");
//        when(mockOrderSubmitUtil.getOrderSubmitDTO(dto1, 0, 0, addressDTO1, true, true)).thenReturn(orderSubmitDTO1);

        // Configure PromotionCommonFeignClient.orderSubmitCalculationDiscountV2(...).
        final Cart cart2 = new Cart();
        cart2.setStoreId(1L);
        cart2.setIsVirtualGoods(1);
        cart2.setCartId(0);
        cart2.setMemberId(0);
        cart2.setGoodsId(0L);
        cart2.setGoodsName("goodsName");
        cart2.setFundsBorrowable(0);
        cart2.setProductPrice(BigDecimal.ONE);
        cart2.setBuyNum(10);
        final OrderSubmitParamDTO submitParamDTO2 = new OrderSubmitParamDTO();
        submitParamDTO2.setChannel("channel");
        submitParamDTO2.setSource(0);
        submitParamDTO2.setAddressId(0);
        submitParamDTO2.setAreaCode("areaCode");
        submitParamDTO2.setProductType(0);
        submitParamDTO2.setIsAloneBuy(false);
        submitParamDTO2.setOrderPattern(0);
        submitParamDTO2.setProductId(0L);
        submitParamDTO2.setNumber(0);
        submitParamDTO2.setPno("pno");
        submitParamDTO2.setVerifyCode("verifyCode");
        submitParamDTO2.setCardCodeList(Arrays.asList("value"));
        final OrderAddressDTO orderAddress5 = new OrderAddressDTO();
        orderAddress5.setReceiverName("receiverName");
        orderAddress5.setReceiverMobile("receiverMobile");
        orderAddress5.setProvince("province");
        orderAddress5.setCity("city");
        orderAddress5.setCityCode("cityCode");
        orderAddress5.setDistrict("district");
        orderAddress5.setTown("town");
        orderAddress5.setDetailAddress("detailAddress");
        submitParamDTO2.setOrderAddress(orderAddress5);
        final OrderAddressDTO purchaseOrderAddress5 = new OrderAddressDTO();
        purchaseOrderAddress5.setReceiverName("receiverName");
        purchaseOrderAddress5.setReceiverMobile("receiverMobile");
        purchaseOrderAddress5.setProvince("province");
        purchaseOrderAddress5.setCity("city");
        purchaseOrderAddress5.setCityCode("cityCode");
        purchaseOrderAddress5.setDistrict("district");
        purchaseOrderAddress5.setTown("town");
        purchaseOrderAddress5.setDetailAddress("detailAddress");
        submitParamDTO2.setPurchaseOrderAddress(purchaseOrderAddress5);
        submitParamDTO2.setChannelOrder(false);
        final OrderSubmitDTO orderSubmitDTO2 = new OrderSubmitDTO(Arrays.asList(cart2), submitParamDTO2);
//        when(mockPromotionCommonFeignClient.orderSubmitCalculationDiscountV2(orderSubmitDTO2, 0))
//                .thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderLocalUtils.getOrderExpressFee(...).
        final Cart cart3 = new Cart();
        cart3.setStoreId(1L);
        cart3.setIsVirtualGoods(1);
        cart3.setCartId(0);
        cart3.setMemberId(0);
        cart3.setGoodsId(0L);
        cart3.setGoodsName("goodsName");
        cart3.setFundsBorrowable(0);
        cart3.setProductPrice(BigDecimal.ONE);
        cart3.setBuyNum(10);
        final OrderSubmitParamDTO submitParamDTO3 = new OrderSubmitParamDTO();
        submitParamDTO3.setChannel("channel");
        submitParamDTO3.setSource(0);
        submitParamDTO3.setAddressId(0);
        submitParamDTO3.setAreaCode("areaCode");
        submitParamDTO3.setProductType(0);
        submitParamDTO3.setIsAloneBuy(false);
        submitParamDTO3.setOrderPattern(0);
        submitParamDTO3.setProductId(0L);
        submitParamDTO3.setNumber(0);
        submitParamDTO3.setPno("pno");
        submitParamDTO3.setVerifyCode("verifyCode");
        submitParamDTO3.setCardCodeList(Arrays.asList("value"));
        final OrderAddressDTO orderAddress6 = new OrderAddressDTO();
        orderAddress6.setReceiverName("receiverName");
        orderAddress6.setReceiverMobile("receiverMobile");
        orderAddress6.setProvince("province");
        orderAddress6.setCity("city");
        orderAddress6.setCityCode("cityCode");
        orderAddress6.setDistrict("district");
        orderAddress6.setTown("town");
        orderAddress6.setDetailAddress("detailAddress");
        submitParamDTO3.setOrderAddress(orderAddress6);
        final OrderAddressDTO purchaseOrderAddress6 = new OrderAddressDTO();
        purchaseOrderAddress6.setReceiverName("receiverName");
        purchaseOrderAddress6.setReceiverMobile("receiverMobile");
        purchaseOrderAddress6.setProvince("province");
        purchaseOrderAddress6.setCity("city");
        purchaseOrderAddress6.setCityCode("cityCode");
        purchaseOrderAddress6.setDistrict("district");
        purchaseOrderAddress6.setTown("town");
        purchaseOrderAddress6.setDetailAddress("detailAddress");
        submitParamDTO3.setPurchaseOrderAddress(purchaseOrderAddress6);
        submitParamDTO3.setChannelOrder(false);
        final OrderSubmitDTO.OrderInfo orderInfo = new OrderSubmitDTO.OrderInfo(Arrays.asList(cart3), submitParamDTO3);
//        when(mockOrderLocalUtils.getOrderExpressFee("cityCode", orderInfo)).thenReturn(new BigDecimal("0.00"));

        // Configure MemberFeignClient.getMemberByMemberId(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setUserNo("userNo");
        member.setCustNo("custNo");
        member.setCappCustNo("cappCustNo");
        member.setMemberName("memberName");
//        when(mockMemberFeignClient.getMemberByMemberId(0)).thenReturn(member);

        // Configure OrderModel.submitOrder(...).
        final Cart cart4 = new Cart();
        cart4.setStoreId(1L);
        cart4.setIsVirtualGoods(1);
        cart4.setCartId(0);
        cart4.setMemberId(0);
        cart4.setGoodsId(0L);
        cart4.setGoodsName("goodsName");
        cart4.setFundsBorrowable(0);
        cart4.setProductPrice(BigDecimal.ONE);
        cart4.setBuyNum(10);
        final OrderSubmitParamDTO submitParamDTO4 = new OrderSubmitParamDTO();
        submitParamDTO4.setChannel("channel");
        submitParamDTO4.setSource(0);
        submitParamDTO4.setAddressId(0);
        submitParamDTO4.setAreaCode("areaCode");
        submitParamDTO4.setProductType(0);
        submitParamDTO4.setIsAloneBuy(false);
        submitParamDTO4.setOrderPattern(0);
        submitParamDTO4.setProductId(0L);
        submitParamDTO4.setNumber(0);
        submitParamDTO4.setPno("pno");
        submitParamDTO4.setVerifyCode("verifyCode");
        submitParamDTO4.setCardCodeList(Arrays.asList("value"));
        final OrderAddressDTO orderAddress7 = new OrderAddressDTO();
        orderAddress7.setReceiverName("receiverName");
        orderAddress7.setReceiverMobile("receiverMobile");
        orderAddress7.setProvince("province");
        orderAddress7.setCity("city");
        orderAddress7.setCityCode("cityCode");
        orderAddress7.setDistrict("district");
        orderAddress7.setTown("town");
        orderAddress7.setDetailAddress("detailAddress");
        submitParamDTO4.setOrderAddress(orderAddress7);
        final OrderAddressDTO purchaseOrderAddress7 = new OrderAddressDTO();
        purchaseOrderAddress7.setReceiverName("receiverName");
        purchaseOrderAddress7.setReceiverMobile("receiverMobile");
        purchaseOrderAddress7.setProvince("province");
        purchaseOrderAddress7.setCity("city");
        purchaseOrderAddress7.setCityCode("cityCode");
        purchaseOrderAddress7.setDistrict("district");
        purchaseOrderAddress7.setTown("town");
        purchaseOrderAddress7.setDetailAddress("detailAddress");
        submitParamDTO4.setPurchaseOrderAddress(purchaseOrderAddress7);
        submitParamDTO4.setChannelOrder(false);
        final OrderSubmitDTO orderSubmitDTO3 = new OrderSubmitDTO(Arrays.asList(cart4), submitParamDTO4);
        final Member member1 = new Member();
        member1.setMemberId(0);
        member1.setUserNo("userNo");
        member1.setCustNo("custNo");
        member1.setCappCustNo("cappCustNo");
        member1.setMemberName("memberName");
        final OrderSubmitMqConsumerDTO consumerDTO = new OrderSubmitMqConsumerDTO();
        final OrderSubmitParamDTO paramDTO1 = new OrderSubmitParamDTO();
        paramDTO1.setChannel("channel");
        paramDTO1.setSource(0);
        paramDTO1.setAddressId(0);
        paramDTO1.setAreaCode("areaCode");
        paramDTO1.setProductType(0);
        paramDTO1.setIsAloneBuy(false);
        paramDTO1.setOrderPattern(0);
        paramDTO1.setProductId(0L);
        paramDTO1.setNumber(0);
        paramDTO1.setPno("pno");
        paramDTO1.setVerifyCode("verifyCode");
        paramDTO1.setCardCodeList(Arrays.asList("value"));
        final OrderAddressDTO orderAddress8 = new OrderAddressDTO();
        orderAddress8.setReceiverName("receiverName");
        orderAddress8.setReceiverMobile("receiverMobile");
        orderAddress8.setProvince("province");
        orderAddress8.setCity("city");
        orderAddress8.setCityCode("cityCode");
        orderAddress8.setDistrict("district");
        orderAddress8.setTown("town");
        orderAddress8.setDetailAddress("detailAddress");
        paramDTO1.setOrderAddress(orderAddress8);
        final OrderAddressDTO purchaseOrderAddress8 = new OrderAddressDTO();
        purchaseOrderAddress8.setReceiverName("receiverName");
        purchaseOrderAddress8.setReceiverMobile("receiverMobile");
        purchaseOrderAddress8.setProvince("province");
        purchaseOrderAddress8.setCity("city");
        purchaseOrderAddress8.setCityCode("cityCode");
        purchaseOrderAddress8.setDistrict("district");
        purchaseOrderAddress8.setTown("town");
        purchaseOrderAddress8.setDetailAddress("detailAddress");
        paramDTO1.setPurchaseOrderAddress(purchaseOrderAddress8);
        paramDTO1.setChannelOrder(false);
        consumerDTO.setParamDTO(paramDTO1);
        consumerDTO.setMemberId(0);
        consumerDTO.setUserNo("userNo");
        consumerDTO.setPaySn("paySn");
        final PreOrderDTO preOrderDTO1 = new PreOrderDTO();
        preOrderDTO1.setIsCalculateDiscount(false);
        preOrderDTO1.setOrderType(0);
        consumerDTO.setPreOrderDTO(preOrderDTO1);
        consumerDTO.setAreaCode("areaCode");
        final OrderSkuInfoDTO skuInfoDTO2 = new OrderSkuInfoDTO();
        consumerDTO.setSkuInfoList(Arrays.asList(skuInfoDTO2));
        consumerDTO.setOrderType(OrderTypeEnum.NORMAL);
        final ChannelOrderSubmitDTO channelOrderSubmitDTO1 = new ChannelOrderSubmitDTO();
        channelOrderSubmitDTO1.setChannel("channel");
        channelOrderSubmitDTO1.setOutBizSource("outBizSource");
        channelOrderSubmitDTO1.setOutBizId("outBizId");
        consumerDTO.setChannelOrderSubmitDTO(channelOrderSubmitDTO1);
        consumerDTO.setSimulationShoppingCart(false);
        consumerDTO.setFinanceRuleCode("financeRuleCode");
//        when(mockIOrderService.submitOrderWithGlobleTransaction(orderSubmitDTO3, member1, consumerDTO))
//                .thenReturn(new ArrayList<>(Arrays.asList("value")));

//        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
//        final List<String> result = placingOrderServiceImplUnderTest.createOrder(req);

        // Verify the results
//        assertThat(result).isNotEmpty();

        // Confirm IOrderService.dealExpress(...).
        final Cart cart5 = new Cart();
        cart5.setStoreId(1L);
        cart5.setIsVirtualGoods(1);
        cart5.setCartId(0);
        cart5.setMemberId(0);
        cart5.setGoodsId(0L);
        cart5.setGoodsName("goodsName");
        cart5.setFundsBorrowable(0);
        cart5.setProductPrice(BigDecimal.ONE);
        cart5.setBuyNum(10);
        final OrderSubmitParamDTO submitParamDTO5 = new OrderSubmitParamDTO();
        submitParamDTO5.setChannel("channel");
        submitParamDTO5.setSource(0);
        submitParamDTO5.setAddressId(0);
        submitParamDTO5.setAreaCode("areaCode");
        submitParamDTO5.setProductType(0);
        submitParamDTO5.setIsAloneBuy(false);
        submitParamDTO5.setOrderPattern(0);
        submitParamDTO5.setProductId(0L);
        submitParamDTO5.setNumber(0);
        submitParamDTO5.setPno("pno");
        submitParamDTO5.setVerifyCode("verifyCode");
        submitParamDTO5.setCardCodeList(Arrays.asList("value"));
        final OrderAddressDTO orderAddress9 = new OrderAddressDTO();
        orderAddress9.setReceiverName("receiverName");
        orderAddress9.setReceiverMobile("receiverMobile");
        orderAddress9.setProvince("province");
        orderAddress9.setCity("city");
        orderAddress9.setCityCode("cityCode");
        orderAddress9.setDistrict("district");
        orderAddress9.setTown("town");
        orderAddress9.setDetailAddress("detailAddress");
        submitParamDTO5.setOrderAddress(orderAddress9);
        final OrderAddressDTO purchaseOrderAddress9 = new OrderAddressDTO();
        purchaseOrderAddress9.setReceiverName("receiverName");
        purchaseOrderAddress9.setReceiverMobile("receiverMobile");
        purchaseOrderAddress9.setProvince("province");
        purchaseOrderAddress9.setCity("city");
        purchaseOrderAddress9.setCityCode("cityCode");
        purchaseOrderAddress9.setDistrict("district");
        purchaseOrderAddress9.setTown("town");
        purchaseOrderAddress9.setDetailAddress("detailAddress");
        submitParamDTO5.setPurchaseOrderAddress(purchaseOrderAddress9);
        submitParamDTO5.setChannelOrder(false);
        final OrderSubmitDTO orderSubmitDTO4 = new OrderSubmitDTO(Arrays.asList(cart5), submitParamDTO5);
//        verify(mockIOrderService).dealExpress(orderSubmitDTO4, Arrays.asList(new BigDecimal("0.00")));

        // Confirm IOrderService.calculateCard(...).
        final Cart cart6 = new Cart();
        cart6.setStoreId(1L);
        cart6.setIsVirtualGoods(1);
        cart6.setCartId(0);
        cart6.setMemberId(0);
        cart6.setGoodsId(0L);
        cart6.setGoodsName("goodsName");
        cart6.setFundsBorrowable(0);
        cart6.setProductPrice(BigDecimal.ONE);
        cart6.setBuyNum(10);
        final OrderSubmitParamDTO submitParamDTO6 = new OrderSubmitParamDTO();
        submitParamDTO6.setChannel("channel");
        submitParamDTO6.setSource(0);
        submitParamDTO6.setAddressId(0);
        submitParamDTO6.setAreaCode("areaCode");
        submitParamDTO6.setProductType(0);
        submitParamDTO6.setIsAloneBuy(false);
        submitParamDTO6.setOrderPattern(0);
        submitParamDTO6.setProductId(0L);
        submitParamDTO6.setNumber(0);
        submitParamDTO6.setPno("pno");
        submitParamDTO6.setVerifyCode("verifyCode");
        submitParamDTO6.setCardCodeList(Arrays.asList("value"));
        final OrderAddressDTO orderAddress10 = new OrderAddressDTO();
        orderAddress10.setReceiverName("receiverName");
        orderAddress10.setReceiverMobile("receiverMobile");
        orderAddress10.setProvince("province");
        orderAddress10.setCity("city");
        orderAddress10.setCityCode("cityCode");
        orderAddress10.setDistrict("district");
        orderAddress10.setTown("town");
        orderAddress10.setDetailAddress("detailAddress");
        submitParamDTO6.setOrderAddress(orderAddress10);
        final OrderAddressDTO purchaseOrderAddress10 = new OrderAddressDTO();
        purchaseOrderAddress10.setReceiverName("receiverName");
        purchaseOrderAddress10.setReceiverMobile("receiverMobile");
        purchaseOrderAddress10.setProvince("province");
        purchaseOrderAddress10.setCity("city");
        purchaseOrderAddress10.setCityCode("cityCode");
        purchaseOrderAddress10.setDistrict("district");
        purchaseOrderAddress10.setTown("town");
        purchaseOrderAddress10.setDetailAddress("detailAddress");
        submitParamDTO6.setPurchaseOrderAddress(purchaseOrderAddress10);
        submitParamDTO6.setChannelOrder(false);
        final OrderSubmitDTO vo = new OrderSubmitDTO(Arrays.asList(cart6), submitParamDTO6);
        verify(mockIOrderService).calculateCard(vo, Arrays.asList("value"), "userNo");
        // verify(mockStringRedisTemplate).delete("key");
        //verify(mockSlodonLock).unlock("lockName");
    }

    @Test
    public void testCreateOrder_ICartServiceReturnsNoItems() throws Exception {
        // Setup
        final OrderSubmitMqConsumerDTO req = new OrderSubmitMqConsumerDTO();
        final OrderSubmitParamDTO paramDTO = new OrderSubmitParamDTO();
        paramDTO.setChannel("channel");
        paramDTO.setSource(0);
        paramDTO.setAddressId(0);
        paramDTO.setAreaCode("areaCode");
        paramDTO.setProductType(0);
        paramDTO.setIsAloneBuy(false);
        paramDTO.setOrderPattern(0);
        paramDTO.setProductId(0L);
        paramDTO.setNumber(0);
        paramDTO.setPno("pno");
        paramDTO.setVerifyCode("verifyCode");
        paramDTO.setCardCodeList(Arrays.asList("value"));
        final OrderAddressDTO orderAddress = new OrderAddressDTO();
        orderAddress.setReceiverName("receiverName");
        orderAddress.setReceiverMobile("receiverMobile");
        orderAddress.setProvince("province");
        orderAddress.setCity("city");
        orderAddress.setCityCode("cityCode");
        orderAddress.setDistrict("district");
        orderAddress.setTown("town");
        orderAddress.setDetailAddress("detailAddress");
        paramDTO.setOrderAddress(orderAddress);
        final OrderAddressDTO purchaseOrderAddress = new OrderAddressDTO();
        purchaseOrderAddress.setReceiverName("receiverName");
        purchaseOrderAddress.setReceiverMobile("receiverMobile");
        purchaseOrderAddress.setProvince("province");
        purchaseOrderAddress.setCity("city");
        purchaseOrderAddress.setCityCode("cityCode");
        purchaseOrderAddress.setDistrict("district");
        purchaseOrderAddress.setTown("town");
        purchaseOrderAddress.setDetailAddress("detailAddress");
        paramDTO.setPurchaseOrderAddress(purchaseOrderAddress);
        paramDTO.setChannelOrder(false);
        req.setParamDTO(paramDTO);
        req.setMemberId(0);
        req.setUserNo("userNo");
        req.setPaySn("paySn");
        final PreOrderDTO preOrderDTO = new PreOrderDTO();
        preOrderDTO.setIsCalculateDiscount(false);
        preOrderDTO.setOrderType(0);
        req.setPreOrderDTO(preOrderDTO);
        req.setAreaCode("areaCode");
        final OrderSkuInfoDTO skuInfoDTO = new OrderSkuInfoDTO();
        req.setSkuInfoList(Arrays.asList(skuInfoDTO));
        req.setOrderType(OrderTypeEnum.NORMAL);
        final ChannelOrderSubmitDTO channelOrderSubmitDTO = new ChannelOrderSubmitDTO();
        channelOrderSubmitDTO.setChannel("channel");
        channelOrderSubmitDTO.setOutBizSource("outBizSource");
        channelOrderSubmitDTO.setOutBizId("outBizId");
        req.setChannelOrderSubmitDTO(channelOrderSubmitDTO);
        req.setSimulationShoppingCart(false);
        req.setFinanceRuleCode("financeRuleCode");

        // Configure ICartService.buildCartList(...).
        final OrderSkuInfoDTO skuInfoDTO1 = new OrderSkuInfoDTO();
        skuInfoDTO1.setProductId(0L);
        skuInfoDTO1.setNumber(0);
        skuInfoDTO1.setProductPrice(new BigDecimal("0.00"));
        skuInfoDTO1.setLandingPrice(new BigDecimal("0.00"));
        skuInfoDTO1.setProductDiscountAmount(new BigDecimal("0.00"));
        final List<OrderSkuInfoDTO> skuInfoList = Arrays.asList(skuInfoDTO1);
        final OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName("receiverName");
        addressDTO.setReceiverMobile("receiverMobile");
        addressDTO.setProvince("province");
        addressDTO.setCity("city");
        addressDTO.setCityCode("cityCode");
        addressDTO.setDistrict("district");
        addressDTO.setTown("town");
        addressDTO.setDetailAddress("detailAddress");
//        when(mockCartService.buildCartList(skuInfoList, OrderTypeEnum.NORMAL, 0, "areaCode", "financeRuleCode",
//                "channel", addressDTO)).thenReturn(Collections.emptyList());

        // Configure OrderSubmitUtil.getOrderSubmitDTOV2(...).
        final Cart cart = new Cart();
        cart.setCartId(0);
        cart.setMemberId(0);
        cart.setGoodsId(0L);
        cart.setGoodsName("goodsName");
        cart.setFundsBorrowable(0);
        cart.setStoreId(1L);
        cart.setIsVirtualGoods(1);
        cart.setProductPrice(BigDecimal.ONE);
        cart.setBuyNum(10);
        final OrderSubmitParamDTO submitParamDTO = new OrderSubmitParamDTO();
        submitParamDTO.setChannel("channel");
        submitParamDTO.setSource(0);
        submitParamDTO.setAddressId(0);
        submitParamDTO.setAreaCode("areaCode");
        submitParamDTO.setProductType(0);
        submitParamDTO.setIsAloneBuy(false);
        submitParamDTO.setOrderPattern(0);
        submitParamDTO.setProductId(0L);
        submitParamDTO.setNumber(0);
        submitParamDTO.setPno("pno");
        submitParamDTO.setVerifyCode("verifyCode");
        submitParamDTO.setCardCodeList(Arrays.asList("value"));
        final OrderAddressDTO orderAddress1 = new OrderAddressDTO();
        orderAddress1.setReceiverName("receiverName");
        orderAddress1.setReceiverMobile("receiverMobile");
        orderAddress1.setProvince("province");
        orderAddress1.setCity("city");
        orderAddress1.setCityCode("cityCode");
        orderAddress1.setDistrict("district");
        orderAddress1.setTown("town");
        orderAddress1.setDetailAddress("detailAddress");
        submitParamDTO.setOrderAddress(orderAddress1);
        final OrderAddressDTO purchaseOrderAddress1 = new OrderAddressDTO();
        purchaseOrderAddress1.setReceiverName("receiverName");
        purchaseOrderAddress1.setReceiverMobile("receiverMobile");
        purchaseOrderAddress1.setProvince("province");
        purchaseOrderAddress1.setCity("city");
        purchaseOrderAddress1.setCityCode("cityCode");
        purchaseOrderAddress1.setDistrict("district");
        purchaseOrderAddress1.setTown("town");
        purchaseOrderAddress1.setDetailAddress("detailAddress");
        submitParamDTO.setPurchaseOrderAddress(purchaseOrderAddress1);
        submitParamDTO.setChannelOrder(false);
        final OrderSubmitDTO orderSubmitDTO = new OrderSubmitDTO(Arrays.asList(cart), submitParamDTO);
        final CartPO cartPO = new CartPO();
        cartPO.setCartId(0);
        cartPO.setMemberId(0);
        cartPO.setStoreId(0L);
        cartPO.setStoreName("storeName");
        cartPO.setGoodsId(0L);
        final List<CartPO> cartPOList = Arrays.asList(cartPO);
        final OrderSubmitParamDTO dto = new OrderSubmitParamDTO();
        dto.setChannel("channel");
        dto.setSource(0);
        dto.setAddressId(0);
        dto.setAreaCode("areaCode");
        dto.setProductType(0);
        dto.setIsAloneBuy(false);
        dto.setOrderPattern(0);
        dto.setProductId(0L);
        dto.setNumber(0);
        dto.setPno("pno");
        dto.setVerifyCode("verifyCode");
        dto.setCardCodeList(Arrays.asList("value"));
        final OrderAddressDTO orderAddress2 = new OrderAddressDTO();
        orderAddress2.setReceiverName("receiverName");
        orderAddress2.setReceiverMobile("receiverMobile");
        orderAddress2.setProvince("province");
        orderAddress2.setCity("city");
        orderAddress2.setCityCode("cityCode");
        orderAddress2.setDistrict("district");
        orderAddress2.setTown("town");
        orderAddress2.setDetailAddress("detailAddress");
        dto.setOrderAddress(orderAddress2);
        final OrderAddressDTO purchaseOrderAddress2 = new OrderAddressDTO();
        purchaseOrderAddress2.setReceiverName("receiverName");
        purchaseOrderAddress2.setReceiverMobile("receiverMobile");
        purchaseOrderAddress2.setProvince("province");
        purchaseOrderAddress2.setCity("city");
        purchaseOrderAddress2.setCityCode("cityCode");
        purchaseOrderAddress2.setDistrict("district");
        purchaseOrderAddress2.setTown("town");
        purchaseOrderAddress2.setDetailAddress("detailAddress");
        dto.setPurchaseOrderAddress(purchaseOrderAddress2);
        dto.setChannelOrder(false);
//        when(mockOrderSubmitUtil.getOrderSubmitDTOV2(cartPOList, dto, 0, false)).thenReturn(orderSubmitDTO);

        // Configure PromotionCommonFeignClient.orderSubmitCalculationDiscountV2(...).
        final Cart cart1 = new Cart();
        cart1.setCartId(0);
        cart1.setMemberId(0);
        cart1.setGoodsId(0L);
        cart1.setGoodsName("goodsName");
        cart1.setFundsBorrowable(0);
        cart1.setStoreId(1L);
        cart1.setIsVirtualGoods(1);
        cart1.setProductPrice(BigDecimal.ONE);
        cart1.setBuyNum(10);
        final OrderSubmitParamDTO submitParamDTO1 = new OrderSubmitParamDTO();
        submitParamDTO1.setChannel("channel");
        submitParamDTO1.setSource(0);
        submitParamDTO1.setAddressId(0);
        submitParamDTO1.setAreaCode("areaCode");
        submitParamDTO1.setProductType(0);
        submitParamDTO1.setIsAloneBuy(false);
        submitParamDTO1.setOrderPattern(0);
        submitParamDTO1.setProductId(0L);
        submitParamDTO1.setNumber(0);
        submitParamDTO1.setPno("pno");
        submitParamDTO1.setVerifyCode("verifyCode");
        submitParamDTO1.setCardCodeList(Arrays.asList("value"));
        final OrderAddressDTO orderAddress3 = new OrderAddressDTO();
        orderAddress3.setReceiverName("receiverName");
        orderAddress3.setReceiverMobile("receiverMobile");
        orderAddress3.setProvince("province");
        orderAddress3.setCity("city");
        orderAddress3.setCityCode("cityCode");
        orderAddress3.setDistrict("district");
        orderAddress3.setTown("town");
        orderAddress3.setDetailAddress("detailAddress");
        submitParamDTO1.setOrderAddress(orderAddress3);
        final OrderAddressDTO purchaseOrderAddress3 = new OrderAddressDTO();
        purchaseOrderAddress3.setReceiverName("receiverName");
        purchaseOrderAddress3.setReceiverMobile("receiverMobile");
        purchaseOrderAddress3.setProvince("province");
        purchaseOrderAddress3.setCity("city");
        purchaseOrderAddress3.setCityCode("cityCode");
        purchaseOrderAddress3.setDistrict("district");
        purchaseOrderAddress3.setTown("town");
        purchaseOrderAddress3.setDetailAddress("detailAddress");
        submitParamDTO1.setPurchaseOrderAddress(purchaseOrderAddress3);
        submitParamDTO1.setChannelOrder(false);
        final OrderSubmitDTO orderSubmitDTO1 = new OrderSubmitDTO(Arrays.asList(cart1), submitParamDTO1);
//        when(mockPromotionCommonFeignClient.orderSubmitCalculationDiscountV2(orderSubmitDTO1, 0))
//                .thenReturn(new JsonResult<>(0, "errMsg"));

        // Configure OrderLocalUtils.getOrderExpressFee(...).
        final Cart cart2 = new Cart();
        cart2.setCartId(0);
        cart2.setMemberId(0);
        cart2.setGoodsId(0L);
        cart2.setGoodsName("goodsName");
        cart2.setFundsBorrowable(0);
        cart2.setStoreId(1L);
        cart2.setIsVirtualGoods(1);
        cart2.setProductPrice(BigDecimal.ONE);
        cart2.setBuyNum(10);
        final OrderSubmitParamDTO submitParamDTO2 = new OrderSubmitParamDTO();
        submitParamDTO2.setChannel("channel");
        submitParamDTO2.setSource(0);
        submitParamDTO2.setAddressId(0);
        submitParamDTO2.setAreaCode("areaCode");
        submitParamDTO2.setProductType(0);
        submitParamDTO2.setIsAloneBuy(false);
        submitParamDTO2.setOrderPattern(0);
        submitParamDTO2.setProductId(0L);
        submitParamDTO2.setNumber(0);
        submitParamDTO2.setPno("pno");
        submitParamDTO2.setVerifyCode("verifyCode");
        submitParamDTO2.setCardCodeList(Arrays.asList("value"));
        final OrderAddressDTO orderAddress4 = new OrderAddressDTO();
        orderAddress4.setReceiverName("receiverName");
        orderAddress4.setReceiverMobile("receiverMobile");
        orderAddress4.setProvince("province");
        orderAddress4.setCity("city");
        orderAddress4.setCityCode("cityCode");
        orderAddress4.setDistrict("district");
        orderAddress4.setTown("town");
        orderAddress4.setDetailAddress("detailAddress");
        submitParamDTO2.setOrderAddress(orderAddress4);
        final OrderAddressDTO purchaseOrderAddress4 = new OrderAddressDTO();
        purchaseOrderAddress4.setReceiverName("receiverName");
        purchaseOrderAddress4.setReceiverMobile("receiverMobile");
        purchaseOrderAddress4.setProvince("province");
        purchaseOrderAddress4.setCity("city");
        purchaseOrderAddress4.setCityCode("cityCode");
        purchaseOrderAddress4.setDistrict("district");
        purchaseOrderAddress4.setTown("town");
        purchaseOrderAddress4.setDetailAddress("detailAddress");
        submitParamDTO2.setPurchaseOrderAddress(purchaseOrderAddress4);
        submitParamDTO2.setChannelOrder(false);
        final OrderSubmitDTO.OrderInfo orderInfo = new OrderSubmitDTO.OrderInfo(Arrays.asList(cart2), submitParamDTO2);
        // when(mockOrderLocalUtils.getOrderExpressFee("cityCode", orderInfo)).thenReturn(new BigDecimal("0.00"));

        // Configure MemberFeignClient.getMemberByMemberId(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setUserNo("userNo");
        member.setCustNo("custNo");
        member.setCappCustNo("cappCustNo");
        member.setMemberName("memberName");
//        when(mockMemberFeignClient.getMemberByMemberId(0)).thenReturn(member);

        // Configure OrderModel.submitOrder(...).
        final Cart cart3 = new Cart();
        cart3.setCartId(0);
        cart3.setMemberId(0);
        cart3.setGoodsId(0L);
        cart3.setGoodsName("goodsName");
        cart3.setFundsBorrowable(0);
        cart3.setStoreId(1L);
        cart3.setIsVirtualGoods(1);
        cart3.setProductPrice(BigDecimal.ONE);
        cart3.setBuyNum(10);
        final OrderSubmitParamDTO submitParamDTO3 = new OrderSubmitParamDTO();
        submitParamDTO3.setChannel("channel");
        submitParamDTO3.setSource(0);
        submitParamDTO3.setAddressId(0);
        submitParamDTO3.setAreaCode("areaCode");
        submitParamDTO3.setProductType(0);
        submitParamDTO3.setIsAloneBuy(false);
        submitParamDTO3.setOrderPattern(0);
        submitParamDTO3.setProductId(0L);
        submitParamDTO3.setNumber(0);
        submitParamDTO3.setPno("pno");
        submitParamDTO3.setVerifyCode("verifyCode");
        submitParamDTO3.setCardCodeList(Arrays.asList("value"));
        final OrderAddressDTO orderAddress5 = new OrderAddressDTO();
        orderAddress5.setReceiverName("receiverName");
        orderAddress5.setReceiverMobile("receiverMobile");
        orderAddress5.setProvince("province");
        orderAddress5.setCity("city");
        orderAddress5.setCityCode("cityCode");
        orderAddress5.setDistrict("district");
        orderAddress5.setTown("town");
        orderAddress5.setDetailAddress("detailAddress");
        submitParamDTO3.setOrderAddress(orderAddress5);
        final OrderAddressDTO purchaseOrderAddress5 = new OrderAddressDTO();
        purchaseOrderAddress5.setReceiverName("receiverName");
        purchaseOrderAddress5.setReceiverMobile("receiverMobile");
        purchaseOrderAddress5.setProvince("province");
        purchaseOrderAddress5.setCity("city");
        purchaseOrderAddress5.setCityCode("cityCode");
        purchaseOrderAddress5.setDistrict("district");
        purchaseOrderAddress5.setTown("town");
        purchaseOrderAddress5.setDetailAddress("detailAddress");
        submitParamDTO3.setPurchaseOrderAddress(purchaseOrderAddress5);
        submitParamDTO3.setChannelOrder(false);
        final OrderSubmitDTO orderSubmitDTO2 = new OrderSubmitDTO(Arrays.asList(cart3), submitParamDTO3);
        final Member member1 = new Member();
        member1.setMemberId(0);
        member1.setUserNo("userNo");
        member1.setCustNo("custNo");
        member1.setCappCustNo("cappCustNo");
        member1.setMemberName("memberName");
        final OrderSubmitMqConsumerDTO consumerDTO = new OrderSubmitMqConsumerDTO();
        final OrderSubmitParamDTO paramDTO1 = new OrderSubmitParamDTO();
        paramDTO1.setChannel("channel");
        paramDTO1.setSource(0);
        paramDTO1.setAddressId(0);
        paramDTO1.setAreaCode("areaCode");
        paramDTO1.setProductType(0);
        paramDTO1.setIsAloneBuy(false);
        paramDTO1.setOrderPattern(0);
        paramDTO1.setProductId(0L);
        paramDTO1.setNumber(0);
        paramDTO1.setPno("pno");
        paramDTO1.setVerifyCode("verifyCode");
        paramDTO1.setCardCodeList(Arrays.asList("value"));
        final OrderAddressDTO orderAddress6 = new OrderAddressDTO();
        orderAddress6.setReceiverName("receiverName");
        orderAddress6.setReceiverMobile("receiverMobile");
        orderAddress6.setProvince("province");
        orderAddress6.setCity("city");
        orderAddress6.setCityCode("cityCode");
        orderAddress6.setDistrict("district");
        orderAddress6.setTown("town");
        orderAddress6.setDetailAddress("detailAddress");
        paramDTO1.setOrderAddress(orderAddress6);
        final OrderAddressDTO purchaseOrderAddress6 = new OrderAddressDTO();
        purchaseOrderAddress6.setReceiverName("receiverName");
        purchaseOrderAddress6.setReceiverMobile("receiverMobile");
        purchaseOrderAddress6.setProvince("province");
        purchaseOrderAddress6.setCity("city");
        purchaseOrderAddress6.setCityCode("cityCode");
        purchaseOrderAddress6.setDistrict("district");
        purchaseOrderAddress6.setTown("town");
        purchaseOrderAddress6.setDetailAddress("detailAddress");
        paramDTO1.setPurchaseOrderAddress(purchaseOrderAddress6);
        paramDTO1.setChannelOrder(false);
        consumerDTO.setParamDTO(paramDTO1);
        consumerDTO.setMemberId(0);
        consumerDTO.setUserNo("userNo");
        consumerDTO.setPaySn("paySn");
        final PreOrderDTO preOrderDTO1 = new PreOrderDTO();
        preOrderDTO1.setIsCalculateDiscount(false);
        preOrderDTO1.setOrderType(0);
        consumerDTO.setPreOrderDTO(preOrderDTO1);
        consumerDTO.setAreaCode("areaCode");
        final OrderSkuInfoDTO skuInfoDTO2 = new OrderSkuInfoDTO();
        consumerDTO.setSkuInfoList(Arrays.asList(skuInfoDTO2));
        consumerDTO.setOrderType(OrderTypeEnum.NORMAL);
        final ChannelOrderSubmitDTO channelOrderSubmitDTO1 = new ChannelOrderSubmitDTO();
        channelOrderSubmitDTO1.setChannel("channel");
        channelOrderSubmitDTO1.setOutBizSource("outBizSource");
        channelOrderSubmitDTO1.setOutBizId("outBizId");
        consumerDTO.setChannelOrderSubmitDTO(channelOrderSubmitDTO1);
        consumerDTO.setSimulationShoppingCart(false);
        consumerDTO.setFinanceRuleCode("financeRuleCode");
//        when(mockOrderModel.submitOrder(orderSubmitDTO2, member1, consumerDTO))
//                .thenReturn(new HashSet<>(Arrays.asList("value")));

//        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);
//
//        // Run the test
//        final long result = placingOrderServiceImplUnderTest.createOrder(req);
//
//        // Verify the results
//        assertThat(result).isEqualTo(0L);
//
//        // Confirm IOrderService.dealExpress(...).
//        final Cart cart4 = new Cart();
//        cart4.setCartId(0);
//        cart4.setMemberId(0);
//        cart4.setGoodsId(0L);
//        cart4.setGoodsName("goodsName");
//        cart4.setFundsBorrowable(0);
//        cart4.setStoreId(1L);
//        cart4.setIsVirtualGoods(1);
//        cart4.setProductPrice(BigDecimal.ONE);
//        cart4.setBuyNum(10);
//        final OrderSubmitParamDTO submitParamDTO4 = new OrderSubmitParamDTO();
//        submitParamDTO4.setChannel("channel");
//        submitParamDTO4.setSource(0);
//        submitParamDTO4.setAddressId(0);
//        submitParamDTO4.setAreaCode("areaCode");
//        submitParamDTO4.setProductType(0);
//        submitParamDTO4.setIsAloneBuy(false);
//        submitParamDTO4.setOrderPattern(0);
//        submitParamDTO4.setProductId(0L);
//        submitParamDTO4.setNumber(0);
//        submitParamDTO4.setPno("pno");
//        submitParamDTO4.setVerifyCode("verifyCode");
//        submitParamDTO4.setCardCodeList(Arrays.asList("value"));
//        final OrderAddressDTO orderAddress7 = new OrderAddressDTO();
//        orderAddress7.setReceiverName("receiverName");
//        orderAddress7.setReceiverMobile("receiverMobile");
//        orderAddress7.setProvince("province");
//        orderAddress7.setCity("city");
//        orderAddress7.setCityCode("cityCode");
//        orderAddress7.setDistrict("district");
//        orderAddress7.setTown("town");
//        orderAddress7.setDetailAddress("detailAddress");
//        submitParamDTO4.setOrderAddress(orderAddress7);
//        final OrderAddressDTO purchaseOrderAddress7 = new OrderAddressDTO();
//        purchaseOrderAddress7.setReceiverName("receiverName");
//        purchaseOrderAddress7.setReceiverMobile("receiverMobile");
//        purchaseOrderAddress7.setProvince("province");
//        purchaseOrderAddress7.setCity("city");
//        purchaseOrderAddress7.setCityCode("cityCode");
//        purchaseOrderAddress7.setDistrict("district");
//        purchaseOrderAddress7.setTown("town");
//        purchaseOrderAddress7.setDetailAddress("detailAddress");
//        submitParamDTO4.setPurchaseOrderAddress(purchaseOrderAddress7);
//        submitParamDTO4.setChannelOrder(false);
//        final OrderSubmitDTO orderSubmitDTO3 = new OrderSubmitDTO(Arrays.asList(cart4), submitParamDTO4);
//        verify(mockIOrderService).dealExpress(orderSubmitDTO3, Arrays.asList(new BigDecimal("0.00")));
//
//        // Confirm IOrderService.calculateCard(...).
//        final Cart cart5 = new Cart();
//        cart5.setCartId(0);
//        cart5.setMemberId(0);
//        cart5.setGoodsId(0L);
//        cart5.setGoodsName("goodsName");
//        cart5.setFundsBorrowable(0);
//        cart5.setStoreId(1L);
//        cart5.setIsVirtualGoods(1);
//        cart5.setProductPrice(BigDecimal.ONE);
//        cart5.setBuyNum(10);
//        final OrderSubmitParamDTO submitParamDTO5 = new OrderSubmitParamDTO();
//        submitParamDTO5.setChannel("channel");
//        submitParamDTO5.setSource(0);
//        submitParamDTO5.setAddressId(0);
//        submitParamDTO5.setAreaCode("areaCode");
//        submitParamDTO5.setProductType(0);
//        submitParamDTO5.setIsAloneBuy(false);
//        submitParamDTO5.setOrderPattern(0);
//        submitParamDTO5.setProductId(0L);
//        submitParamDTO5.setNumber(0);
//        submitParamDTO5.setPno("pno");
//        submitParamDTO5.setVerifyCode("verifyCode");
//        submitParamDTO5.setCardCodeList(Arrays.asList("value"));
//        final OrderAddressDTO orderAddress8 = new OrderAddressDTO();
//        orderAddress8.setReceiverName("receiverName");
//        orderAddress8.setReceiverMobile("receiverMobile");
//        orderAddress8.setProvince("province");
//        orderAddress8.setCity("city");
//        orderAddress8.setCityCode("cityCode");
//        orderAddress8.setDistrict("district");
//        orderAddress8.setTown("town");
//        orderAddress8.setDetailAddress("detailAddress");
//        submitParamDTO5.setOrderAddress(orderAddress8);
//        final OrderAddressDTO purchaseOrderAddress8 = new OrderAddressDTO();
//        purchaseOrderAddress8.setReceiverName("receiverName");
//        purchaseOrderAddress8.setReceiverMobile("receiverMobile");
//        purchaseOrderAddress8.setProvince("province");
//        purchaseOrderAddress8.setCity("city");
//        purchaseOrderAddress8.setCityCode("cityCode");
//        purchaseOrderAddress8.setDistrict("district");
//        purchaseOrderAddress8.setTown("town");
//        purchaseOrderAddress8.setDetailAddress("detailAddress");
//        submitParamDTO5.setPurchaseOrderAddress(purchaseOrderAddress8);
//        submitParamDTO5.setChannelOrder(false);
//        final OrderSubmitDTO vo = new OrderSubmitDTO(Arrays.asList(cart5), submitParamDTO5);
//        verify(mockIOrderService).calculateCard(vo, Arrays.asList("value"), "userNo");
        // verify(mockStringRedisTemplate).delete("key");
        // verify(mockSlodonLock).unlock("lockName");
    }

    @Test
    public void testCreateRebateGiftOrder() {
        // Setup
        final RebateOrderSubmitDTO dto = new RebateOrderSubmitDTO();
        dto.setIdempotent("idempotent");
        dto.setChannel("channel");
        dto.setStoreId(0L);
        final OrderSkuInfoDTO orderSkuInfoDTO = new OrderSkuInfoDTO();
        orderSkuInfoDTO.setProductId(0L);
        orderSkuInfoDTO.setNumber(0);
        orderSkuInfoDTO.setTaxPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO.setFinanceRuleCode("financeRuleCode");
        orderSkuInfoDTO.setAgriServiceFees(new BigDecimal("0.00"));
        dto.setSkuInfoList(Arrays.asList(orderSkuInfoDTO));
        final OrderAddressDTO address = new OrderAddressDTO();
        address.setReceiverName("receiverName");
        address.setReceiverMobile("receiverMobile");
        address.setProvince("province");
        address.setCity("city");
        address.setCityCode("cityCode");
        address.setDistrict("district");
        address.setTown("town");
        address.setDetailAddress("detailAddress");
        dto.setAddress(address);
        dto.setPointId(0L);
        dto.setMemberId(0L);

        final OrderSubmitVO expectedResult = new OrderSubmitVO();
        expectedResult.setPaySn("paySn");
        expectedResult.setOrderSnList(Arrays.asList("value"));

        // Configure OrderCreateHelper.buildOrderAddressByPointId(...).
        final OrderAddressDTO orderAddressDTO = new OrderAddressDTO();
        orderAddressDTO.setReceiverName("receiverName");
        orderAddressDTO.setReceiverMobile("receiverMobile");
        orderAddressDTO.setProvince("province");
        orderAddressDTO.setCity("city");
        orderAddressDTO.setCityCode("cityCode");
        orderAddressDTO.setDistrict("district");
        orderAddressDTO.setTown("town");
        orderAddressDTO.setDetailAddress("detailAddress");
//        when(mockOrderCreateHelper.buildOrderAddressByPointId(0L)).thenReturn(orderAddressDTO);

        // Configure MemberFeignClient.getMemberByMemberId(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setUserNo("userNo");
        member.setCustNo("custNo");
        member.setCappCustNo("cappCustNo");
        member.setMemberName("orderPlaceUserName");
//        when(mockMemberFeignClient.getMemberByMemberId(0)).thenReturn(member);

        // Configure OrderCreateHelper.getStoreAreaCodeByAddress(...).
        final OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName("receiverName");
        addressDTO.setReceiverMobile("receiverMobile");
        addressDTO.setProvince("province");
        addressDTO.setCity("city");
        addressDTO.setCityCode("cityCode");
        addressDTO.setDistrict("district");
        addressDTO.setTown("town");
        addressDTO.setDetailAddress("detailAddress");
        final Member member1 = new Member();
        member1.setMemberId(0);
        member1.setUserNo("userNo");
        member1.setCustNo("custNo");
        member1.setCappCustNo("cappCustNo");
        member1.setMemberName("orderPlaceUserName");
//        when(mockOrderCreateHelper.getStoreAreaCodeByAddress("storeId", addressDTO, member1)).thenReturn("areaCode");

        // Configure ICartService.buildCartList(...).
        final CartPO cartPO = new CartPO();
        cartPO.setCartId(0);
        cartPO.setMemberId(0);
        cartPO.setStoreId(0L);
        cartPO.setStoreName("storeName");
        cartPO.setAreaCode("areaCode");
        final List<CartPO> cartPOS = Arrays.asList(cartPO);
        final OrderSkuInfoDTO orderSkuInfoDTO1 = new OrderSkuInfoDTO();
        orderSkuInfoDTO1.setProductId(0L);
        orderSkuInfoDTO1.setNumber(0);
        orderSkuInfoDTO1.setTaxPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO1.setFinanceRuleCode("financeRuleCode");
        orderSkuInfoDTO1.setAgriServiceFees(new BigDecimal("0.00"));
        final List<OrderSkuInfoDTO> skuInfoList = Arrays.asList(orderSkuInfoDTO1);
        final OrderAddressDTO addressDTO1 = new OrderAddressDTO();
        addressDTO1.setReceiverName("receiverName");
        addressDTO1.setReceiverMobile("receiverMobile");
        addressDTO1.setProvince("province");
        addressDTO1.setCity("city");
        addressDTO1.setCityCode("cityCode");
        addressDTO1.setDistrict("district");
        addressDTO1.setTown("town");
        addressDTO1.setDetailAddress("detailAddress");
//        when(mockCartService.buildCartList(skuInfoList, OrderTypeEnum.NORMAL, 0, "areaCode", "financeRuleCode",
//                "channel", addressDTO1)).thenReturn(cartPOS);

        // Configure OrderSubmitUtil.getOrderSubmitDTOV2(...).
        final Cart cart = new Cart();
        cart.setCartId(0);
        cart.setMemberId(0);
        cart.setStoreId(0L);
        cart.setStoreName("storeName");
        cart.setGoodsId(0L);
        final OrderSubmitParamDTO orderSubmitParamDTO = new OrderSubmitParamDTO();
        orderSubmitParamDTO.setChannel("channel");
        orderSubmitParamDTO.setUsrNo("userNo");
        orderSubmitParamDTO.setMemberId(0);
        orderSubmitParamDTO.setSource(0);
        orderSubmitParamDTO.setOrderFrom(0);
        orderSubmitParamDTO.setAddressId(0);
        orderSubmitParamDTO.setAreaCode("areaCode");
        orderSubmitParamDTO.setIsCart(false);
        orderSubmitParamDTO.setProductType(0);
        orderSubmitParamDTO.setIsAloneBuy(false);
        orderSubmitParamDTO.setOrderPattern(0);
        orderSubmitParamDTO.setGroupBuyingTag(0);
        orderSubmitParamDTO.setProductId(0L);
        orderSubmitParamDTO.setNumber(0);
        orderSubmitParamDTO.setPno("pno");
        orderSubmitParamDTO.setVerifyCode("verifyCode");
        orderSubmitParamDTO.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress = new OrderAddressDTO();
        orderAddress.setReceiverName("receiverName");
        orderAddress.setReceiverMobile("receiverMobile");
        orderAddress.setProvince("province");
        orderAddress.setCity("city");
        orderAddress.setCityCode("cityCode");
        orderAddress.setDistrict("district");
        orderAddress.setTown("town");
        orderAddress.setDetailAddress("detailAddress");
        orderSubmitParamDTO.setOrderAddress(orderAddress);
        final OrderAddressDTO purchaseOrderAddress = new OrderAddressDTO();
        purchaseOrderAddress.setReceiverName("receiverName");
        purchaseOrderAddress.setReceiverMobile("receiverMobile");
        purchaseOrderAddress.setProvince("province");
        purchaseOrderAddress.setCity("city");
        purchaseOrderAddress.setCityCode("cityCode");
        purchaseOrderAddress.setDistrict("district");
        purchaseOrderAddress.setTown("town");
        purchaseOrderAddress.setDetailAddress("detailAddress");
        orderSubmitParamDTO.setPurchaseOrderAddress(purchaseOrderAddress);
        orderSubmitParamDTO.setChannelOrder(false);
        orderSubmitParamDTO.setBankTransferable(false);
        orderSubmitParamDTO.setOrderType(OrderTypeEnum.NORMAL);
        final OrderPromotionParamDTO orderPromotionInfo = new OrderPromotionParamDTO();
        orderSubmitParamDTO.setOrderPromotionInfo(orderPromotionInfo);
        orderSubmitParamDTO.setShareResource(false);
        orderSubmitParamDTO.setPlacingCombinationFlag(false);
        orderSubmitParamDTO.setOrderPlaceUserRole(OrderPlaceUserRole.SELF);
        orderSubmitParamDTO.setLoanPayer("loanPayer");
        orderSubmitParamDTO.setLoanConfirmMethod("loanConfirmMethod");
        orderSubmitParamDTO.setAttachmentUrls(Arrays.asList("value"));
//        final OrderSubmitDTO orderSubmitDTO = new OrderSubmitDTO(Arrays.asList(cart), orderSubmitParamDTO);
        final CartPO cartPO1 = new CartPO();
        cartPO1.setCartId(0);
        cartPO1.setMemberId(0);
        cartPO1.setStoreId(0L);
        cartPO1.setStoreName("storeName");
        cartPO1.setAreaCode("areaCode");
        final List<CartPO> cartPOList = Arrays.asList(cartPO1);
        final OrderSubmitParamDTO dto1 = new OrderSubmitParamDTO();
        dto1.setChannel("channel");
        dto1.setUsrNo("userNo");
        dto1.setMemberId(0);
        dto1.setSource(0);
        dto1.setOrderFrom(0);
        dto1.setAddressId(0);
        dto1.setAreaCode("areaCode");
        dto1.setIsCart(false);
        dto1.setProductType(0);
        dto1.setIsAloneBuy(false);
        dto1.setOrderPattern(0);
        dto1.setGroupBuyingTag(0);
        dto1.setProductId(0L);
        dto1.setNumber(0);
        dto1.setPno("pno");
        dto1.setVerifyCode("verifyCode");
        dto1.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress1 = new OrderAddressDTO();
        orderAddress1.setReceiverName("receiverName");
        orderAddress1.setReceiverMobile("receiverMobile");
        orderAddress1.setProvince("province");
        orderAddress1.setCity("city");
        orderAddress1.setCityCode("cityCode");
        orderAddress1.setDistrict("district");
        orderAddress1.setTown("town");
        orderAddress1.setDetailAddress("detailAddress");
        dto1.setOrderAddress(orderAddress1);
        final OrderAddressDTO purchaseOrderAddress1 = new OrderAddressDTO();
        purchaseOrderAddress1.setReceiverName("receiverName");
        purchaseOrderAddress1.setReceiverMobile("receiverMobile");
        purchaseOrderAddress1.setProvince("province");
        purchaseOrderAddress1.setCity("city");
        purchaseOrderAddress1.setCityCode("cityCode");
        purchaseOrderAddress1.setDistrict("district");
        purchaseOrderAddress1.setTown("town");
        purchaseOrderAddress1.setDetailAddress("detailAddress");
        dto1.setPurchaseOrderAddress(purchaseOrderAddress1);
        dto1.setChannelOrder(false);
        dto1.setBankTransferable(false);
        dto1.setOrderType(OrderTypeEnum.NORMAL);
        final OrderPromotionParamDTO orderPromotionInfo1 = new OrderPromotionParamDTO();
        dto1.setOrderPromotionInfo(orderPromotionInfo1);
        dto1.setShareResource(false);
        dto1.setPlacingCombinationFlag(false);
        dto1.setOrderPlaceUserRole(OrderPlaceUserRole.SELF);
        dto1.setLoanPayer("loanPayer");
        dto1.setLoanConfirmMethod("loanConfirmMethod");
        dto1.setAttachmentUrls(Arrays.asList("value"));
//        when(mockOrderSubmitUtil.getOrderSubmitDTOV2(cartPOList, dto1, true)).thenReturn(orderSubmitDTO);

//        when(mockShardingId.next(SeqEnum.PNO, "memberId")).thenReturn(0L);
//        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Configure OrderModel.submit(...).
        final Cart cart1 = new Cart();
        cart1.setCartId(0);
        cart1.setMemberId(0);
        cart1.setStoreId(0L);
        cart1.setStoreName("storeName");
        cart1.setGoodsId(0L);
        final OrderSubmitParamDTO orderSubmitParamDTO1 = new OrderSubmitParamDTO();
        orderSubmitParamDTO1.setChannel("channel");
        orderSubmitParamDTO1.setUsrNo("userNo");
        orderSubmitParamDTO1.setMemberId(0);
        orderSubmitParamDTO1.setSource(0);
        orderSubmitParamDTO1.setOrderFrom(0);
        orderSubmitParamDTO1.setAddressId(0);
        orderSubmitParamDTO1.setAreaCode("areaCode");
        orderSubmitParamDTO1.setIsCart(false);
        orderSubmitParamDTO1.setProductType(0);
        orderSubmitParamDTO1.setIsAloneBuy(false);
        orderSubmitParamDTO1.setOrderPattern(0);
        orderSubmitParamDTO1.setGroupBuyingTag(0);
        orderSubmitParamDTO1.setProductId(0L);
        orderSubmitParamDTO1.setNumber(0);
        orderSubmitParamDTO1.setPno("pno");
        orderSubmitParamDTO1.setVerifyCode("verifyCode");
        orderSubmitParamDTO1.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress2 = new OrderAddressDTO();
        orderAddress2.setReceiverName("receiverName");
        orderAddress2.setReceiverMobile("receiverMobile");
        orderAddress2.setProvince("province");
        orderAddress2.setCity("city");
        orderAddress2.setCityCode("cityCode");
        orderAddress2.setDistrict("district");
        orderAddress2.setTown("town");
        orderAddress2.setDetailAddress("detailAddress");
        orderSubmitParamDTO1.setOrderAddress(orderAddress2);
        final OrderAddressDTO purchaseOrderAddress2 = new OrderAddressDTO();
        purchaseOrderAddress2.setReceiverName("receiverName");
        purchaseOrderAddress2.setReceiverMobile("receiverMobile");
        purchaseOrderAddress2.setProvince("province");
        purchaseOrderAddress2.setCity("city");
        purchaseOrderAddress2.setCityCode("cityCode");
        purchaseOrderAddress2.setDistrict("district");
        purchaseOrderAddress2.setTown("town");
        purchaseOrderAddress2.setDetailAddress("detailAddress");
        orderSubmitParamDTO1.setPurchaseOrderAddress(purchaseOrderAddress2);
        orderSubmitParamDTO1.setChannelOrder(false);
        orderSubmitParamDTO1.setBankTransferable(false);
        orderSubmitParamDTO1.setOrderType(OrderTypeEnum.NORMAL);
        final OrderPromotionParamDTO orderPromotionInfo2 = new OrderPromotionParamDTO();
        orderSubmitParamDTO1.setOrderPromotionInfo(orderPromotionInfo2);
        orderSubmitParamDTO1.setShareResource(false);
        orderSubmitParamDTO1.setPlacingCombinationFlag(false);
        orderSubmitParamDTO1.setOrderPlaceUserRole(OrderPlaceUserRole.SELF);
        orderSubmitParamDTO1.setLoanPayer("loanPayer");
        orderSubmitParamDTO1.setLoanConfirmMethod("loanConfirmMethod");
        orderSubmitParamDTO1.setAttachmentUrls(Arrays.asList("value"));
//        final OrderSubmitDTO orderSubmitDTO1 = new OrderSubmitDTO(Arrays.asList(cart1), orderSubmitParamDTO1);
        final Member member2 = new Member();
        member2.setMemberId(0);
        member2.setUserNo("userNo");
        member2.setCustNo("custNo");
        member2.setCappCustNo("cappCustNo");
        member2.setMemberName("orderPlaceUserName");
        final OrderSubmitMqConsumerDTO consumerDTO = new OrderSubmitMqConsumerDTO();
        final OrderSubmitParamDTO paramDTO = new OrderSubmitParamDTO();
        paramDTO.setChannel("channel");
        paramDTO.setUsrNo("userNo");
        paramDTO.setMemberId(0);
        paramDTO.setSource(0);
        paramDTO.setOrderFrom(0);
        paramDTO.setAddressId(0);
        paramDTO.setAreaCode("areaCode");
        paramDTO.setIsCart(false);
        paramDTO.setProductType(0);
        paramDTO.setIsAloneBuy(false);
        paramDTO.setOrderPattern(0);
        paramDTO.setGroupBuyingTag(0);
        paramDTO.setProductId(0L);
        paramDTO.setNumber(0);
        paramDTO.setPno("pno");
        paramDTO.setVerifyCode("verifyCode");
        paramDTO.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress3 = new OrderAddressDTO();
        orderAddress3.setProvince("province");
        orderAddress3.setCity("city");
        orderAddress3.setCityCode("cityCode");
        orderAddress3.setDistrict("district");
        orderAddress3.setDetailAddress("detailAddress");
        paramDTO.setOrderAddress(orderAddress3);
        final OrderAddressDTO purchaseOrderAddress3 = new OrderAddressDTO();
        purchaseOrderAddress3.setProvince("province");
        purchaseOrderAddress3.setCity("city");
        purchaseOrderAddress3.setCityCode("cityCode");
        purchaseOrderAddress3.setDistrict("district");
        purchaseOrderAddress3.setDetailAddress("detailAddress");
        paramDTO.setPurchaseOrderAddress(purchaseOrderAddress3);
        paramDTO.setChannelOrder(false);
        paramDTO.setBankTransferable(false);
        paramDTO.setOrderType(OrderTypeEnum.NORMAL);
        final OrderPromotionParamDTO orderPromotionInfo3 = new OrderPromotionParamDTO();
        paramDTO.setOrderPromotionInfo(orderPromotionInfo3);
        paramDTO.setShareResource(false);
        paramDTO.setPlacingCombinationFlag(false);
        paramDTO.setOrderPlaceUserRole(OrderPlaceUserRole.SELF);
        consumerDTO.setParamDTO(paramDTO);
        consumerDTO.setMemberId(0);
        consumerDTO.setUserNo("userNo");
        consumerDTO.setPaySn("paySn");
        final PreOrderDTO preOrderDTO = new PreOrderDTO();
        preOrderDTO.setIsCalculateDiscount(false);
        preOrderDTO.setOrderType(0);
        consumerDTO.setPreOrderDTO(preOrderDTO);
        consumerDTO.setAreaCode("areaCode");
        final OrderSkuInfoDTO orderSkuInfoDTO2 = new OrderSkuInfoDTO();
        orderSkuInfoDTO2.setProductId(0L);
        orderSkuInfoDTO2.setNumber(0);
        orderSkuInfoDTO2.setTaxPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO2.setFinanceRuleCode("financeRuleCode");
        orderSkuInfoDTO2.setAgriServiceFees(new BigDecimal("0.00"));
        consumerDTO.setSkuInfoList(Arrays.asList(orderSkuInfoDTO2));
        consumerDTO.setOrderType(OrderTypeEnum.NORMAL);
        final ChannelOrderSubmitDTO channelOrderSubmitDTO = new ChannelOrderSubmitDTO();
        channelOrderSubmitDTO.setChannel("channel");
        channelOrderSubmitDTO.setOutBizSource("outBizSource");
        channelOrderSubmitDTO.setOutBizId("outBizId");
        consumerDTO.setChannelOrderSubmitDTO(channelOrderSubmitDTO);
        final OrderOfflineParamDTO orderOfflineParamDTO = new OrderOfflineParamDTO();
        orderOfflineParamDTO.setOrderPattern(0);
        orderOfflineParamDTO.setEmployeeCode("employeeCode");
        final OrderAddressDTO address1 = new OrderAddressDTO();
        address1.setProvince("province");
        address1.setCity("city");
        address1.setCityCode("cityCode");
        address1.setDistrict("district");
        address1.setDetailAddress("detailAddress");
        orderOfflineParamDTO.setAddress(address1);
        orderOfflineParamDTO.setPointId(0L);
        final OrderOfflineDTO orderOfflineDTO = new OrderOfflineDTO();
        orderOfflineParamDTO.setOrderOfflineList(Arrays.asList(orderOfflineDTO));
        final OrderOfflineInfoDTO offlineInfoDTO = new OrderOfflineInfoDTO();
        orderOfflineParamDTO.setOfflineInfoDTO(offlineInfoDTO);
        orderOfflineParamDTO.setUserMobile("memberMobile");
        orderOfflineParamDTO.setIsContinueSubmit(false);
        consumerDTO.setOrderOfflineParamDTO(orderOfflineParamDTO);
        final GroupOrderProductSubmitDTO groupOrderProductSubmitDTO = new GroupOrderProductSubmitDTO();
        consumerDTO.setGroupOrderProductSubmitDTO(groupOrderProductSubmitDTO);
//        when(mockOrderModel.submit(orderSubmitDTO1, member2, consumerDTO)).thenReturn(Arrays.asList("value"));

        // Run the test
//        final OrderSubmitVO result = placingOrderServiceImplUnderTest.createRebateGiftOrder(dto);

        // Verify the results
//        assertEquals(expectedResult, result);
//        verify(mockStringRedisTemplate).expire("idempotent", 7L, TimeUnit.DAYS);
    }

    @Test
    public void testCreateRebateGiftOrder_ICartServiceReturnsNoItems() {
        // Setup
        final RebateOrderSubmitDTO dto = new RebateOrderSubmitDTO();
        dto.setIdempotent("idempotent");
        dto.setChannel("channel");
        dto.setStoreId(0L);
        final OrderSkuInfoDTO orderSkuInfoDTO = new OrderSkuInfoDTO();
        orderSkuInfoDTO.setProductId(0L);
        orderSkuInfoDTO.setNumber(0);
        orderSkuInfoDTO.setTaxPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO.setFinanceRuleCode("financeRuleCode");
        orderSkuInfoDTO.setAgriServiceFees(new BigDecimal("0.00"));
        dto.setSkuInfoList(Arrays.asList(orderSkuInfoDTO));
        final OrderAddressDTO address = new OrderAddressDTO();
        address.setReceiverName("receiverName");
        address.setReceiverMobile("receiverMobile");
        address.setProvince("province");
        address.setCity("city");
        address.setCityCode("cityCode");
        address.setDistrict("district");
        address.setTown("town");
        address.setDetailAddress("detailAddress");
        dto.setAddress(address);
        dto.setPointId(0L);
        dto.setMemberId(0L);

        final OrderSubmitVO expectedResult = new OrderSubmitVO();
        expectedResult.setPaySn("paySn");
        expectedResult.setOrderSnList(Arrays.asList("value"));

        // Configure OrderCreateHelper.buildOrderAddressByPointId(...).
        final OrderAddressDTO orderAddressDTO = new OrderAddressDTO();
        orderAddressDTO.setReceiverName("receiverName");
        orderAddressDTO.setReceiverMobile("receiverMobile");
        orderAddressDTO.setProvince("province");
        orderAddressDTO.setCity("city");
        orderAddressDTO.setCityCode("cityCode");
        orderAddressDTO.setDistrict("district");
        orderAddressDTO.setTown("town");
        orderAddressDTO.setDetailAddress("detailAddress");
//        when(mockOrderCreateHelper.buildOrderAddressByPointId(0L)).thenReturn(orderAddressDTO);

        // Configure MemberFeignClient.getMemberByMemberId(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setUserNo("userNo");
        member.setCustNo("custNo");
        member.setCappCustNo("cappCustNo");
        member.setMemberName("orderPlaceUserName");
//        when(mockMemberFeignClient.getMemberByMemberId(0)).thenReturn(member);

        // Configure OrderCreateHelper.getStoreAreaCodeByAddress(...).
        final OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName("receiverName");
        addressDTO.setReceiverMobile("receiverMobile");
        addressDTO.setProvince("province");
        addressDTO.setCity("city");
        addressDTO.setCityCode("cityCode");
        addressDTO.setDistrict("district");
        addressDTO.setTown("town");
        addressDTO.setDetailAddress("detailAddress");
        final Member member1 = new Member();
        member1.setMemberId(0);
        member1.setUserNo("userNo");
        member1.setCustNo("custNo");
        member1.setCappCustNo("cappCustNo");
        member1.setMemberName("orderPlaceUserName");
//        when(mockOrderCreateHelper.getStoreAreaCodeByAddress("storeId", addressDTO, member1)).thenReturn("areaCode");

        // Configure ICartService.buildCartList(...).
        final OrderSkuInfoDTO orderSkuInfoDTO1 = new OrderSkuInfoDTO();
        orderSkuInfoDTO1.setProductId(0L);
        orderSkuInfoDTO1.setNumber(0);
        orderSkuInfoDTO1.setTaxPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO1.setFinanceRuleCode("financeRuleCode");
        orderSkuInfoDTO1.setAgriServiceFees(new BigDecimal("0.00"));
        final List<OrderSkuInfoDTO> skuInfoList = Arrays.asList(orderSkuInfoDTO1);
        final OrderAddressDTO addressDTO1 = new OrderAddressDTO();
        addressDTO1.setReceiverName("receiverName");
        addressDTO1.setReceiverMobile("receiverMobile");
        addressDTO1.setProvince("province");
        addressDTO1.setCity("city");
        addressDTO1.setCityCode("cityCode");
        addressDTO1.setDistrict("district");
        addressDTO1.setTown("town");
        addressDTO1.setDetailAddress("detailAddress");
//        when(mockCartService.buildCartList(skuInfoList, OrderTypeEnum.NORMAL, 0, "areaCode", "financeRuleCode",
//                "channel", addressDTO1)).thenReturn(Collections.emptyList());

        // Configure OrderSubmitUtil.getOrderSubmitDTOV2(...).
        final Cart cart = new Cart();
        cart.setCartId(0);
        cart.setMemberId(0);
        cart.setStoreId(0L);
        cart.setStoreName("storeName");
        cart.setGoodsId(0L);
        final OrderSubmitParamDTO orderSubmitParamDTO = new OrderSubmitParamDTO();
        orderSubmitParamDTO.setChannel("channel");
        orderSubmitParamDTO.setUsrNo("userNo");
        orderSubmitParamDTO.setMemberId(0);
        orderSubmitParamDTO.setSource(0);
        orderSubmitParamDTO.setOrderFrom(0);
        orderSubmitParamDTO.setAddressId(0);
        orderSubmitParamDTO.setAreaCode("areaCode");
        orderSubmitParamDTO.setIsCart(false);
        orderSubmitParamDTO.setProductType(0);
        orderSubmitParamDTO.setIsAloneBuy(false);
        orderSubmitParamDTO.setOrderPattern(0);
        orderSubmitParamDTO.setGroupBuyingTag(0);
        orderSubmitParamDTO.setProductId(0L);
        orderSubmitParamDTO.setNumber(0);
        orderSubmitParamDTO.setPno("pno");
        orderSubmitParamDTO.setVerifyCode("verifyCode");
        orderSubmitParamDTO.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress = new OrderAddressDTO();
        orderAddress.setReceiverName("receiverName");
        orderAddress.setReceiverMobile("receiverMobile");
        orderAddress.setProvince("province");
        orderAddress.setCity("city");
        orderAddress.setCityCode("cityCode");
        orderAddress.setDistrict("district");
        orderAddress.setTown("town");
        orderAddress.setDetailAddress("detailAddress");
        orderSubmitParamDTO.setOrderAddress(orderAddress);
        final OrderAddressDTO purchaseOrderAddress = new OrderAddressDTO();
        purchaseOrderAddress.setReceiverName("receiverName");
        purchaseOrderAddress.setReceiverMobile("receiverMobile");
        purchaseOrderAddress.setProvince("province");
        purchaseOrderAddress.setCity("city");
        purchaseOrderAddress.setCityCode("cityCode");
        purchaseOrderAddress.setDistrict("district");
        purchaseOrderAddress.setTown("town");
        purchaseOrderAddress.setDetailAddress("detailAddress");
        orderSubmitParamDTO.setPurchaseOrderAddress(purchaseOrderAddress);
        orderSubmitParamDTO.setChannelOrder(false);
        orderSubmitParamDTO.setBankTransferable(false);
        orderSubmitParamDTO.setOrderType(OrderTypeEnum.NORMAL);
        final OrderPromotionParamDTO orderPromotionInfo = new OrderPromotionParamDTO();
        orderSubmitParamDTO.setOrderPromotionInfo(orderPromotionInfo);
        orderSubmitParamDTO.setShareResource(false);
        orderSubmitParamDTO.setPlacingCombinationFlag(false);
        orderSubmitParamDTO.setOrderPlaceUserRole(OrderPlaceUserRole.SELF);
        orderSubmitParamDTO.setLoanPayer("loanPayer");
        orderSubmitParamDTO.setLoanConfirmMethod("loanConfirmMethod");
        orderSubmitParamDTO.setAttachmentUrls(Arrays.asList("value"));
//        final OrderSubmitDTO orderSubmitDTO = new OrderSubmitDTO(Arrays.asList(cart), orderSubmitParamDTO);
        final CartPO cartPO = new CartPO();
        cartPO.setCartId(0);
        cartPO.setMemberId(0);
        cartPO.setStoreId(0L);
        cartPO.setStoreName("storeName");
        cartPO.setAreaCode("areaCode");
        final List<CartPO> cartPOList = Arrays.asList(cartPO);
        final OrderSubmitParamDTO dto1 = new OrderSubmitParamDTO();
        dto1.setChannel("channel");
        dto1.setUsrNo("userNo");
        dto1.setMemberId(0);
        dto1.setSource(0);
        dto1.setOrderFrom(0);
        dto1.setAddressId(0);
        dto1.setAreaCode("areaCode");
        dto1.setIsCart(false);
        dto1.setProductType(0);
        dto1.setIsAloneBuy(false);
        dto1.setOrderPattern(0);
        dto1.setGroupBuyingTag(0);
        dto1.setProductId(0L);
        dto1.setNumber(0);
        dto1.setPno("pno");
        dto1.setVerifyCode("verifyCode");
        dto1.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress1 = new OrderAddressDTO();
        orderAddress1.setReceiverName("receiverName");
        orderAddress1.setReceiverMobile("receiverMobile");
        orderAddress1.setProvince("province");
        orderAddress1.setCity("city");
        orderAddress1.setCityCode("cityCode");
        orderAddress1.setDistrict("district");
        orderAddress1.setTown("town");
        orderAddress1.setDetailAddress("detailAddress");
        dto1.setOrderAddress(orderAddress1);
        final OrderAddressDTO purchaseOrderAddress1 = new OrderAddressDTO();
        purchaseOrderAddress1.setReceiverName("receiverName");
        purchaseOrderAddress1.setReceiverMobile("receiverMobile");
        purchaseOrderAddress1.setProvince("province");
        purchaseOrderAddress1.setCity("city");
        purchaseOrderAddress1.setCityCode("cityCode");
        purchaseOrderAddress1.setDistrict("district");
        purchaseOrderAddress1.setTown("town");
        purchaseOrderAddress1.setDetailAddress("detailAddress");
        dto1.setPurchaseOrderAddress(purchaseOrderAddress1);
        dto1.setChannelOrder(false);
        dto1.setBankTransferable(false);
        dto1.setOrderType(OrderTypeEnum.NORMAL);
        final OrderPromotionParamDTO orderPromotionInfo1 = new OrderPromotionParamDTO();
        dto1.setOrderPromotionInfo(orderPromotionInfo1);
        dto1.setShareResource(false);
        dto1.setPlacingCombinationFlag(false);
        dto1.setOrderPlaceUserRole(OrderPlaceUserRole.SELF);
        dto1.setLoanPayer("loanPayer");
        dto1.setLoanConfirmMethod("loanConfirmMethod");
        dto1.setAttachmentUrls(Arrays.asList("value"));
//        when(mockOrderSubmitUtil.getOrderSubmitDTOV2(cartPOList, dto1, true)).thenReturn(orderSubmitDTO);

//        when(mockShardingId.next(SeqEnum.PNO, "memberId")).thenReturn(0L);
//        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Configure OrderModel.submit(...).
        final Cart cart1 = new Cart();
        cart1.setCartId(0);
        cart1.setMemberId(0);
        cart1.setStoreId(0L);
        cart1.setStoreName("storeName");
        cart1.setGoodsId(0L);
        final OrderSubmitParamDTO orderSubmitParamDTO1 = new OrderSubmitParamDTO();
        orderSubmitParamDTO1.setChannel("channel");
        orderSubmitParamDTO1.setUsrNo("userNo");
        orderSubmitParamDTO1.setMemberId(0);
        orderSubmitParamDTO1.setSource(0);
        orderSubmitParamDTO1.setOrderFrom(0);
        orderSubmitParamDTO1.setAddressId(0);
        orderSubmitParamDTO1.setAreaCode("areaCode");
        orderSubmitParamDTO1.setIsCart(false);
        orderSubmitParamDTO1.setProductType(0);
        orderSubmitParamDTO1.setIsAloneBuy(false);
        orderSubmitParamDTO1.setOrderPattern(0);
        orderSubmitParamDTO1.setGroupBuyingTag(0);
        orderSubmitParamDTO1.setProductId(0L);
        orderSubmitParamDTO1.setNumber(0);
        orderSubmitParamDTO1.setPno("pno");
        orderSubmitParamDTO1.setVerifyCode("verifyCode");
        orderSubmitParamDTO1.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress2 = new OrderAddressDTO();
        orderAddress2.setReceiverName("receiverName");
        orderAddress2.setReceiverMobile("receiverMobile");
        orderAddress2.setProvince("province");
        orderAddress2.setCity("city");
        orderAddress2.setCityCode("cityCode");
        orderAddress2.setDistrict("district");
        orderAddress2.setTown("town");
        orderAddress2.setDetailAddress("detailAddress");
        orderSubmitParamDTO1.setOrderAddress(orderAddress2);
        final OrderAddressDTO purchaseOrderAddress2 = new OrderAddressDTO();
        purchaseOrderAddress2.setReceiverName("receiverName");
        purchaseOrderAddress2.setReceiverMobile("receiverMobile");
        purchaseOrderAddress2.setProvince("province");
        purchaseOrderAddress2.setCity("city");
        purchaseOrderAddress2.setCityCode("cityCode");
        purchaseOrderAddress2.setDistrict("district");
        purchaseOrderAddress2.setTown("town");
        purchaseOrderAddress2.setDetailAddress("detailAddress");
        orderSubmitParamDTO1.setPurchaseOrderAddress(purchaseOrderAddress2);
        orderSubmitParamDTO1.setChannelOrder(false);
        orderSubmitParamDTO1.setBankTransferable(false);
        orderSubmitParamDTO1.setOrderType(OrderTypeEnum.NORMAL);
        final OrderPromotionParamDTO orderPromotionInfo2 = new OrderPromotionParamDTO();
        orderSubmitParamDTO1.setOrderPromotionInfo(orderPromotionInfo2);
        orderSubmitParamDTO1.setShareResource(false);
        orderSubmitParamDTO1.setPlacingCombinationFlag(false);
        orderSubmitParamDTO1.setOrderPlaceUserRole(OrderPlaceUserRole.SELF);
        orderSubmitParamDTO1.setLoanPayer("loanPayer");
        orderSubmitParamDTO1.setLoanConfirmMethod("loanConfirmMethod");
        orderSubmitParamDTO1.setAttachmentUrls(Arrays.asList("value"));
//        final OrderSubmitDTO orderSubmitDTO1 = new OrderSubmitDTO(Arrays.asList(cart1), orderSubmitParamDTO1);
        final Member member2 = new Member();
        member2.setMemberId(0);
        member2.setUserNo("userNo");
        member2.setCustNo("custNo");
        member2.setCappCustNo("cappCustNo");
        member2.setMemberName("orderPlaceUserName");
        final OrderSubmitMqConsumerDTO consumerDTO = new OrderSubmitMqConsumerDTO();
        final OrderSubmitParamDTO paramDTO = new OrderSubmitParamDTO();
        paramDTO.setChannel("channel");
        paramDTO.setUsrNo("userNo");
        paramDTO.setMemberId(0);
        paramDTO.setSource(0);
        paramDTO.setOrderFrom(0);
        paramDTO.setAddressId(0);
        paramDTO.setAreaCode("areaCode");
        paramDTO.setIsCart(false);
        paramDTO.setProductType(0);
        paramDTO.setIsAloneBuy(false);
        paramDTO.setOrderPattern(0);
        paramDTO.setGroupBuyingTag(0);
        paramDTO.setProductId(0L);
        paramDTO.setNumber(0);
        paramDTO.setPno("pno");
        paramDTO.setVerifyCode("verifyCode");
        paramDTO.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress3 = new OrderAddressDTO();
        orderAddress3.setProvince("province");
        orderAddress3.setCity("city");
        orderAddress3.setCityCode("cityCode");
        orderAddress3.setDistrict("district");
        orderAddress3.setDetailAddress("detailAddress");
        paramDTO.setOrderAddress(orderAddress3);
        final OrderAddressDTO purchaseOrderAddress3 = new OrderAddressDTO();
        purchaseOrderAddress3.setProvince("province");
        purchaseOrderAddress3.setCity("city");
        purchaseOrderAddress3.setCityCode("cityCode");
        purchaseOrderAddress3.setDistrict("district");
        purchaseOrderAddress3.setDetailAddress("detailAddress");
        paramDTO.setPurchaseOrderAddress(purchaseOrderAddress3);
        paramDTO.setChannelOrder(false);
        paramDTO.setBankTransferable(false);
        paramDTO.setOrderType(OrderTypeEnum.NORMAL);
        final OrderPromotionParamDTO orderPromotionInfo3 = new OrderPromotionParamDTO();
        paramDTO.setOrderPromotionInfo(orderPromotionInfo3);
        paramDTO.setShareResource(false);
        paramDTO.setPlacingCombinationFlag(false);
        paramDTO.setOrderPlaceUserRole(OrderPlaceUserRole.SELF);
        consumerDTO.setParamDTO(paramDTO);
        consumerDTO.setMemberId(0);
        consumerDTO.setUserNo("userNo");
        consumerDTO.setPaySn("paySn");
        final PreOrderDTO preOrderDTO = new PreOrderDTO();
        preOrderDTO.setIsCalculateDiscount(false);
        preOrderDTO.setOrderType(0);
        consumerDTO.setPreOrderDTO(preOrderDTO);
        consumerDTO.setAreaCode("areaCode");
        final OrderSkuInfoDTO orderSkuInfoDTO2 = new OrderSkuInfoDTO();
        orderSkuInfoDTO2.setProductId(0L);
        orderSkuInfoDTO2.setNumber(0);
        orderSkuInfoDTO2.setTaxPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO2.setFinanceRuleCode("financeRuleCode");
        orderSkuInfoDTO2.setAgriServiceFees(new BigDecimal("0.00"));
        consumerDTO.setSkuInfoList(Arrays.asList(orderSkuInfoDTO2));
        consumerDTO.setOrderType(OrderTypeEnum.NORMAL);
        final ChannelOrderSubmitDTO channelOrderSubmitDTO = new ChannelOrderSubmitDTO();
        channelOrderSubmitDTO.setChannel("channel");
        channelOrderSubmitDTO.setOutBizSource("outBizSource");
        channelOrderSubmitDTO.setOutBizId("outBizId");
        consumerDTO.setChannelOrderSubmitDTO(channelOrderSubmitDTO);
        final OrderOfflineParamDTO orderOfflineParamDTO = new OrderOfflineParamDTO();
        orderOfflineParamDTO.setOrderPattern(0);
        orderOfflineParamDTO.setEmployeeCode("employeeCode");
        final OrderAddressDTO address1 = new OrderAddressDTO();
        address1.setProvince("province");
        address1.setCity("city");
        address1.setCityCode("cityCode");
        address1.setDistrict("district");
        address1.setDetailAddress("detailAddress");
        orderOfflineParamDTO.setAddress(address1);
        orderOfflineParamDTO.setPointId(0L);
        final OrderOfflineDTO orderOfflineDTO = new OrderOfflineDTO();
        orderOfflineParamDTO.setOrderOfflineList(Arrays.asList(orderOfflineDTO));
        final OrderOfflineInfoDTO offlineInfoDTO = new OrderOfflineInfoDTO();
        orderOfflineParamDTO.setOfflineInfoDTO(offlineInfoDTO);
        orderOfflineParamDTO.setUserMobile("memberMobile");
        orderOfflineParamDTO.setIsContinueSubmit(false);
        consumerDTO.setOrderOfflineParamDTO(orderOfflineParamDTO);
        final GroupOrderProductSubmitDTO groupOrderProductSubmitDTO = new GroupOrderProductSubmitDTO();
        consumerDTO.setGroupOrderProductSubmitDTO(groupOrderProductSubmitDTO);
//        when(mockOrderModel.submit(orderSubmitDTO1, member2, consumerDTO)).thenReturn(Arrays.asList("value"));

        // Run the test
//        final OrderSubmitVO result = placingOrderServiceImplUnderTest.createRebateGiftOrder(dto);

        // Verify the results
//        assertEquals(expectedResult, result);
//        verify(mockStringRedisTemplate).expire("idempotent", 7L, TimeUnit.DAYS);
    }

    @Test
    public void testCreateRebateGiftOrder_OrderModelReturnsNoItems() {
        // Setup
        final RebateOrderSubmitDTO dto = new RebateOrderSubmitDTO();
        dto.setIdempotent("idempotent");
        dto.setChannel("channel");
        dto.setStoreId(0L);
        final OrderSkuInfoDTO orderSkuInfoDTO = new OrderSkuInfoDTO();
        orderSkuInfoDTO.setProductId(0L);
        orderSkuInfoDTO.setNumber(0);
        orderSkuInfoDTO.setTaxPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO.setFinanceRuleCode("financeRuleCode");
        orderSkuInfoDTO.setAgriServiceFees(new BigDecimal("0.00"));
        dto.setSkuInfoList(Arrays.asList(orderSkuInfoDTO));
        final OrderAddressDTO address = new OrderAddressDTO();
        address.setReceiverName("receiverName");
        address.setReceiverMobile("receiverMobile");
        address.setProvince("province");
        address.setCity("city");
        address.setCityCode("cityCode");
        address.setDistrict("district");
        address.setTown("town");
        address.setDetailAddress("detailAddress");
        dto.setAddress(address);
        dto.setPointId(0L);
        dto.setMemberId(0L);

        final OrderSubmitVO expectedResult = new OrderSubmitVO();
        expectedResult.setPaySn("paySn");
        expectedResult.setOrderSnList(Arrays.asList("value"));

        // Configure OrderCreateHelper.buildOrderAddressByPointId(...).
        final OrderAddressDTO orderAddressDTO = new OrderAddressDTO();
        orderAddressDTO.setReceiverName("receiverName");
        orderAddressDTO.setReceiverMobile("receiverMobile");
        orderAddressDTO.setProvince("province");
        orderAddressDTO.setCity("city");
        orderAddressDTO.setCityCode("cityCode");
        orderAddressDTO.setDistrict("district");
        orderAddressDTO.setTown("town");
        orderAddressDTO.setDetailAddress("detailAddress");
//        when(mockOrderCreateHelper.buildOrderAddressByPointId(0L)).thenReturn(orderAddressDTO);

        // Configure MemberFeignClient.getMemberByMemberId(...).
        final Member member = new Member();
        member.setMemberId(0);
        member.setUserNo("userNo");
        member.setCustNo("custNo");
        member.setCappCustNo("cappCustNo");
        member.setMemberName("orderPlaceUserName");
//        when(mockMemberFeignClient.getMemberByMemberId(0)).thenReturn(member);

        // Configure OrderCreateHelper.getStoreAreaCodeByAddress(...).
        final OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceiverName("receiverName");
        addressDTO.setReceiverMobile("receiverMobile");
        addressDTO.setProvince("province");
        addressDTO.setCity("city");
        addressDTO.setCityCode("cityCode");
        addressDTO.setDistrict("district");
        addressDTO.setTown("town");
        addressDTO.setDetailAddress("detailAddress");
        final Member member1 = new Member();
        member1.setMemberId(0);
        member1.setUserNo("userNo");
        member1.setCustNo("custNo");
        member1.setCappCustNo("cappCustNo");
        member1.setMemberName("orderPlaceUserName");
//        when(mockOrderCreateHelper.getStoreAreaCodeByAddress("storeId", addressDTO, member1)).thenReturn("areaCode");

        // Configure ICartService.buildCartList(...).
        final CartPO cartPO = new CartPO();
        cartPO.setCartId(0);
        cartPO.setMemberId(0);
        cartPO.setStoreId(0L);
        cartPO.setStoreName("storeName");
        cartPO.setAreaCode("areaCode");
        final List<CartPO> cartPOS = Arrays.asList(cartPO);
        final OrderSkuInfoDTO orderSkuInfoDTO1 = new OrderSkuInfoDTO();
        orderSkuInfoDTO1.setProductId(0L);
        orderSkuInfoDTO1.setNumber(0);
        orderSkuInfoDTO1.setTaxPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO1.setFinanceRuleCode("financeRuleCode");
        orderSkuInfoDTO1.setAgriServiceFees(new BigDecimal("0.00"));
        final List<OrderSkuInfoDTO> skuInfoList = Arrays.asList(orderSkuInfoDTO1);
        final OrderAddressDTO addressDTO1 = new OrderAddressDTO();
        addressDTO1.setReceiverName("receiverName");
        addressDTO1.setReceiverMobile("receiverMobile");
        addressDTO1.setProvince("province");
        addressDTO1.setCity("city");
        addressDTO1.setCityCode("cityCode");
        addressDTO1.setDistrict("district");
        addressDTO1.setTown("town");
        addressDTO1.setDetailAddress("detailAddress");
//        when(mockCartService.buildCartList(skuInfoList, OrderTypeEnum.NORMAL, 0, "areaCode", "financeRuleCode",
//                "channel", addressDTO1)).thenReturn(cartPOS);

        // Configure OrderSubmitUtil.getOrderSubmitDTOV2(...).
        final Cart cart = new Cart();
        cart.setCartId(0);
        cart.setMemberId(0);
        cart.setStoreId(0L);
        cart.setStoreName("storeName");
        cart.setGoodsId(0L);
        final OrderSubmitParamDTO orderSubmitParamDTO = new OrderSubmitParamDTO();
        orderSubmitParamDTO.setChannel("channel");
        orderSubmitParamDTO.setUsrNo("userNo");
        orderSubmitParamDTO.setMemberId(0);
        orderSubmitParamDTO.setSource(0);
        orderSubmitParamDTO.setOrderFrom(0);
        orderSubmitParamDTO.setAddressId(0);
        orderSubmitParamDTO.setAreaCode("areaCode");
        orderSubmitParamDTO.setIsCart(false);
        orderSubmitParamDTO.setProductType(0);
        orderSubmitParamDTO.setIsAloneBuy(false);
        orderSubmitParamDTO.setOrderPattern(0);
        orderSubmitParamDTO.setGroupBuyingTag(0);
        orderSubmitParamDTO.setProductId(0L);
        orderSubmitParamDTO.setNumber(0);
        orderSubmitParamDTO.setPno("pno");
        orderSubmitParamDTO.setVerifyCode("verifyCode");
        orderSubmitParamDTO.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress = new OrderAddressDTO();
        orderAddress.setReceiverName("receiverName");
        orderAddress.setReceiverMobile("receiverMobile");
        orderAddress.setProvince("province");
        orderAddress.setCity("city");
        orderAddress.setCityCode("cityCode");
        orderAddress.setDistrict("district");
        orderAddress.setTown("town");
        orderAddress.setDetailAddress("detailAddress");
        orderSubmitParamDTO.setOrderAddress(orderAddress);
        final OrderAddressDTO purchaseOrderAddress = new OrderAddressDTO();
        purchaseOrderAddress.setReceiverName("receiverName");
        purchaseOrderAddress.setReceiverMobile("receiverMobile");
        purchaseOrderAddress.setProvince("province");
        purchaseOrderAddress.setCity("city");
        purchaseOrderAddress.setCityCode("cityCode");
        purchaseOrderAddress.setDistrict("district");
        purchaseOrderAddress.setTown("town");
        purchaseOrderAddress.setDetailAddress("detailAddress");
        orderSubmitParamDTO.setPurchaseOrderAddress(purchaseOrderAddress);
        orderSubmitParamDTO.setChannelOrder(false);
        orderSubmitParamDTO.setBankTransferable(false);
        orderSubmitParamDTO.setOrderType(OrderTypeEnum.NORMAL);
        final OrderPromotionParamDTO orderPromotionInfo = new OrderPromotionParamDTO();
        orderSubmitParamDTO.setOrderPromotionInfo(orderPromotionInfo);
        orderSubmitParamDTO.setShareResource(false);
        orderSubmitParamDTO.setPlacingCombinationFlag(false);
        orderSubmitParamDTO.setOrderPlaceUserRole(OrderPlaceUserRole.SELF);
        orderSubmitParamDTO.setLoanPayer("loanPayer");
        orderSubmitParamDTO.setLoanConfirmMethod("loanConfirmMethod");
        orderSubmitParamDTO.setAttachmentUrls(Arrays.asList("value"));
//        final OrderSubmitDTO orderSubmitDTO = new OrderSubmitDTO(Arrays.asList(cart), orderSubmitParamDTO);
        final CartPO cartPO1 = new CartPO();
        cartPO1.setCartId(0);
        cartPO1.setMemberId(0);
        cartPO1.setStoreId(0L);
        cartPO1.setStoreName("storeName");
        cartPO1.setAreaCode("areaCode");
        final List<CartPO> cartPOList = Arrays.asList(cartPO1);
        final OrderSubmitParamDTO dto1 = new OrderSubmitParamDTO();
        dto1.setChannel("channel");
        dto1.setUsrNo("userNo");
        dto1.setMemberId(0);
        dto1.setSource(0);
        dto1.setOrderFrom(0);
        dto1.setAddressId(0);
        dto1.setAreaCode("areaCode");
        dto1.setIsCart(false);
        dto1.setProductType(0);
        dto1.setIsAloneBuy(false);
        dto1.setOrderPattern(0);
        dto1.setGroupBuyingTag(0);
        dto1.setProductId(0L);
        dto1.setNumber(0);
        dto1.setPno("pno");
        dto1.setVerifyCode("verifyCode");
        dto1.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress1 = new OrderAddressDTO();
        orderAddress1.setReceiverName("receiverName");
        orderAddress1.setReceiverMobile("receiverMobile");
        orderAddress1.setProvince("province");
        orderAddress1.setCity("city");
        orderAddress1.setCityCode("cityCode");
        orderAddress1.setDistrict("district");
        orderAddress1.setTown("town");
        orderAddress1.setDetailAddress("detailAddress");
        dto1.setOrderAddress(orderAddress1);
        final OrderAddressDTO purchaseOrderAddress1 = new OrderAddressDTO();
        purchaseOrderAddress1.setReceiverName("receiverName");
        purchaseOrderAddress1.setReceiverMobile("receiverMobile");
        purchaseOrderAddress1.setProvince("province");
        purchaseOrderAddress1.setCity("city");
        purchaseOrderAddress1.setCityCode("cityCode");
        purchaseOrderAddress1.setDistrict("district");
        purchaseOrderAddress1.setTown("town");
        purchaseOrderAddress1.setDetailAddress("detailAddress");
        dto1.setPurchaseOrderAddress(purchaseOrderAddress1);
        dto1.setChannelOrder(false);
        dto1.setBankTransferable(false);
        dto1.setOrderType(OrderTypeEnum.NORMAL);
        final OrderPromotionParamDTO orderPromotionInfo1 = new OrderPromotionParamDTO();
        dto1.setOrderPromotionInfo(orderPromotionInfo1);
        dto1.setShareResource(false);
        dto1.setPlacingCombinationFlag(false);
        dto1.setOrderPlaceUserRole(OrderPlaceUserRole.SELF);
        dto1.setLoanPayer("loanPayer");
        dto1.setLoanConfirmMethod("loanConfirmMethod");
        dto1.setAttachmentUrls(Arrays.asList("value"));
//        when(mockOrderSubmitUtil.getOrderSubmitDTOV2(cartPOList, dto1, true)).thenReturn(orderSubmitDTO);

//        when(mockShardingId.next(SeqEnum.PNO, "memberId")).thenReturn(0L);
//        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Configure OrderModel.submit(...).
        final Cart cart1 = new Cart();
        cart1.setCartId(0);
        cart1.setMemberId(0);
        cart1.setStoreId(0L);
        cart1.setStoreName("storeName");
        cart1.setGoodsId(0L);
        final OrderSubmitParamDTO orderSubmitParamDTO1 = new OrderSubmitParamDTO();
        orderSubmitParamDTO1.setChannel("channel");
        orderSubmitParamDTO1.setUsrNo("userNo");
        orderSubmitParamDTO1.setMemberId(0);
        orderSubmitParamDTO1.setSource(0);
        orderSubmitParamDTO1.setOrderFrom(0);
        orderSubmitParamDTO1.setAddressId(0);
        orderSubmitParamDTO1.setAreaCode("areaCode");
        orderSubmitParamDTO1.setIsCart(false);
        orderSubmitParamDTO1.setProductType(0);
        orderSubmitParamDTO1.setIsAloneBuy(false);
        orderSubmitParamDTO1.setOrderPattern(0);
        orderSubmitParamDTO1.setGroupBuyingTag(0);
        orderSubmitParamDTO1.setProductId(0L);
        orderSubmitParamDTO1.setNumber(0);
        orderSubmitParamDTO1.setPno("pno");
        orderSubmitParamDTO1.setVerifyCode("verifyCode");
        orderSubmitParamDTO1.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress2 = new OrderAddressDTO();
        orderAddress2.setReceiverName("receiverName");
        orderAddress2.setReceiverMobile("receiverMobile");
        orderAddress2.setProvince("province");
        orderAddress2.setCity("city");
        orderAddress2.setCityCode("cityCode");
        orderAddress2.setDistrict("district");
        orderAddress2.setTown("town");
        orderAddress2.setDetailAddress("detailAddress");
        orderSubmitParamDTO1.setOrderAddress(orderAddress2);
        final OrderAddressDTO purchaseOrderAddress2 = new OrderAddressDTO();
        purchaseOrderAddress2.setReceiverName("receiverName");
        purchaseOrderAddress2.setReceiverMobile("receiverMobile");
        purchaseOrderAddress2.setProvince("province");
        purchaseOrderAddress2.setCity("city");
        purchaseOrderAddress2.setCityCode("cityCode");
        purchaseOrderAddress2.setDistrict("district");
        purchaseOrderAddress2.setTown("town");
        purchaseOrderAddress2.setDetailAddress("detailAddress");
        orderSubmitParamDTO1.setPurchaseOrderAddress(purchaseOrderAddress2);
        orderSubmitParamDTO1.setChannelOrder(false);
        orderSubmitParamDTO1.setBankTransferable(false);
        orderSubmitParamDTO1.setOrderType(OrderTypeEnum.NORMAL);
        final OrderPromotionParamDTO orderPromotionInfo2 = new OrderPromotionParamDTO();
        orderSubmitParamDTO1.setOrderPromotionInfo(orderPromotionInfo2);
        orderSubmitParamDTO1.setShareResource(false);
        orderSubmitParamDTO1.setPlacingCombinationFlag(false);
        orderSubmitParamDTO1.setOrderPlaceUserRole(OrderPlaceUserRole.SELF);
        orderSubmitParamDTO1.setLoanPayer("loanPayer");
        orderSubmitParamDTO1.setLoanConfirmMethod("loanConfirmMethod");
        orderSubmitParamDTO1.setAttachmentUrls(Arrays.asList("value"));
//        final OrderSubmitDTO orderSubmitDTO1 = new OrderSubmitDTO(Arrays.asList(cart1), orderSubmitParamDTO1);
        final Member member2 = new Member();
        member2.setMemberId(0);
        member2.setUserNo("userNo");
        member2.setCustNo("custNo");
        member2.setCappCustNo("cappCustNo");
        member2.setMemberName("orderPlaceUserName");
        final OrderSubmitMqConsumerDTO consumerDTO = new OrderSubmitMqConsumerDTO();
        final OrderSubmitParamDTO paramDTO = new OrderSubmitParamDTO();
        paramDTO.setChannel("channel");
        paramDTO.setUsrNo("userNo");
        paramDTO.setMemberId(0);
        paramDTO.setSource(0);
        paramDTO.setOrderFrom(0);
        paramDTO.setAddressId(0);
        paramDTO.setAreaCode("areaCode");
        paramDTO.setIsCart(false);
        paramDTO.setProductType(0);
        paramDTO.setIsAloneBuy(false);
        paramDTO.setOrderPattern(0);
        paramDTO.setGroupBuyingTag(0);
        paramDTO.setProductId(0L);
        paramDTO.setNumber(0);
        paramDTO.setPno("pno");
        paramDTO.setVerifyCode("verifyCode");
        paramDTO.setFinanceRuleCode("financeRuleCode");
        final OrderAddressDTO orderAddress3 = new OrderAddressDTO();
        orderAddress3.setProvince("province");
        orderAddress3.setCity("city");
        orderAddress3.setCityCode("cityCode");
        orderAddress3.setDistrict("district");
        orderAddress3.setDetailAddress("detailAddress");
        paramDTO.setOrderAddress(orderAddress3);
        final OrderAddressDTO purchaseOrderAddress3 = new OrderAddressDTO();
        purchaseOrderAddress3.setProvince("province");
        purchaseOrderAddress3.setCity("city");
        purchaseOrderAddress3.setCityCode("cityCode");
        purchaseOrderAddress3.setDistrict("district");
        purchaseOrderAddress3.setDetailAddress("detailAddress");
        paramDTO.setPurchaseOrderAddress(purchaseOrderAddress3);
        paramDTO.setChannelOrder(false);
        paramDTO.setBankTransferable(false);
        paramDTO.setOrderType(OrderTypeEnum.NORMAL);
        final OrderPromotionParamDTO orderPromotionInfo3 = new OrderPromotionParamDTO();
        paramDTO.setOrderPromotionInfo(orderPromotionInfo3);
        paramDTO.setShareResource(false);
        paramDTO.setPlacingCombinationFlag(false);
        paramDTO.setOrderPlaceUserRole(OrderPlaceUserRole.SELF);
        consumerDTO.setParamDTO(paramDTO);
        consumerDTO.setMemberId(0);
        consumerDTO.setUserNo("userNo");
        consumerDTO.setPaySn("paySn");
        final PreOrderDTO preOrderDTO = new PreOrderDTO();
        preOrderDTO.setIsCalculateDiscount(false);
        preOrderDTO.setOrderType(0);
        consumerDTO.setPreOrderDTO(preOrderDTO);
        consumerDTO.setAreaCode("areaCode");
        final OrderSkuInfoDTO orderSkuInfoDTO2 = new OrderSkuInfoDTO();
        orderSkuInfoDTO2.setProductId(0L);
        orderSkuInfoDTO2.setNumber(0);
        orderSkuInfoDTO2.setTaxPrice(new BigDecimal("0.00"));
        orderSkuInfoDTO2.setFinanceRuleCode("financeRuleCode");
        orderSkuInfoDTO2.setAgriServiceFees(new BigDecimal("0.00"));
        consumerDTO.setSkuInfoList(Arrays.asList(orderSkuInfoDTO2));
        consumerDTO.setOrderType(OrderTypeEnum.NORMAL);
        final ChannelOrderSubmitDTO channelOrderSubmitDTO = new ChannelOrderSubmitDTO();
        channelOrderSubmitDTO.setChannel("channel");
        channelOrderSubmitDTO.setOutBizSource("outBizSource");
        channelOrderSubmitDTO.setOutBizId("outBizId");
        consumerDTO.setChannelOrderSubmitDTO(channelOrderSubmitDTO);
        final OrderOfflineParamDTO orderOfflineParamDTO = new OrderOfflineParamDTO();
        orderOfflineParamDTO.setOrderPattern(0);
        orderOfflineParamDTO.setEmployeeCode("employeeCode");
        final OrderAddressDTO address1 = new OrderAddressDTO();
        address1.setProvince("province");
        address1.setCity("city");
        address1.setCityCode("cityCode");
        address1.setDistrict("district");
        address1.setDetailAddress("detailAddress");
        orderOfflineParamDTO.setAddress(address1);
        orderOfflineParamDTO.setPointId(0L);
        final OrderOfflineDTO orderOfflineDTO = new OrderOfflineDTO();
        orderOfflineParamDTO.setOrderOfflineList(Arrays.asList(orderOfflineDTO));
        final OrderOfflineInfoDTO offlineInfoDTO = new OrderOfflineInfoDTO();
        orderOfflineParamDTO.setOfflineInfoDTO(offlineInfoDTO);
        orderOfflineParamDTO.setUserMobile("memberMobile");
        orderOfflineParamDTO.setIsContinueSubmit(false);
        consumerDTO.setOrderOfflineParamDTO(orderOfflineParamDTO);
        final GroupOrderProductSubmitDTO groupOrderProductSubmitDTO = new GroupOrderProductSubmitDTO();
        consumerDTO.setGroupOrderProductSubmitDTO(groupOrderProductSubmitDTO);
//        when(mockOrderModel.submit(orderSubmitDTO1, member2, consumerDTO)).thenReturn(Collections.emptyList());

        // Run the test
//        final OrderSubmitVO result = placingOrderServiceImplUnderTest.createRebateGiftOrder(dto);

        // Verify the results
//        assertEquals(expectedResult, result);
//        verify(mockStringRedisTemplate).expire("idempotent", 7L, TimeUnit.DAYS);
    }
}
