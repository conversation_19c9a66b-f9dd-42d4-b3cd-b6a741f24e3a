spring:
  redis:
    host: r-2zef4d5b5cdab4e4.redis.rds.aliyuncs.com
    port: 6379
    password: Db123456
    database: 99
    timeout: 100000
    jedis:
      pool:
        max-wait: 2000ms
        min-idle: 2
        max-idle: 8
  rabbitmq:
    host: rabbitmq.tsg.cfpamf.com
    port: 5672
    username: admin
    password: donttelldev
    template:
      receive-timeout: 2000
      reply-timeout: 2000
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  cloud:
    nacos:
      discovery:
        namespace: 6a137026-68f4-4887-9ac6-4084187e9a98
        server-addr: http://nacoco.tsg.cfpamf.com
        group: MALL_GROUP
        username: nacos
        password: nacos

seata:
  application-id: ${spring.application.name}
  tx-service-group: seata_newmall_tx_group
  enableAutoDataSourceProxy: true
  config: #从配置中心获取seata service的配置
    type: nacos #type 默认为file
    nacos:
      serverAddr: http://nacoco.tsg.cfpamf.com
      group: SEATA_GROUP
      username: nacos
      password: nacos
  registry: # 从注册中心获取seata-server服务端
    type: nacos #type 默认为file
    nacos:
      application: seata-server
      server-addr: http://nacoco.tsg.cfpamf.com
      group: SEATA_GROUP
      username: nacos
      password: nacos


cfpamf:
  multiple:
    dataSource:
      enabled: true
  smartid:
    server: smartid.tsg.cfpamf.com
    token: 0f673adf80504e2eaa552f5d791b644c

  ##配置数据源
  jdbc:
    dataSource:
      masterdb:
        jdbcUrl: *********************************************************************************************************************************************************************
        username: cd_mall
        password: Cd_Mall1
#        #开发环境
#        jdbcUrl: *********************************************************************************************************************************************************************
#        username: finapp
#        password: FinApp
        hikariPool:
          maximumPoolSize: 10
          driverClassName: com.mysql.cj.jdbc.Driver
  ##配置mybatis plus
  mybatis:
    masterdb:
      basePackage: com.cfpamf.ms.mallorder.mapper
      typeAliasesPackage: com.cfpamf.ms.mallorder.po
      mapperLocations: classpath:mapper/**/*.xml
      configuration:
        # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
        default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
      type-enums-package: com.cfpamf.ms.mallorder.constant.enums
      pageProperties:
        overflow: true
        dialectType: mysql
      metaObjectHandler: com.cfpamf.ms.mallorder.common.handler.MyMetaObjectHandler
  ##配置swagger
  swagger:
    dockets:
      demo:
        groupName: 订单中心
        basePackage: com.cfpamf.ms.mallorder.controller
        author: 毛亮
        title: 订单中心

log4jdbc:
  sqltiming:
    warn:
      threshold: 300
    error:
      threshold: 2000
  dump.sql.select: true

xxl:
  job:
    version: 2.0
  newjob:
    admin:
      addresses: http://xxl-job2.tsg.cfpamf.com/xxl-job-admin
    executor:
      appname: ${spring.application.name}
      ip:
      port: 9911
      logpath: /data/applogs/xxl-job/jobhandler/
      logretentiondays: 5
    accessToken:

aliyun:
  img:
    url: https://mall-sld-test.oss-cn-beijing.aliyuncs.com/

mall-logistic:
  url: http://mall-logistic.tsg.cfpamf.com
mall-order-biz:
  url: http://mall-order.test-newmall
mall-payment:
  url: http://mall-payment.tsg.cfpamf.com
  notify: ${mall-order-biz.url}/front/orderPayCallback/notify
  refundNotify: ${mall-order-biz.url}/front/orderPayCallback/refundNotify
  loanNotify: ${mall-order-biz.url}/front/orderPayCallback/loanNotify
  wxpay-key: caa08fe60b014a14b5503fb2ea60aae1
ms-service-customer:
  url: ms-customer.tsg.cfpamf.com
platform-collection:
  account: **********
  name: 河北电子服务商

ms-bizconfig-service:
  url: http://ms-bizconfig.tsg.cfpamf.com/
ms-service-loan:
  url: http://ms-loan.tsg.cfpamf.com
bms:
  api:
    url: http://bms-service.tsg.cfpamf.com
core-trade-service:
  url: http://core-trade.tsg.cfpamf.com
hrms-salary:
  url: http://hrms-salary.tsg.cfpamf.com
bkTransfer:
  expire-url: https://2i1i.cn/g7Di
#hr服务
hrms-biz:
  url: http://hrms-biz.tsg.cfpamf.com
dbc-service:
  url: http://dbc-service.tsg.cfpamf.com
auditors:
  platform: ***********,***********
card-service:
  url: http://card-service.tsg.cfpamf.com
mall-biz:
  url: http://mall-biz.tsg.cfpamf.com
wms-service:
  url: http://wms-service.tsg.cfpamf.com
ms-promotion-service:
  url: http://ms-promotion-service.test-capp
ms-messagepush-service:
  url: http://ms-messagepush-service.test-capp
ding-talk:
  url: https://oapi.dingtalk.com/robot/send?access_token=ab8bdb02cfb902cab5c1d757732c00d6f85a09ea4d1a39ff42c2363ac43bc425
dts-center:
  url: http://dts-center.dsg.cfpamf.com
cashier-service:
  url: http://*************:10200
erp-service:
  url: http://erp-services.tsg.cfpamf.com/
oms-base-service:
  url: http://oms-base.tsg.cfpamf.com
dayEnd:
  start: 2330
  end: 100
enable:
  tcc: true

channel-fee-rate:
  mappedRate:
    2-WXPAY: 0.006
    3-ALIPAY: 0.006
    4-BANK_PAY: 0
    1-BANK_TRANSFER: 0
    1-CARD: 0
    1-CARD_VOUCHER: 0
    1-ENJOY_PAY: 0
    1-FOLLOW_HEART: 0
    1-ONLINE: 0
    5-WXPAY: 0.006
    5-ALIPAY: 0.002
    5-CARD: 0
    5-CARD_VOUCHER: 0
    5-ENJOY_PAY: 0
    5-FOLLOW_HEART: 0
    5-ONLINE: 0
    5-BANK_TRANSFER: 0

wx-combine-xzcard:
  orderList: [****************,****************,****************,****************,****************,****************,****************]

presell:
  paymethods:
    deposit: ALIPAY,WXPAY
    remain: ALIPAY,WXPAY,ENJOY_PAY,FOLLOW_HEART,CREDIT_PAY

order-rate-config:
  maxSharingRate: 30