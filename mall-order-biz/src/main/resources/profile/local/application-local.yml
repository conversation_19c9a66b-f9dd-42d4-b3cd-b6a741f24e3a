
spring:
  redis:
    host: r-2zef4d5b5cdab4e4.redis.rds.aliyuncs.com
    port: 6379
    password: Db123456
    database: 99
    timeout: 100000
    jedis:
      pool:
        max-wait: 2000ms
        min-idle: 2
        max-idle: 8
  rabbitmq:
    host: rabbitmq-serverless-cn-lmr3tw56v05.cn-beijing.amqp-22.vpc.mq.amqp.aliyuncs.com
    port: 5672
    username: MjpyYWJiaXRtcS1zZXJ2ZXJsZXNzLWNuLWxtcjN0dzU2djA1OkxUQUk1dFNZaW1DRHVpSFRWVk5lSGppVA==
    password: MDQ2QTFFMkY4ODc3MUU5QzA5NjE1NzQyMTFGQUIwQUEwMDk1REI3OToxNzIxMTE0NTE5MTg4
    template:
      receive-timeout: 2000
      reply-timeout: 2000
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  cloud:
    nacos:
      discovery:
        namespace: 6a137026-68f4-4887-9ac6-4084187e9a98
        server-addr: mse-b81bd222-nacos-ans.mse.aliyuncs.com
        group: MALL_GROUP
        username:
        password:
        #不注册到nacos
        register-enabled: false
seata:
  enabled: true
  application-id: ${spring.application.name}
  tx-service-group: seata_newmall_tx_group
  enableAutoDataSourceProxy: true
  config: #从配置中心获取seata service的配置
    type: nacos #type 默认为file
    nacos:
      serverAddr: mse-b81bd222-nacos-ans.mse.aliyuncs.com
      group: SEATA_GROUP
      username:
      password:
  registry: # 从注册中心获取seata-server服务端
    type: nacos #type 默认为file
    nacos:
      application: seata-server
      server-addr: mse-b81bd222-nacos-ans.mse.aliyuncs.com
      group: SEATA_GROUP
      username:
      password:
  client:
    rm:
      tableMetaCheckEnable: true

feign:
  client:
    config:
      default:
        # FeignClientConfiguration
        connectTimeout: 20000 # Feign的连接建立超时时间
        readTimeout: 20000 # Feign的请求处理超时时间
        loggerLevel: full #

cfpamf:
  multiple:
    dataSource:
      enabled: true
  smartid:
    server: smartid.tsg.cfpamf.com
    token: 0f673adf80504e2eaa552f5d791b644c

  ##配置数据源
  jdbc:
    dataSource:
      masterdb:
        jdbcUrl: ********************************************************************************************************************************************************************
        #        username: bbc
        #        password: Zhnx#BBC@T
        username: cd_mall
        password: Cd_Mall1
        hikariPool:
          maximumPoolSize: 10
          driverClassName: com.mysql.cj.jdbc.Driver
       
      pg:
        jdbcUrl: ******************************************************************************************************************
        username: devops
        password: devops123
        maxWait: 2000
        hikariPool:
          maximumPoolSize: 10
          driverClassName: org.postgresql.Driver
          connectionInitSql: SELECT 'X'
          
  ##配置mybatis plus
  mybatis:
    masterdb:
      basePackage: com.cfpamf.ms.mallorder.mapper
      typeAliasesPackage: com.cfpamf.ms.mallorder.po
      mapperLocations: classpath:mapper/**/*.xml
      configuration:
        # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
        default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
      type-enums-package: com.cfpamf.ms.mallorder.constant.enums
      pageProperties:
        overflow: true
        dialectType: mysql
      metaObjectHandler: com.cfpamf.ms.mallorder.common.handler.MyMetaObjectHandler
     
    pg:
      basePackage: com.cfpamf.ms.mallorder.pgMapper
      typeAliasesPackage: com.cfpamf.ms.mallorder.po.pgrpt
      mapperLocations: classpath:pgMapper/**/*.xml
      configuration:
        # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
        default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
      type-enums-package: com.cfpamf.ms.mallorder.common.enums.pgrpt
      pageProperties:
        overflow: true
        dialectType: postgresql 
      
  ##配置swagger
  swagger:
    dockets:
      demo:
        groupName: 订单中心
        basePackage: com.cfpamf.ms.mallorder.controller
        author: 毛亮
        title: 订单中心

  ##OSS配置
  oss:
    bucket: "mall-sld-test"
    endPoint: "https://oss-cn-beijing.aliyuncs.com"
    accessKeyId: "LTAI5tFeQgPuGg3fY6wycs1k"
    accessKeySecret: "******************************"
    oss-expiretime: 600

log4jdbc:
  sqltiming:
    warn:
      threshold: 300
    error:
      threshold: 2000
  dump.sql.select: true

xxl:
  job:
    version: 2.0
  newjob:
    admin:
      addresses: http://xxl-job2.tsg.cfpamf.com/xxl-job-admin
    executor:
      appname: ${spring.application.name}
      ip:
      port: 9911
      logpath: /data/applogs/xxl-job/jobhandler/
      logretentiondays: 5
    accessToken:


mall-logistic:
  #url: http://mall-logistic.tsg.cfpamf.com
  url: http://mall-logistic.tsg.cfpamf.com
mall-order-biz:
  url: http://mall-order.tsg.cfpamf.com
mall-payment:
  url: http://mall-payment.tsg.cfpamf.com
  notify: ${mall-order-biz.url}/front/orderPayCallback/notify
  refundNotify: ${mall-order-biz.url}/front/orderPayCallback/refundNotify
  loanNotify: ${mall-order-biz.url}/front/orderPayCallback/loanNotify
  wxpay-key: caa08fe60b014a14b5503fb2ea60aae1
ms-service-customer:
  url: ms-customer.tsg.cfpamf.com
platform-collection:
  account: **********
  name: 河北电子服务商

mall-goods:
  url: http://mall-goods.tsg.cfpamf.com
mall-shop:
  url: http://mall-shop.tsg.cfpamf.com
ms-bizconfig-service:
  url: http://ms-bizconfig.tsg.cfpamf.com/
ms-service-loan:
  url: http://ms-loan.tsg.cfpamf.com
agric-host-order:
  url: http://agric-host-order.tsg.cfpamf.com/
agric:
  crm:
    url: http://agric-crm.tsg.cfpamf.com/
bms:
  api:
    url: http://bms-service.tsg.cfpamf.com/
core-trade-service:
  url: http://core-trade.tsg.cfpamf.com
hrms-salary:
  url: http://hrms-salary.tsg.cfpamf.com
#hr服务
hrms-biz:
  url: http://hrms-biz.tsg.cfpamf.com
dbc-service:
  url: http://dbc-service.tsg.cfpamf.com
auditors:
  platform: ***********,***********
card-service:
  url: http://card-service.tsg.cfpamf.com
mall-biz:
  url: http://mall-biz.tsg.cfpamf.com
wms-service:
  url: http://wms-service.tsg.cfpamf.com
ms-promotion-service:
  url: http://ms-promotion.tsg.cfpamf.com
mall-account:
  url: http://mall-account.tsg.cfpamf.com
mall-system:
  url: http://mall-system.tsg.cfpamf.com
mall-promotion:
  url: http://mall-promotion.tsg.cfpamf.com
#UDC消息发送
ms-messagepush-service:
  url: http://ms-messagepush.tsg.cfpamf.com
ding-talk:
  url: https://oapi.dingtalk.com/robot/send?access_token=ab8bdb02cfb902cab5c1d757732c00d6f85a09ea4d1a39ff42c2363ac43bc425
dts-center:
  url: http://dts-center.tsg.cfpamf.com
mall-file-center:
  host: http://mall-file-center.tsg.cfpamf.com
oms-base-service:
  url: http://oms-base.tsg.cfpamf.com
cashier-service:
  url: http://mall-cashier.tsg.cfpamf.com
erp-service:
  url: http://erp-services.tsg.cfpamf.com
mall-stock:
  url: http://mall-stock.tsg.cfpamf.com
crawler-service:
  url: http://crawler-service.tsg.cfpamf.com
mall-member:
  url: http://mall-member.tsg.cfpamf.com
user-publicize-service:
  url: http://user-publicize-service.tsg.cfpamf.com
wecat-service:
  url: http://wecat-service.tsg.cfpamf.com
scrm-service:
  url: http://scrm-service.tsg.cfpamf.com
ares-trade:
  url: http://ares-trade.tsg.cfpamf.com
#  url: localhost:10210
ares-after-sale:
  url: http://ares-after-sale.tsg.cfpamf.com
#  url: localhost:10211
spyhunter-engine:
  url: http://spyhunter-engine.tsg.cfpamf.com
  valetModelGuid: 61D899E4-22C5-44AC-9D30-72B8BF372621
  valetChannelXX: xx
  valetChannelBAPP: bapp
  valetAllowLoan: allowloan
  valetConfirmMethod: confirmmethod
  valetConfirmNoFace: confirmnoface
mall-external-adapter:
  url: http://mall-external-adapter.tsg.cfpamf.com

dayEnd:
  start: 2330
  end: 100
enable:
  tcc: false
  enableTransaction: true
aliyun:
  img:
    url: https://mall-sld-test.oss-cn-beijing.aliyuncs.com/

channel-fee-rate:
  mappedRate:
    2-WXPAY: 0.006
    3-ALIPAY: 0.006
    4-BANK_PAY: 0
    1-BANK_TRANSFER: 0
    1-CARD: 0
    1-CARD_VOUCHER: 0
    1-ENJOY_PAY: 0
    1-FOLLOW_HEART: 0
    1-ONLINE: 0
    5-WXPAY: 0.006
    5-ALIPAY: 0.006
    5-CARD: 0
    5-CARD_VOUCHER: 0
    5-ENJOY_PAY: 0
    5-FOLLOW_HEART: 0
    5-ONLINE: 0
    5-BANK_TRANSFER: 0
    4-AGREED_PAY: 0
    4-ONLINE: 0

wx-combine-xzcard:
  orderList: [****************,****************,****************,****************,****************,****************,****************]

#订单退款黑名单，拦截订单退款
refund-black-list:
  orderList: [****************,****************,****************]

presell:
  paymethods:
    deposit: WXPAY,BANK_TRANSFER,ALIPAY
    remain: WXPAY,ENJOY_PAY,FOLLOW_HEART,BANK_TRANSFER,ALIPAY

spring.cloud.nacos.elegant.offline.enabled: true

bkTransfer:
  expire-url: https://2i1i.cn/g7Di
order:
  offline:
    privilege: 2030006,1140041,2930008,870002,2140002,290007,1140004,4220003,2670005,6120002,6040003,6710002,5860006
    agriFeeCalStoreId: 2030006,1140041,2930008,870002,2140002,290007,1140004,4220003,2670005,6120002,6040003,6710002,5860006

home:
  service:
    auto:
      receive:
        days: 1
logging:
  level:
    com.cfpamf.ms.mallorder.mapper: debug
    com.cfpamf.ms.mallorder.pgMapper: debug

store-for-deliver:
  storeId: 230002,1140006,2670005,2930008,1140041,4220003,6710002,540004,5710003,5860006,6800002,1140004
store-for-self-order-deliver:
  storeId: 2030006,5970003,1140004,2140002,1140041,4220003,6710002

#是否允许客户经理签收
store-for-receive:
  storeId: 230002,1140006,2670005,1140004,1140041,4220003,6880005,6710002,5710003

#代客下单贷款类支付是否需要扫脸
face-scan-pay:
  enabled: false

funds-borrow-day:
  days: 3
  closeStoreDays: 3
  preAlertDays: 1
  factoryNo: 100101001

#商品自提自动发货
self-lift-order:
  auto-delivery-store-list: 2670005,7230009,6710002,

order-rate-config:
  maxSharingRate: 30

after:
  sale:
    loanpay:
      delay:
        days: 450
    common:  #超期售后时间
      delay:
        days: 300
    alipay:  #超期支付宝时间
      delay:
        days: 80

show:
  insurance:
    categoryId: 1000001701

commission-incentive-rule-config:
  whitelistsStore: [8390002,7230009]

employee-trade-statistical:
    startTime: 2024-01-01 00:00:00

fastOrder-expired: 48

share-order-configuration:
  shareableGoodsType: [1]
  shareableProductType: [1]
  shareablePromotionType: [402,107]


refund:
  erp:
    check:
      storeId: 2140002

store-risk-control-warning:
  num5: 5
  days30: 30
  num3: 3
  days90: 90

store-area-code-config:
  supervisor-map:
    # 辽宁区域
    CNDB: "ZHNX40338"
    #甘肃区域
    CNGS: "ZHNX40338"
      #湖南区域
    CNHN: "ZHNX40338"
      #山东区域
    CNSD: "ZHNX40338"
      #四川区域
    CNXN: "ZHNX40338"
      #广东区域
    GDQY: "ZHNX40338"
      #河北区域
    HBQYJG: "ZHNX40338"
    #河南区域
    HN01: "ZHNX40338"
      #海南区域
    HN02: "ZHNX40338"
      #内蒙区域
    NMQYJG: "ZHNX40338"
      #山西区域
    SXQY: "ZHNX40338"
      #云南区域
    XNQY: "ZHNX40338"
      #江苏区域
    Z1385275: "ZHNX40338"
      #江西区域
    Z4078404: "ZHNX40338"
      #湖北区域
    Z4113332: "ZHNX40338"
      #重庆区域
    Z4618781: "ZHNX40338"


immobilization-receiver-config:
  receiverList: ["ZHNX34338","ZHNX31920","CNBJ0372"]
  excludeCategory:
    - 1000000208
  sameStoreOverDays: 90
  sameStoreOverNum: 2
  ruleReceiverList:
    risk-warning-same-store:
      - ZHNX34338
      - ZHNX12663
      - ZHNX40338

order-performance-store-config:
  deliveryCheckFlag: true
  followRegisterRecommendStore: [8390002]


flow-manager:
  url: http://flow-manager.test-public

order-plan-date-loan:
  perSize: 100

order-beverage-work:
  storeIdList: [ 283303 ]
  procDefKeyList: [ "mall_order_refund_apply_35","mall_order_refund_return_apply_35","sku_return_process_11","sku_apply_process_11","sku_apply_cancel_process_11","wine_banquet_17","split_pack_process_11" ]

material:
  receiveMaterialNo: 100101005
  eleReceiveMaterialNo: 200101021

ms-contract:
  url: http://ms-contract.tsg.cfpamf.com/

ms-finance:
  url: http://ms-finance.tsg.cfpamf.com/

invoice:
  start: 20250420

special-store-id-configuration:
  lifeServiceRecommendStoreId: [1140006,2140002,1140004]