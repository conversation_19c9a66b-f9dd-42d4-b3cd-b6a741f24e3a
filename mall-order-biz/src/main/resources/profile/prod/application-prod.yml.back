spring:
  redis:
    host: r-2ze9d2094f4a8124.redis.rds.aliyuncs.com
    port: 6379
    password: DB_123456
    database: 11
    timeout: 100000
    jedis:
      pool:
        max-wait: 2000ms
        min-idle: 2
        max-idle: 8
  rabbitmq:
    host: rabbitmq.osg.cfpamf.com
    port: 5672
    username: admin
    password: MQsms#third#2017
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  cloud:
    alibaba:
      seata:
        enableAutoDataSourceProxy: false
        tx-service-group: seata_slodon_tx_group
        registry:
          file:
            name: registry.conf #因为seata版本驼峰参数映射有问题导致，seata的zk配置参数设置不上导致异常
    nacos:
      discovery:
        server-addr: nacos.osg.cfpamf.com:8848
        group: MALL_GROUP
        password: nacos
        username: nacos
#SEATA配置
seata:
  service:
    grouplist: {seata.osg.cfpamf.com:8091}

cfpamf:
  multiple:
    dataSource:
      enabled: true
  smartid:
    server: smartid.osg.cfpamf.com
    token: 0f673adf80504e2eaa552f5d791b644c

  ##配置数据源
  jdbc:
    dataSource:
      masterdb:
        rm-2zew39m161cbn22yf:
          mysql:
            rds:
              aliyuncs:
                com:3306/mall_order:
        jdbcUrl: *********************************************************************************************************************************************************************
        username: mall_order
        password: Zhnx#order@2021P
        hikariPool:
          maximumPoolSize: 10
          driverClassName: com.mysql.cj.jdbc.Driver
  ##配置mybatis plus
  mybatis:
    masterdb:
      basePackage: com.cfpamf.ms.mallorder.mapper
      typeAliasesPackage: com.cfpamf.ms.mallorder.po
      mapperLocations: classpath:mapper/**/*.xml
      configuration:
        default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
      type-enums-package: com.cfpamf.ms.mallorder.constant.enums
      pageProperties:
        overflow: true
        dialectType: mysql
      metaObjectHandler: com.cfpamf.ms.mallorder.common.handler.MyMetaObjectHandler
  ##配置swagger
  swagger:
    dockets:
      demo:
        groupName: 订单中心
        basePackage: com.cfpamf.ms.mallorder.controller
        author: 毛亮
        title: 订单中心

log4jdbc:
  sqltiming:
    warn:
      threshold: 300
    error:
      threshold: 2000
  dump.sql.select: true

xxl:
  job:
    version: 2.0
  newjob:
    admin:
      addresses: http://xxl-job2.osg.cfpamf.com/xxl-job-admin
    executor:
      appname: ${spring.application.name}
      ip:
      port: 9911
      logpath: /data/applogs/xxl-job/jobhandler/
      logretentiondays: 5
    accessToken:

aliyun:
  img:
    url: https://mall-sld-prod.oss-cn-beijing.aliyuncs.com/

mall-payment:
  url: http://mall-payment.osg.cfpamf.com
  notify: ${mall-order-biz.url}front/orderPayCallback/notify
  refundNotify: ${mall-order-biz.url}front/orderPayCallback/refundNotify
  loanNotify: ${mall-order-biz.url}front/orderPayCallback/loanNotify
  wxpay-key: caa08fe60b014a14b5503fb2ea60aae1
ms-service-customer:
  url: ms-customer.osg.cfpamf.com
mall-order-biz:
  url: http://mall-order.osg.cfpamf.com/
ms-service-loan:
  url: http://ms-loan.osg.cfpamf.com
ms-bizconfig-service:
  url: http://ms-bizconfig.osg.cfpamf.com/
core-trade-service:
  url: http://core-trade.osg.cfpamf.com
#hr服务
hrms-biz:
  url: http://hrms-biz.osg.cfpamf.com/
bms:
  api:
    url: http://bms-service.osg.cfpamf.com
card-service:
  url: http://card-service.osg.cfpamf.com