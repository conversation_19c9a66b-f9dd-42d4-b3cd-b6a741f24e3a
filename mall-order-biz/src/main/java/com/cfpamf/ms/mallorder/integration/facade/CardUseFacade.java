package com.cfpamf.ms.mallorder.integration.facade;

import com.cdfinance.ms.card.facade.model.request.cardUse.CardPayRequest;
import com.cdfinance.ms.card.facade.model.request.cardUse.CardPrePayRequest;
import com.cdfinance.ms.card.facade.model.request.cardUse.CardRefundCheckRequest;
import com.cdfinance.ms.card.facade.model.request.cardUse.CardRefundRequest;
import com.cdfinance.ms.card.facade.model.request.cardUse.CardTryPayRequest;
import com.cdfinance.ms.card.facade.model.request.cardUse.CardUseRequest;
import com.cdfinance.ms.card.facade.model.response.cardUse.CardPayResponse;
import com.cdfinance.ms.card.facade.model.response.cardUse.CardPrePayResponse;
import com.cdfinance.ms.card.facade.model.response.cardUse.CardRefundCheckResponse;
import com.cdfinance.ms.card.facade.model.response.cardUse.CardTryPayResponse;
import com.cdfinance.ms.card.facade.model.response.cardUse.CardUserQueryResponse;
import com.cdfinance.ms.card.facade.model.vo.PageResultVO;
import com.cfpamf.common.ms.result.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR>
 * @date 2021/9/14 15:25
 */
@FeignClient(value = "card-service", url = "${card-service.url}",name = "card-service")
public interface CardUseFacade {

    @PostMapping(path = "/cardUse/customer/list", produces = "application/json")
    Result<PageResultVO<CardUserQueryResponse>> cardUseList(@RequestBody CardUseRequest request,
                                                            @RequestHeader(name = "x-cfpamf-auth-user-info") String userInfo);

    @PostMapping(path = "/cardUse/pay/try", produces = "application/json", consumes = "application/json")
    Result<CardTryPayResponse> tryPay(@RequestBody CardTryPayRequest request,
                                      @RequestHeader(name = "x-cfpamf-auth-user-info") String userInfo);

    @PostMapping(path = "/cardUse/pay/frozen", produces = "application/json", consumes = "application/json")
    Result<CardPrePayResponse> frozenPay(@RequestBody CardPrePayRequest request,
                                         @RequestHeader(name = "x-cfpamf-auth-user-info") String userInfo);

    @PostMapping(path = "/cardUse/pay/success", produces = "application/json", consumes = "application/json")
    Result<CardPayResponse> paySuccess(@RequestBody CardPayRequest request,
                                       @RequestHeader(name = "x-cfpamf-auth-user-info") String userInfo);

    @PostMapping(path = "/cardUse/pay/cancel", produces = "application/json", consumes = "application/json")
    Result<CardPayResponse> payCancel(@RequestBody CardPayRequest request,
                                      @RequestHeader(name = "x-cfpamf-auth-user-info") String userInfo);

    @PostMapping(path = "/cardUse/pay/refund", produces = "application/json", consumes = "application/json")
    Result<CardPayResponse> payRefund(@RequestBody CardRefundRequest request,
                                      @RequestHeader(name = "x-cfpamf-auth-user-info") String userInfo);

    @PostMapping(path = "/cardUse/pay/refund/check", produces = "application/json", consumes = "application/json")
    Result<CardRefundCheckResponse> payRefundCheck(@RequestBody CardRefundCheckRequest request,
                                                   @RequestHeader(name = "x-cfpamf-auth-user-info") String userInfo);

}
