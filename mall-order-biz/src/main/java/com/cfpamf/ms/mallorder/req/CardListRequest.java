package com.cfpamf.ms.mallorder.req;

import com.cdfinance.ms.card.facade.model.vo.PageVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class CardListRequest extends PageVO {

    @ApiModelProperty(value = "店铺id")
    @NotEmpty(message = "店铺id不允许为空")
    private List<String> storeIds;

    @ApiModelProperty(value = "活动类型")
    private Integer promotionType;

}
