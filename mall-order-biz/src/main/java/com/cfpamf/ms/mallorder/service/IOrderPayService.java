package com.cfpamf.ms.mallorder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdfinance.ms.card.facade.model.response.cardUse.CardRefundCheckResponse;
import com.cdfinance.ms.card.facade.model.response.cardUse.CardUserQueryResponse;
import com.cfpamf.ms.loan.facade.vo.external.mall.MallContractCodesVo;
import com.cfpamf.ms.loan.facade.vo.external.mall.MallContractContentVO;
import com.cfpamf.ms.mallorder.dto.OrderAutoPayDTO;
import com.cfpamf.ms.mallorder.po.OrderPayPO;
import com.cfpamf.ms.mallorder.req.CardListRequest;
import com.cfpamf.ms.mallorder.req.OrderPayRequest;
import com.cfpamf.ms.mallorder.vo.MallWithholdCheckResponseVO;
import com.cfpamf.ms.mallorder.vo.OrderPayBriefInfoVO;
import com.cfpamf.ms.mallorder.vo.OrderPayInfoVO;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PageVO;
import com.cfpamf.ms.mallorder.vo.OrderPayBriefInfoVO;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 订单支付  service 接口
 */
public interface IOrderPayService extends IService<OrderPayPO> {

    /**
     * @param
     * @return java.util.List<com.cfpamf.ms.mallorder.dto.OrderAutoPayDTO>
     * @description : 获取待自动支付订单
     */
    List<OrderAutoPayDTO> listOrderAutoPay();

    /**
     * @param userNo
     * @param useRequest
     * @return com.slodon.bbc.core.response.JsonResult<com.slodon.bbc.core.response.PageVO   <   com.cdfinance.ms.card.facade.model.response.cardUse.CardUserQueryResponse>>
     * @description : 获取可用卡列表
     */
    JsonResult<PageVO<CardUserQueryResponse>> listCardUser(String userNo, CardListRequest useRequest);

    /**
     * @param xzCardAmount
     * @param paySn
     * @param userNo
     * @param xzCardList
     * @return void
     * @description :执行乡助卡冻结请求
     */
    String payCardAmount(BigDecimal xzCardAmount,BigDecimal totalAmount, String paySn, String userNo, List<String> xzCardList);

    /**
     * @param paySn
     * @param xzCardAmount
     * @param userNo
    * @return void
    * @description : 退款试算
    */
    CardRefundCheckResponse payRefundCheck(String paySn, BigDecimal xzCardAmount, String userNo);

    /**
     * @param afsSn
     * @param paySn
     * @param xzCardAmount
     * @param userNo
    * @return void
    * @description :退款
    */
    void payRefund(String afsSn, String paySn, BigDecimal xzCardAmount, String userNo);

    /**
     * 获取支付单号
     *
     * @param memberId  用户ID
     */
    String getOrderPno(String memberId);

    /**
     * 根据支付单号查询订单
     *
     * @param pno   支付单号
     */
    OrderPayBriefInfoVO getOrderPayBriefInfoByPno(String pno);


    OrderPayPO getByPaySn(String paySn);

    /***
    * @param request
    * @return com.cfpamf.ms.loan.facade.vo.external.mall.MallContractContentVO
    * @description : 预览合同
    */
    MallContractContentVO contractPreview(@NotNull @Valid OrderPayRequest request);


    MallContractCodesVo listLoanContractCode(@NotNull @Valid OrderPayRequest request);

    /**
     * 预付订金订单类型需要特殊处理支付金额
     *
     * @param orderSn
     * @param payType 订单类型，1-订金
     * @param orderPayInfoVO
     * */
    void dealPreSellResult(String orderSn,Integer payType,OrderPayInfoVO orderPayInfoVO);


    /**
     * @param bizId
     * @return void
     * @description :渠道交易流水号补偿
     */
    Boolean orderBankTrxNoMakeUp(Long bizId);

    /**
     * 代扣还款校验
     *
     * @param orderNo 订单编号
     * @return 校验结果
     */
    MallWithholdCheckResponseVO checkWithHold(String orderNo);
}
