package com.cfpamf.ms.mallorder.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.beust.jcommander.Strings;
import com.cdfinance.ms.card.facade.model.request.cardUse.CardTryPayRequest;
import com.cdfinance.ms.card.facade.model.response.cardUse.CardTryPayResponse;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.dts.biz.api.DtsBzOrderOperateClient;
import com.cfpamf.dts.biz.dto.BzOrderAddressDTO;
import com.cfpamf.framework.autoconfigure.common.exception.MSException;
import com.cfpamf.framework.autoconfigure.redis.lock.DistributeLock;
import com.cfpamf.ms.bms.facade.vo.DictionaryItemVO;
import com.cfpamf.ms.loan.facade.request.external.mall.GetMallProductElementRequest;
import com.cfpamf.ms.loan.facade.request.external.mall.GetMallProductRateRequest;
import com.cfpamf.ms.loan.facade.request.external.mall.MallCommodityBriefInfo;
import com.cfpamf.ms.mall.filecenter.constant.IScenesMaterialProofConstant;
import com.cfpamf.ms.mall.filecenter.domain.vo.FileScenesProofMaterialVO;
import com.cfpamf.ms.mall.filecenter.domain.vo.FileScenesProofVO;
import com.cfpamf.ms.mall.filecenter.domain.vo.FileScenesWithMaterialVO;
import com.cfpamf.ms.mall.settlement.enums.BillAmountTypeEnum;
import com.cfpamf.ms.mallLogistic.constant.LogisticConst;
import com.cfpamf.ms.mallLogistic.dto.ExpressDTO;
import com.cfpamf.ms.mallLogistic.req.ExpressTrackQuery;
import com.cfpamf.ms.mallgoods.facade.api.GoodsFreightTemplateFeignClient;
import com.cfpamf.ms.mallgoods.facade.api.ProductExtendFeignClient;
import com.cfpamf.ms.mallgoods.facade.dto.CalculateExpressDTO;
import com.cfpamf.ms.mallgoods.facade.dto.ProductInfoDTO;
import com.cfpamf.ms.mallgoods.facade.enums.EventStockTypeEnum;
import com.cfpamf.ms.mallgoods.facade.request.GoodsLandingPricePramaDTO;
import com.cfpamf.ms.mallgoods.facade.vo.GoodsLandingPriceResponse;
import com.cfpamf.ms.mallgoods.facade.vo.Product;
import com.cfpamf.ms.mallgoods.facade.vo.ProductExtend;
import com.cfpamf.ms.mallgoods.facade.vo.ProductPriceVO;
import com.cfpamf.ms.mallmember.api.MemberAddressFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallmember.po.MemberAddress;
import com.cfpamf.ms.mallmember.vo.ReceiveListInfoVo;
import com.cfpamf.ms.mallorder.builder.OrderBuilder;
import com.cfpamf.ms.mallorder.common.config.*;
import com.cfpamf.ms.mallorder.common.constant.CommonConst;
import com.cfpamf.ms.mallorder.common.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.common.enums.OrderProductDeliveryEnum;
import com.cfpamf.ms.mallorder.common.enums.PayMethodEnum;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.enums.transaction.TransactionLogTypeEnum;
import com.cfpamf.ms.mallorder.common.enums.transaction.TransactionRoleEnum;
import com.cfpamf.ms.mallorder.common.enums.transaction.TransactionStatusEnum;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.help.ResultUtils;
import com.cfpamf.ms.mallorder.common.help.SystemSettingObtainHelper;
import com.cfpamf.ms.mallorder.common.mq.RabbitMqConfig;
import com.cfpamf.ms.mallorder.common.mq.msg.PromotionDiscountMsg;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.common.util.OrderBizUtils;
import com.cfpamf.ms.mallorder.common.util.OrderLocalUtils;
import com.cfpamf.ms.mallorder.constant.ExchangeOrderConst;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.controller.fegin.facade.DbcServiceFeign;
import com.cfpamf.ms.mallorder.dto.*;
import com.cfpamf.ms.mallorder.enums.OrderPatternEnum;
import com.cfpamf.ms.mallorder.enums.*;
import com.cfpamf.ms.mallorder.integration.cashier.CashierIntegration;
import com.cfpamf.ms.mallorder.integration.erp.ERPIntegration;
import com.cfpamf.ms.mallorder.integration.facade.BmsIntegration;
import com.cfpamf.ms.mallorder.integration.facade.TemplateMessageFacade;
import com.cfpamf.ms.mallorder.integration.facade.dto.UnitConvertVo;
import com.cfpamf.ms.mallorder.integration.filecenter.FileCenterIntegration;
import com.cfpamf.ms.mallorder.integration.filecenter.ProofSceneEnum;
import com.cfpamf.ms.mallorder.integration.filecenter.SceneTypeEnum;
import com.cfpamf.ms.mallorder.integration.goods.GoodsFeignIntegration;
import com.cfpamf.ms.mallorder.integration.logistic.LogisticIntegration;
import com.cfpamf.ms.mallorder.integration.messagepush.MessagePushIntegration;
import com.cfpamf.ms.mallorder.integration.pay.CardPayIntegration;
import com.cfpamf.ms.mallorder.integration.pay.PayIntegration;
import com.cfpamf.ms.mallorder.integration.shop.ShopIntegration;
import com.cfpamf.ms.mallorder.integration.wms.WmsIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderLogisticMapper;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.mapper.OrderPresellMapper;
import com.cfpamf.ms.mallorder.mapper.OrderProductMapper;
import com.cfpamf.ms.mallorder.model.*;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.OrderDeliveryMessageReq;
import com.cfpamf.ms.mallorder.req.OrderDeliveryReq;
import com.cfpamf.ms.mallorder.req.OrderInfoModifyReq;
import com.cfpamf.ms.mallorder.req.OrderRenewalPriceReq;
import com.cfpamf.ms.mallorder.req.front.PurchaseOrderAddressUpdateRequest;
import com.cfpamf.ms.mallorder.request.OrderReturnExample;
import com.cfpamf.ms.mallorder.request.req.ErpOrderDeliveryRequest;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.v2.common.lock.Lock;
import com.cfpamf.ms.mallorder.v2.manager.GoodsStockService;
import com.cfpamf.ms.mallorder.v2.service.OrderPayRecordService;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallorder.validation.OrderAmountValidation;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallorder.vo.exportvo.DeliveryFailureVO;
import com.cfpamf.ms.mallpromotion.constant.PromotionConst;
import com.cfpamf.ms.mallshop.api.*;
import com.cfpamf.ms.mallshop.enums.StoreJindieStockFlagEnum;
import com.cfpamf.ms.mallshop.enums.WhiteListEnum;
import com.cfpamf.ms.mallshop.request.StoreAuditorListReq;
import com.cfpamf.ms.mallshop.request.VendorExample;
import com.cfpamf.ms.mallshop.resp.Store;
import com.cfpamf.ms.mallshop.resp.Vendor;
import com.cfpamf.ms.mallshop.vo.FrontStoreRegionRequestVO;
import com.cfpamf.ms.mallshop.vo.FrontStoreRegionVo;
import com.cfpamf.ms.mallshop.vo.StoreAuditorVO;
import com.cfpamf.ms.mallsystem.api.ExpressFeignClient;
import com.cfpamf.ms.mallsystem.request.ExpressExample;
import com.cfpamf.ms.mallsystem.vo.Express;
import com.cfpamf.msgpush.facade.enums.ReceiverTypeEnum;
import com.cfpamf.msgpush.facade.request.templateMessage.BatchBizMessageSubReq;
import com.cfpamf.msgpush.facade.request.templateMessage.BatchBizMessageTemplateReq;
import com.google.api.client.util.Lists;
import com.slodon.bbc.core.constant.MemberTplConst;
import com.slodon.bbc.core.constant.ResponseConst;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.express.TracesResult;
import com.slodon.bbc.core.express.TrackUtil;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.TimeUtil;
import com.slodon.bbc.core.util.UserUtil;
import com.slodon.bbc.core.util.WebUtil;
import com.slodon.bbc.starter.mq.entity.MessageSendProperty;
import com.slodon.bbc.starter.mq.entity.MessageSendVO;
import com.slodon.bbc.starter.mq.entity.VendorLogSendVO;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.slodon.bbc.core.constant.StarterConfigConst.*;

/**
 * 订单正向操作 service
 */
@Slf4j
@Service
public class OrderServiceImpl extends ServiceImpl<OrderMapper, OrderPO> implements IOrderService {

    @Autowired
    OrderPerformanceStoreConfig orderPerformanceStoreConfig;
    @Autowired
    IOrderDeliveryFailedLogService orderDeliverFailedLogService;
    @Resource
    private OrderModel orderModel;
    @Resource
    private OrderPayModel orderPayModel;
    @Resource
    private OrderReturnModel orderReturnModel;
    @Resource
    private OrderExtendModel orderExtendModel;
    @Resource
    private OrderLogModel orderLogModel;
    @Autowired
    private DbcServiceFeign dbcServiceFeign;
    @Autowired
    private StoreRegionFeignClient storeRegionFeignClient;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private VendorFeignClient vendorFeignClient;
    @Resource
    private MemberAddressFeignClient memberAddressFeignClient;
    @Resource
    private GoodsFreightTemplateFeignClient goodsFreightTemplateFeignClient;
    @Resource
    private OrderCreateHelper orderCreateHelper;
    @Autowired
    private IOrderProductService orderProductService;
    @Resource
    private IOrderReturnService orderReturnService;
    @Autowired
    private IOrderExtendService orderExtendService;
    @Autowired
    private IOrderService orderService;
    @Autowired
    private OrderLocalUtils orderLocalUtils;
    @Resource
    private OrderProductMapper orderProductMapper;
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private IOrderLogisticService orderLogisticService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private CardPayIntegration cardPayIntegration;
    @Resource
    private IOrderExtendFinanceService financeService;
    @Resource
    private SystemSettingObtainHelper systemSettingObtainHelper;
    @Resource
    private StoreIsolateWhiteListFeignClient storeIsolateWhiteListFeignClient;
    @Autowired
    private IOrderPayService iOrderPayService;
    @Autowired
    private LogisticIntegration logisticIntegration;
    @Autowired
    private IOrderPayService orderPayService;
    @Autowired
    private StoreForDeliverConfig storeForDeliverConfig;
    @Resource
    private OrderPresellMapper orderPresellMapper;
    @Autowired
    private IOrderPerformanceBelongsService orderPerformanceBelongsService;
    @Resource
    private OrderLogisticMapper orderLogisticMapper;
    @Autowired
    private IOrderPriceRecordService orderPriceRecordService;

    @Resource
    private HomeServiceFeignClient homeServiceFeignClient;
    @Autowired
    private ICommonMqEventService commonMqEventService;
    @Autowired
    private IOrderAmountStateRecordService orderAmountRecordService;
    @Autowired
    private OrderPresellService orderPresellService;
    @Autowired
    private PayIntegration payIntegration;
    @Autowired
    private PrivilegeService privilegeService;
    @Autowired
    private GoodsStockService goodsStockService;
    @Autowired
    private ITransactionLogService transactionLogService;
    @Resource
    private IPerformanceService performanceService;
    @Resource
    private DistributeLock distributeLock;
    @Resource
    private StoreAuditorFeignClient storeAuditorFeignClient;

    @Value("${home.service.auto.receive.days:15}")
    private Integer homeServiceAutoReceiveDays;

    @Value("${extend.auto.receive.days:45}")
    private Integer extendAutoReceiveDays;

    @Value("${home.service.start.time:2022-04-01 00:00:00}")
    private String homeServiceStartTimeStr;

    @Value("${home.service.batch.count:10}")
    private int homeServiceBatchCount;

    @Autowired
    private HttpServletRequest request;

    @Resource
    private IOrderDeliveryRecordService orderDeliveryRecordService;

    @Value("${after.sale.common.delay.days:300}")
    private int afterSaleCommonDelayDays;

    @Value("${after.sale.alipay.delay.days:80}")
    private int afterSaleAlipayDelayDays;

    @Value("${after.sale.loanpay.delay.days:300}")
    private int afterSaleLoanPayDelayDays;

    @Resource
    private ExpressFeignClient expressFeignClient;

    @Autowired
    private CashierIntegration cashierIntegration;
    @Autowired
    private GoodsFeignIntegration goodsFeignIntegration;
    @Autowired
    private OrderPayRecordService orderPayRecordService;
    @Autowired
    private FileCenterIntegration fileCenterIntegration;
    @Autowired
    private ShopIntegration shopIntegration;
    @Autowired
    private WmsIntegration wmsIntegration;
    @Autowired
    private ERPIntegration erpIntegration;
    @Autowired
    private IFundsBorrowBizService fundsBorrowBizService;
    @Autowired
    private IOrderTradeProofService orderTradeProofService;

    @Autowired
    private IOrderAmountStateRecordService orderAmountStateRecordService;

    @Resource
    private OrderAfterServiceModel orderAfterServiceModel;

    @Resource
    private IOrderLogisticItemService orderLogisticItemService;

    @Autowired
    private OrderAmountValidation orderAmountValidation;

    @Resource
    private IOrderExchangeDetailService orderExchangeDetailService;

    @Resource
    private IBzOrderProductCombinationService orderProductCombinationService;

    @Resource
    private TemplateMessageFacade templateMessageFacade;

    @Resource
    private MessagePushIntegration messagePushIntegration;

    @Autowired
    private BmsIntegration bmsIntegration;

    @Value("${store-risk-control-warning.num3}")
    private Integer num3; //统计数量

    @Value("${store-risk-control-warning.days30}")
    private Integer days30; //统计天数

    @Value("${store-risk-control-warning.num5}")
    private Integer num5; //统计数量

    @Value("${store-risk-control-warning.days90}")
    private Integer days90; //统计天数

    @Resource
    private StoreAreaCodeConfig storeAreaCodeConfig;

    @Resource
    private RiskWarningConfig riskWarningConfig;

    @Autowired
    private IOrderAfterService orderAfterService;

    @Resource
    private ProductExtendFeignClient productExtendFeignClient;

    @Autowired
    private Lock lock;

    @Autowired
    private OrderProductModel orderProductModel;


    @Value("${cjFinance.certificateOfApprovalNo:100101003}")
    private String certificateOfApprovalNo;

    @Value("${cjFinance.certificateOfNameplateNo:100101001}")
    private String certificateOfNameplateNo;

    @Autowired
    private RenewPriceSwitchConfig switchConfig;


    @Resource
    private DtsBzOrderOperateClient dtsBzOrderOperateClient;

    @Override
    public List<OrderProductDeliveryVO> deliveryDetail(String orderSn) {
        List<OrderProductDeliveryVO> productDeliveryVOS = orderProductMapper.orderProductDelivery(orderSn, null);
        AssertUtil.notEmpty(productDeliveryVOS, "订单商品为空");
        OrderPO orderPODb = orderModel.getOrderByOrderSn(orderSn);

        List<Long> orderProductIdList = productDeliveryVOS.stream().map(OrderProductDeliveryVO::getOrderProductId).collect(Collectors.toList());

        Map<Long, Integer> productRefundNumMap = orderAfterServiceModel.getProductRefundCountMap(orderProductIdList);
        Map<Long, Integer> productExchangeNumMap = new HashMap<>();
        if (ExchangeOrderConst.ORDER_EXCHANGE_FLAG_1 == orderPODb.getExchangeFlag()) {
            productExchangeNumMap = orderExchangeDetailService.getProductExchangeCountMap(orderProductIdList);
        }

        SceneTypeEnum sceneTypeEnum = this.getOrderFileSceneNo(orderPODb, OrderProductDeliveryEnum.WAIT_DELIVERY);

        /*if (!SettleModeEnum.BORROW.getCode().equals(orderPODb.getSettleMode())) {
            return productDeliveryVOS;
        }*/
        List<OrderProductDeliveryVO> orderProductDeliveryVOS =
                orderLocalUtils.sortFullGiftProductDeliveryVO(orderPODb.getOrderType(), productDeliveryVOS);

        BzOrderProductCombinationPO combinationPO = null;
        if (orderPODb.getOrderType() == OrderTypeEnum.COMBINATION.getValue()) {
            //如果是组合订单，标识出主/子商品行
            LambdaQueryWrapper<BzOrderProductCombinationPO> queryOrder = Wrappers.lambdaQuery();
            queryOrder.eq(BzOrderProductCombinationPO::getOrderSn, orderPODb.getOrderSn())
                    .select(BzOrderProductCombinationPO::getMainProductId);
            combinationPO = orderProductCombinationService.getOne(queryOrder);
        }
        for (OrderProductDeliveryVO itemVO : orderProductDeliveryVOS) {
            if (SettleModeEnum.BORROW.getCode().equals(orderPODb.getSettleMode())) {
                if (Objects.nonNull(sceneTypeEnum)) {
                    itemVO.setSceneNo(sceneTypeEnum.getValue());
                }
                itemVO.setSettleMode(SettleModeEnum.BORROW.getCode());
            }

            //已发货数量，数据库已有
            //已售后数量
            int returnedNum = 0;
            //售后中数量
            int returningNum = 0;

            OrderReturnExample returnExample = new OrderReturnExample();
            returnExample.setOrderSn(itemVO.getOrderSn());
            returnExample.setOrderProductId(itemVO.getOrderProductId());
            List<OrderReturnPO> orderReturnPOList = orderReturnModel.getOrderReturnList(returnExample, null);
            for (OrderReturnPO orderReturnPO : orderReturnPOList) {
                if (OrderReturnStatus.duringRefundStatus().contains(orderReturnPO.getState())) {
                    returningNum += orderReturnPO.getReturnNum();
                } else if (OrderReturnStatus.isFinish(orderReturnPO.getState())) {
                    returnedNum += orderReturnPO.getReturnNum();
                }
            }

            itemVO.setReturnedNum(returnedNum);
            itemVO.setReturningNum(returningNum);
            //可发货数量
            //默认可发货数量 = 购买数量 - 已发货数量 - 仅退款数量
            int refundNum = productRefundNumMap.get(itemVO.getOrderProductId()) == null ? 0 : productRefundNumMap.get(itemVO.getOrderProductId());
            int exchangedNum = productExchangeNumMap.get(itemVO.getOrderProductId()) == null ? 0 : productExchangeNumMap.get(itemVO.getOrderProductId());
            itemVO.setWaitDeliveryNum(itemVO.getProductNum() - itemVO.getDeliveryNum() - refundNum - exchangedNum);
            if (orderPODb.getOrderType() == OrderTypeEnum.COMBINATION.getValue()
                    && ObjectUtil.isNotEmpty(combinationPO) && itemVO.getProductId().equals(combinationPO.getMainProductId())) {
                itemVO.setIsMain(1);
            }
        }
        return orderProductDeliveryVOS;
    }

    /**
     * 发货详情v2接口
     *
     * @param orderSn 订单编号
     * @return 发货订单详情
     */
    @Override
    public OrderDeliverVo deliveryDetailV2(String orderSn) {
        // 查询原发货信息
        OrderDeliverVo result = new OrderDeliverVo();
        result.setDeliveryVOList(deliveryDetail(orderSn));
        // 查询合格证交易凭证
        List<FileScenesProofVO> approvalNoProof = orderTradeProofService.queryScenesMaterial(orderSn, null, ProofSceneEnum.SUBMIT.getCode(), certificateOfApprovalNo, false);
        if (!CollectionUtils.isEmpty(approvalNoProof)) {
            List<FileScenesProofMaterialVO> approvalContentList = approvalNoProof.stream().flatMap(p -> p.getMaterialVOList().stream()).collect(Collectors.toList());
            result.setApprovalList(approvalContentList);
        }
        // 查询铭牌交易凭证
        List<FileScenesProofVO> nameplateProof = orderTradeProofService.queryScenesMaterial(orderSn, null, ProofSceneEnum.SUBMIT.getCode(), certificateOfNameplateNo, false);
        if (!CollectionUtils.isEmpty(nameplateProof)) {
            List<FileScenesProofMaterialVO> nameplateContentList = nameplateProof.stream().flatMap(p -> p.getMaterialVOList().stream()).collect(Collectors.toList());
            result.setNameplateList(nameplateContentList);
        }
        return result;
    }

    @Autowired
    private StoreFeignClient storeFeignClient;

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "masterdbTx")
    public Boolean deliveryV2(OrderDeliveryReq deliveryReq, Vendor vendor) {
        OrderPO orderPODb = orderModel.getOrderByOrderSn(deliveryReq.getOrderSn());
        log.info("deliveryV2====deliveryReq = {}", JSONObject.toJSONString(deliveryReq));
        if (deliveryReq.getAllowNoLogistics() == null) {
            Boolean allowNoLogistics =
                    storeIsolateWhiteListFeignClient.isEmptyLogisticsInfoAllowed(orderPODb.getStoreId());
            deliveryReq.setAllowNoLogistics(allowNoLogistics);
        }

        // 配置的白名单订单，允许线下补录发货
        List<String> whiteList = getOfflineWhiteOrderList();
        if (!whiteList.contains(deliveryReq.getOrderSn())) {
            //非线下补录订单发货标识，不能对线下补录订单进行发货
            if(!deliveryReq.isOffLineOrder()) {
                BizAssertUtil.isTrue(OrderTypeEnum.isOffline(orderPODb.getOrderType()), "线下订单不能进行普通发货");
            }
        }


        List<OrderProductPO> productPOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(deliveryReq.getOrderProductIds()) && CollectionUtils.isEmpty(deliveryReq.getProductIds())) {
            throw new BusinessException("请选择需要发货的商品");
        }
        //根据订单商品主键orderProductId或者商品productId组装发货信息
        if (CollectionUtils.isNotEmpty(deliveryReq.getOrderProductIds())) {
            productPOList = getProductList(deliveryReq);
            BizAssertUtil.notEmpty(productPOList, "查询不到待发货的订单商品信息!");
            if (CollectionUtils.isEmpty(deliveryReq.getProductIds())) {
                List<Long> productIds =
                        productPOList.stream().map(OrderProductPO::getProductId).collect(Collectors.toList());
                deliveryReq.setProductIds(productIds);
            }
        }
        if (CollectionUtils.isNotEmpty(deliveryReq.getProductIds())) {
            if (CollectionUtils.isEmpty(deliveryReq.getOrderProductDetail())) {
                List<OrderProductDeliverDTO> orderProductDeliveryList = getOrderProductDeliveryList(deliveryReq.getOrderSn(), deliveryReq.getProductIds());
                deliveryReq.setOrderProductDetail(orderProductDeliveryList);
            }
            if (CollectionUtils.isEmpty(deliveryReq.getOrderProductIds())) {
                deliveryReq.setOrderProductIds(deliveryReq.getOrderProductDetail().stream().map(OrderProductDeliverDTO::getOrderProductId).collect(Collectors.toList()));
                productPOList = getProductList(deliveryReq);
            }

        }

        BizAssertUtil.notEmpty(productPOList, "该订单不存在未发货的商品，请勿重复发货!");

        // 查询订单分支
        OrderExtendPO orderExtendPO =
                orderExtendService.lambdaQuery().eq(OrderExtendPO::getOrderSn, orderPODb.getOrderSn())
                        .one();

        // 数据校验--参数检验、权限校验、物流单号校验
        deliveryParamValidate(orderPODb, productPOList, orderExtendPO, vendor, deliveryReq);

        //erp履约渠道,销售出库可用库存校验；增加发货中状态
        boolean outboundFlag = productPOList.get(0).getPerformanceChannel().equals(OrderPerformanceChannelEnum.PERFORMANCE_CHANNEL_ERP.getValue()) && tianjieOrderSnCheck(orderPODb.getOrderSn());
        if (outboundFlag) {
            //erp履约渠道,销售出库可用库存校验
            performanceService.erpOutBoundCheck(orderPODb, productPOList, orderExtendPO, deliveryReq);
        }
        // 添加物流信息
        String deliveryResult = addDeliveryInfo(deliveryReq.getOrderProductIds(), deliveryReq, orderPODb, vendor, outboundFlag);
        log.info("deliveryV2====deliveryResult = {}", deliveryResult);
        if (!OrderConst.CHECK_SUCCESS.equals(deliveryResult)) {
            throw new BusinessException(deliveryResult);
        }
        return Boolean.TRUE;
    }

    /**
     * 获取线下补录订单白名单订单列表
     *
     * @return 白名单订单列表
     */
    private List<String> getOfflineWhiteOrderList() {
        List<String> whiteList = Lists.newArrayList();
        List<DictionaryItemVO> dictionaryItemVOS = bmsIntegration.listDictionaryItemsByTypeCode(CommonConst.OFFLINE_WHITE_LIST_KEY, CommonConst.MALL_SYSTEM_MANAGE_ID);
        if (!CollectionUtils.isEmpty(dictionaryItemVOS)){
            log.info("getOfflineWhiteOrderList,dictionaryItemVOS:{}",dictionaryItemVOS);
            for (DictionaryItemVO dictionaryItemVO : dictionaryItemVOS) {
                log.info("getOfflineWhiteOrderList start deal,itemVO:{}",dictionaryItemVO);
                String itemDesc = dictionaryItemVO.getItemDesc();
                if (StringUtils.isNotBlank(itemDesc)){
                    String[] split = itemDesc.split(",");
                    whiteList.addAll(Arrays.asList(split));
                }
            }
        }
        log.info("getOfflineWhiteOrderList final result:{}",whiteList);
        return whiteList;
    }


    public List<OrderProductDeliverDTO> getProductDeliveryListByProductId(String orderSn, List<Long> productIds) {
        List<OrderProductDeliverDTO> orderProductDetailList = new ArrayList<>();
        LambdaQueryWrapper<OrderProductPO> productQuery = new LambdaQueryWrapper<>();
        productQuery.eq(OrderProductPO::getOrderSn, orderSn).in(OrderProductPO::getProductId, productIds)
                .eq(OrderProductPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        List<OrderProductPO> productPOS = orderProductService.list(productQuery);
        BizAssertUtil.notEmpty(productPOS, "查询发货商品异常，请联系管理员！");

        List<Long> orderProductIdList = productPOS.stream().map(OrderProductPO::getOrderProductId).collect(Collectors.toList());
        Map<Long, Integer> productRefundNumMap = orderAfterServiceModel.getProductRefundCountMap(orderProductIdList);

        Map<Long, Integer> productExchangeNumMap = orderExchangeDetailService.getProductExchangeCountMap(orderProductIdList);


        for (OrderProductPO orderProductPO : productPOS) {
            OrderProductDeliverDTO orderProductDeliverDTO = new OrderProductDeliverDTO();
            orderProductDeliverDTO.setOrderProductId(orderProductPO.getOrderProductId());
            //默认可发货数量 = 购买数量 - 已发货数量 - 仅退款数量 - 换货退款数量
            int refundNum = productRefundNumMap.get(orderProductPO.getOrderProductId()) == null ? 0 : productRefundNumMap.get(orderProductPO.getOrderProductId());
            int exchangedNum = productExchangeNumMap.get(orderProductPO.getOrderProductId()) == null ? 0 : productExchangeNumMap.get(orderProductPO.getOrderProductId());
            orderProductDeliverDTO.setDeliveryNum(orderProductPO.getProductNum() - orderProductPO.getDeliveryNum() - refundNum - exchangedNum);
            orderProductDetailList.add(orderProductDeliverDTO);
        }
        return orderProductDetailList;
    }

    private List<OrderProductPO> getProductList(OrderDeliveryReq deliveryReq) {
        LambdaQueryWrapper<OrderProductPO> productPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        productPOLambdaQueryWrapper.eq(OrderProductPO::getOrderSn, deliveryReq.getOrderSn())
                .in(OrderProductPO::getOrderProductId, deliveryReq.getOrderProductIds());
        if (deliveryReq.isOutboundProductFlag()) {
            productPOLambdaQueryWrapper.in(OrderProductPO::getDeliveryState, Arrays.asList(OrderProductDeliveryEnum.WAIT_DELIVERY, OrderProductDeliveryEnum.PART_DELIVERY, OrderProductDeliveryEnum.OUTBOUND));
        } else {
            productPOLambdaQueryWrapper.in(OrderProductPO::getDeliveryState, Arrays.asList(OrderProductDeliveryEnum.WAIT_DELIVERY, OrderProductDeliveryEnum.PART_DELIVERY));
        }


        return orderProductService.list(productPOLambdaQueryWrapper);
    }

    private void updateDeliveryInfo(Long logisticId, OrderDeliveryReq deliveryReq, OrderPO orderPODb, Vendor vendor) {
        OrderLogisticPO updateLogisticPO = new OrderLogisticPO();
        updateLogisticPO.setLogisticId(logisticId);
        if (deliveryReq.getDeliverType() == OrderConst.DELIVER_TYPE_0) {

            if (deliveryReq.getDeliverType().equals(OrderConst.DELIVER_TYPE_0)) {
                // 查询快递公司信息
                Express express;
                if (deliveryReq.getExpressId() != null && deliveryReq.getExpressId() != 0) {
                    express = expressFeignClient.getExpressByExpressId(deliveryReq.getExpressId());
                    AssertUtil.notNull(express, "获取快递公司信息为空，请重试");
                } else {
                    ExpressExample expressExample = new ExpressExample();
                    expressExample.setExpressCode(deliveryReq.getExpressCompanyCode());
                    List<Express> expressList = expressFeignClient.getExpressList(expressExample);
                    AssertUtil.notEmpty(expressList, "获取快递公司信息为空，请重试");
                    express = expressList.get(0);
                }
                updateLogisticPO.setExpressId(express.getExpressId());
                updateLogisticPO.setExpressName(express.getExpressName());
                updateLogisticPO.setExpressCompanyCode(express.getExpressCode());
                updateLogisticPO.setExpressNumber(deliveryReq.getExpressNumber());
            }

        } else if (deliveryReq.getDeliverType() == OrderConst.DELIVER_TYPE_1
                && deliveryReq.getAllowNoLogistics() != null && deliveryReq.getAllowNoLogistics()) {
            updateLogisticPO.setDeliverMobile(deliveryReq.getDeliverMobile());
            updateLogisticPO.setDeliverName(deliveryReq.getDeliverName());
        }
        orderLogisticService.updateById(updateLogisticPO);
        // 异步写订单日志
        CompletableFuture.runAsync(() -> {
            orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_VENDOR, vendor.getVendorId(), vendor.getVendorName(),
                    orderPODb.getOrderSn(), orderPODb.getOrderState(), orderPODb.getOrderState(),
                    orderPODb.getLoanPayState(), "商品发货修改", deliveryReq.getChannel());
        });

       /* // 记录发货日志
        String operator;
        if (deliveryReq.getChannel() == null || ChannelEnum.getValue(deliveryReq.getChannel().getValue()) == null) {
            operator = vendor.getVendorName();
        } else {
            operator = ChannelEnum.getValue(deliveryReq.getChannel().getValue()).getValue();
        }
        //幂等健:订单号+orderProductId+时间戳
        String bizNo = performanceService.buildBizUniqueNo(orderPODb.getOrderSn(), deliveryReq.getOrderProductIds());
        orderDeliveryRecordService.addDeliveryRecord(logisticId, operator, deliveryReq, bizNo);*/
    }

    /**
     * @param orderProductIds 订单货品id
     * @param deliveryReq
     * @param orderPODb
     * @param vendor
     */
    private String addDeliveryInfo(List<Long> orderProductIds, OrderDeliveryReq deliveryReq, OrderPO orderPODb,
                                   Vendor vendor, boolean outboundFlag) {
        // 发货校验
        if (orderPODb.getOrderState() < OrderStatusEnum.WAIT_RECEIPT.getValue()
                || OrderStatusEnum.TRADE_CLOSE.getValue().equals(orderPODb.getOrderState())) {
            String verifyResult = this.orderDeliveryVerify(orderPODb, vendor);
            if (!OrderConst.CHECK_SUCCESS.equals(verifyResult)) {
                return verifyResult;
            }
        }

        if (outboundFlag) {
            if (orderPerformanceStoreConfig.getDeliveryCheckFlag()
                    && !orderPerformanceBelongsService.verifyBoundBelongerByOrderSn(orderPODb.getOrderSn())) {
                return "订单业绩归属未绑定或未生效，请联系管理员";
            }
        }


        int deliverPackageState = DeliverPackageStateEnum.DELIVERED_SUCCESS.getValue();
        List<OrderProductPO> orderProductPOList = orderProductMapper.selectBatchIds(deliveryReq.getOrderProductIds());
        Map<Long, OrderProductDeliverDTO> orderProductDeliverDTOMap = deliveryReq.getOrderProductDetail().stream().collect(
                Collectors.toMap(OrderProductDeliverDTO::getOrderProductId, OrderProductDeliverDTO -> OrderProductDeliverDTO, (x1, x2) -> x1));

        if (outboundFlag) {
            //erp履约渠道,增加发货中状态
            deliverPackageState = DeliverPackageStateEnum.OUTBOUND.getValue();
            // 更新商品行发货状态为出库中
            LambdaUpdateWrapper<OrderProductPO> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(OrderProductPO::getDeliveryState, OrderProductDeliveryEnum.OUTBOUND.getValue())
                    .eq(OrderProductPO::getDeliveryState, OrderProductDeliveryEnum.WAIT_DELIVERY.getValue())
                    .in(OrderProductPO::getOrderProductId, orderProductIds);
            orderProductService.update(updateWrapper);

        } else {
            List<Long> orderProductIdList = orderProductPOList.stream().map(OrderProductPO::getOrderProductId).collect(Collectors.toList());
            // 更新商品行发货状态及发货数量
            Map<Long, Integer> productRefundNumMap = orderAfterServiceModel.getProductRefundCountMap(orderProductIdList);

            Map<Long, Integer> productExchangeNumMap = orderExchangeDetailService.getProductExchangeCountMap(orderProductIdList);

            for (OrderProductPO orderProductPO : orderProductPOList) {
                int deliveryNum = orderProductDeliverDTOMap.get(orderProductPO.getOrderProductId()).getDeliveryNum();
                log.info("addDeliveryInfo deliveryNum = {}", deliveryNum);
                int refundNum = productRefundNumMap.get(orderProductPO.getOrderProductId()) == null ? 0 : productRefundNumMap.get(orderProductPO.getOrderProductId());
                int exchangedNum = productExchangeNumMap.get(orderProductPO.getOrderProductId()) == null ? 0 : productExchangeNumMap.get(orderProductPO.getOrderProductId());
                //剩余发货数量 = 商品购买数量 - 发货数量 - 已发货数量 - 仅退款数量 - 换货退款数量
                int remainDeliverNum = orderProductPO.getProductNum() - deliveryNum - orderProductPO.getDeliveryNum() - refundNum - exchangedNum;
                log.info("remainDeliverNum={},refundNum={},exchangedNum={},orderProductPO.getDeliveryNum()={}", remainDeliverNum, refundNum, exchangedNum, orderProductPO.getDeliveryNum());
                OrderProductDeliveryEnum OrderProductDeliveryEnum = remainDeliverNum == 0 ? com.cfpamf.ms.mallorder.common.enums.OrderProductDeliveryEnum.DELIVERED : com.cfpamf.ms.mallorder.common.enums.OrderProductDeliveryEnum.PART_DELIVERY;
                LambdaUpdateWrapper<OrderProductPO> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.set(OrderProductPO::getDeliveryState, OrderProductDeliveryEnum)
                        .set(OrderProductPO::getDeliveryNum, deliveryNum + orderProductPO.getDeliveryNum())
                        .in(OrderProductPO::getDeliveryState, com.cfpamf.ms.mallorder.common.enums.OrderProductDeliveryEnum.getWaitDeliveryAndOutboundState())
                        .in(OrderProductPO::getOrderProductId, orderProductPO.getOrderProductId());
                orderProductService.update(updateWrapper);
            }
        }

        // 保存包裹物流信息
        OrderLogisticPO logisticPO = orderLogisticService.saveLogistic(orderPODb, deliveryReq, vendor.getVendorName(), deliverPackageState);

        // 保存包裹商品信息
        orderLogisticItemService.batchSave(logisticPO.getPackageSn(), orderProductPOList, orderProductDeliverDTOMap, vendor.getVendorName());


        LambdaUpdateWrapper<OrderProductPO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(OrderProductPO::getLogisticId, logisticPO.getLogisticId())
                .in(OrderProductPO::getOrderProductId, orderProductIds);
        orderProductService.update(updateWrapper);

        Integer logRole = OrderConst.LOG_ROLE_VENDOR;
        if (deliveryReq.isManagerDeliver()) {
            logRole = OrderConst.LOG_ROLE_CUSTOMER_MANAGER;
        } else if (deliveryReq.isStationMasterDeliver()) {
            logRole = OrderConst.LOG_ROLE_STATIONMASTER;
        }
        if (orderPODb.getOrderState() < OrderStatusEnum.WAIT_RECEIPT.getValue()) {
            // 订单发货
            if (outboundFlag) {
                ErpPerformanceStock erpPerformanceStock = performanceService.buildErpOutBoundParam(orderProductIds, deliveryReq, orderPODb, vendor);
                erpPerformanceStock.setBizNo(logisticPO.getPackageSn());
                //组装mq
                orderCreateHelper.productOutbound(erpPerformanceStock);
                //异步写日志
                Integer finalLogRole = logRole;
                CompletableFuture.runAsync(() -> {
                    orderLogModel.insertOrderLog(finalLogRole, vendor.getVendorId(), vendor.getVendorName(), orderPODb.getOrderSn(),
                            orderPODb.getOrderState(), orderPODb.getOrderState(), LoanStatusEnum.APPLY_SUCCESS.getValue(), "商品出库中",
                            deliveryReq.getChannel());
                });
            } else {
                this.orderDelivery(orderPODb, vendor, deliveryReq.getChannel(), logRole, orderProductIds);

                // 发送会员MQ消息、推送微信消息
                OrderLogisticPO finalLogisticPO = logisticPO;
                CompletableFuture.runAsync(() -> {
                    this.deliveryMessage(orderPODb, finalLogisticPO, vendor);
                });
            }
        }


        return OrderConst.CHECK_SUCCESS;
    }

    private List<OrderProductDeliverDTO> getOrderProductDeliveryList(String orderSn, List<Long> productIds) {
        LambdaQueryWrapper<OrderProductPO> productQuery = new LambdaQueryWrapper<>();
        productQuery.eq(OrderProductPO::getOrderSn, orderSn).in(OrderProductPO::getProductId, productIds)
                .eq(OrderProductPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        List<OrderProductPO> productPOS = orderProductService.list(productQuery);
        List<Long> orderProductIdList = productPOS.stream().map(OrderProductPO::getOrderProductId).collect(Collectors.toList());
        // 更新商品行发货状态及发货数量
        Map<Long, Integer> productRefundNumMap = orderAfterServiceModel.getProductRefundCountMap(orderProductIdList);

        Map<Long, Integer> productExchangeNumMap = orderExchangeDetailService.getProductExchangeCountMap(orderProductIdList);
        return productPOS.stream().map(x -> {
            OrderProductDeliverDTO orderProductDeliverDTO = new OrderProductDeliverDTO();
            orderProductDeliverDTO.setOrderProductId(x.getOrderProductId());
            //可发货数量 = 商品数量 - 已发货数量 - 仅退款数量 - 换货退款数量
            int productRefundCount = productRefundNumMap.get(x.getOrderProductId()) == null ? 0 : productRefundNumMap.get(x.getOrderProductId());
            int productExchangedCount = productExchangeNumMap.get(x.getOrderProductId()) == null ? 0 : productExchangeNumMap.get(x.getOrderProductId());
            orderProductDeliverDTO.setDeliveryNum(x.getProductNum() - x.getDeliveryNum() - productRefundCount - productExchangedCount);
            return orderProductDeliverDTO;
        }).collect(Collectors.toList());
    }

    /**
     * @param productPOS
     * @return
     */
    @Override
    public Map<Long, Integer> getProductRefundCountMap(List<OrderProductPO> productPOS) {
        List<Long> orderProductIdList = productPOS.stream().map(OrderProductPO::getOrderProductId).collect(Collectors.toList());
        return orderAfterServiceModel.getProductRefundCountMap(orderProductIdList);
    }

    // 参数检验、权限校验、物流单号校验
    private OrderPO deliveryParamValidate(OrderPO orderPO, List<OrderProductPO> orderProductPOList,
                                          OrderExtendPO orderExtendPO, Vendor vendor, OrderDeliveryReq deliveryReq) {
        BizAssertUtil.notNull(orderPO, "订单不存在，请重试");
        BizAssertUtil.isTrue(!OrderDeliverTypeEnum.isDeliverType(deliveryReq.getDeliverType()), "发货类型错误");

        if (CollectionUtils.isNotEmpty(deliveryReq.getOrderProductDetail())) {
            //orderProductPOList转map得到xxx商品、及该商品的可发货数量，循环前端传的发货商品列表中的可发货数量，在这里对比
            List<Long> orderProductIdList = orderProductPOList.stream().map(OrderProductPO::getOrderProductId).collect(Collectors.toList());
            // 更新商品行发货状态及发货数量
            Map<Long, Integer> productRefundNumMap = orderAfterServiceModel.getProductRefundCountMap(orderProductIdList);

            Map<Long, Integer> productExchangeNumMap = new HashMap<>();
            if (ExchangeOrderConst.ORDER_EXCHANGE_FLAG_1 == orderPO.getExchangeFlag()) {
                productExchangeNumMap = orderExchangeDetailService.getProductExchangeCountMap(orderProductIdList);
            }

            Map<Long, OrderProductPO> orderProductPOMap = orderProductPOList.stream().collect(Collectors.toMap(OrderProductPO::getOrderProductId, x -> x));

            for (OrderProductDeliverDTO orderProductDeliverDTO : deliveryReq.getOrderProductDetail()) {
                OrderProductPO orderProduct = orderProductPOMap.get(orderProductDeliverDTO.getOrderProductId());
                BizAssertUtil.notNull(orderProduct, "没有可发货的商品");
                int refundNum = productRefundNumMap.get(orderProductDeliverDTO.getOrderProductId()) == null ? 0 : productRefundNumMap.get(orderProductDeliverDTO.getOrderProductId());
                int exchangedNum = productExchangeNumMap.get(orderProductDeliverDTO.getOrderProductId()) == null ? 0 : productExchangeNumMap.get(orderProductDeliverDTO.getOrderProductId());
                //可发货数量 = 商品购买数量 - 已发货数量 - 仅退款数量 - 换货退款数量
                int deliveryNum = orderProduct.getProductNum() - orderProduct.getDeliveryNum() - refundNum - exchangedNum;
                BizAssertUtil.isTrue(orderProductDeliverDTO.getDeliveryNum() > deliveryNum, orderProduct.getGoodsName() + ",商品发货数量超过可发货数量");
            }

        }

        List<OrderProductPO> productPOValidList = orderProductPOList.stream()
                .filter(x -> x.getProductNum() - x.getReturnNumber() == 0).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(productPOValidList)) {
            throw new BusinessException("该商品已售后，无法发货，请确认！");
        }


        BizAssertUtil.isTrue(CollectionUtils.isEmpty(deliveryReq.getOrderProductIds())
                && CollectionUtils.isEmpty(deliveryReq.getProductIds()), "发货商品不能为空");
        BizAssertUtil.isTrue(!OrderDeliverTypeEnum.isLogisticsDelivery(deliveryReq.getDeliverType())
                && !deliveryReq.getAllowNoLogistics(), "非白名单商家不支持无物流发货");
        if (OrderDeliverTypeEnum.LOGISTICS_DELIVERY.getValue().equals(deliveryReq.getDeliverType())) {
            if ((deliveryReq.getExpressId() == null || deliveryReq.getExpressId() == 0)
                    && StringUtils.isBlank(deliveryReq.getExpressCompanyCode())) {
                throw new BusinessException("快递公司id/编号不能为空");
            }
            BizAssertUtil.notEmpty(deliveryReq.getExpressNumber(), "快递单号不能为空");
        }

        if (orderExtendPO.getKingdeeStockPushMode() != null && orderExtendPO.getKingdeeStockPushMode().equals(StoreJindieStockFlagEnum.WAREHOUSE.getCode())) {
            BizAssertUtil.isTrue(StringUtils.isEmpty(deliveryReq.getDeliveryWarehouse()), "金蝶库存推送方式，仓库编码不能为空！");
        }

        // BAPP客户经理发货,仅允许对特定商家发货
        if (deliveryReq.isManagerDeliver()) {
            BizAssertUtil.isTrue(!privilegeService.storeDeliveryPrivilege(orderPO.getOrderSn()),
                    "客户经理发货，仅允许对特定商家发货，该商家不支持发货");
        }

        // 站长发货,仅允许对特定商家发货
        if (deliveryReq.isStationMasterDeliver()) {
            BizAssertUtil.isTrue(!privilegeService.storeDeliveryPrivilege(orderPO.getOrderSn()),
                    "站长发货，仅允许对特定商家发货，该商家不支持发货");
        }

        if (deliveryReq.getChannel() == null || ChannelEnum.getValue(deliveryReq.getChannel().getValue()) == null) {
            // 供应商履约模式的订单不允许商家操作发货
            BizAssertUtil.isTrue(
                    StringUtils.isNotEmpty(orderPO.getPerformanceModes()) && orderPO.getPerformanceModes()
                            .contains(OrderPerformanceModeEnum.PERFORMANCE_MODE_SUPPLIER.getValue().toString()),
                    "抱歉，受外部渠道限制，无法操作!");
            // 厂商模式订单不允许商家操作发货
            BizAssertUtil.isTrue(
                    StringUtils.isNotEmpty(orderPO.getPerformanceModes()) && orderPO.getPerformanceModes()
                            .contains(OrderPerformanceModeEnum.PERFORMANCE_MODE_MFR_DEALER.getValue().toString()),
                    "抱歉，厂商模式订单仅允许经销商操作发货!");
        }

        // 检查订单发货文件资料是否上传
        List<Long> orderProductIdList = orderProductPOList.stream().map(OrderProductPO::getOrderProductId).collect(Collectors.toList());
        Boolean isUpload = orderTradeProofService.checkOrderProofUpload(orderPO.getOrderSn(), orderProductIdList, ProofSceneEnum.DELIVERY);
        if (!isUpload) {
            throw new BusinessException("请先上传订单对应的发货资料！");
        }

        // 拼车发货限制,遍历校验商品wms总发货数<订单总发货数
        Boolean storeOnList = shopIntegration.isStoreOnList(WhiteListEnum.CARPOOL, orderPO.getStoreId());
        // 拼车校验白名单商家，校验发货数
        if (storeOnList) {
            for (OrderProductPO orderProductPO : orderProductPOList) {
                if (StringUtils.isEmpty(orderProductPO.getChannelSkuId())) {
                    continue;
                }

                // 查询wms总发货数量
                String createTimeBeg = "2022-03-01 00:00:00";
                Long wmsDeliverTotalQty = wmsIntegration.getDeliverTotalQty(orderProductPO.getChannelSkuId(),
                        orderExtendPO.getBranch(), orderPO.getStoreId(), createTimeBeg, null);
                // 商家在wms不存在，无需校验
                if (wmsDeliverTotalQty == null) {
                    break;
                }

                int orderDeliverTotalQty = 0;
                // 分支为空，默认拼车发货数为0，拦截
                if (StringUtils.isEmpty(orderExtendPO.getBranch())) {
                    throw new BusinessException("该订单管护分支为空，受拼车发货限制，暂无法发货");
                }

                // 根据单位编码获取物料的所有编码
                List<String> skuUnitCodeList =
                        erpIntegration.getSkuUnitCodeListBySkuUnitCode(orderProductPO.getChannelSkuId());
                List<Map<String, Object>> maps = orderProductMapper.obtainProductDeliveryNum(
                        orderPO.getStoreId(), skuUnitCodeList, orderExtendPO.getBranch());
                Map<String, Object> thisProductQty = new HashMap<>(3);
                thisProductQty.put("skuId", orderProductPO.getChannelSkuId());
                thisProductQty.put("skuUnit", orderProductPO.getChannelSkuUnit());
                thisProductQty.put("deliveryQty", orderProductPO.getProductNum() - orderProductPO.getReturnNumber());
                maps.add(thisProductQty);
                // 单位转换
                List<UnitConvertVo> unitConvertVos = erpIntegration.unitConvert(maps);
                orderDeliverTotalQty =
                        unitConvertVos.stream().map(UnitConvertVo::getPackageNum).mapToInt(BigDecimal::intValue).sum();

                log.info("order delivery check deliverQty,order:{},wmsDeliverTotalQty:{},orderDeliverTotalQty:{}",
                        orderPO.getOrderSn(), wmsDeliverTotalQty, orderDeliverTotalQty);
                if (wmsDeliverTotalQty < orderDeliverTotalQty) {
                    throw new BusinessException(String.format("商品[%s]发货数大于拼车已发货数，超发数量为%d，暂无法发货",
                            orderProductPO.getGoodsName(), orderDeliverTotalQty - wmsDeliverTotalQty));
                }
            }
        }

        if (vendor.getStoreId() != null) {
            // 权限校验
            OrderLocalUtils.checkOrderPermissions(vendor.getStoreId(), orderPO.getStoreId(),
                    orderPO.getRecommendStoreId(), deliveryReq.getDistribution(), orderPO.getOrderType());
        }

        // 店铺、非自提订单 仅允许ERP操作发货
        if ((deliveryReq.getChannel() == null || deliveryReq.getChannel() != OrderCreateChannel.CHANNEL_WMS)
                && !OrderPatternEnum.SELF_LIFT.getValue().equals(orderPO.getOrderPattern())) {
            Boolean spellingStore = shopIntegration.isStoreOnList(WhiteListEnum.SPELLING_FULL_GIFT, orderPO.getStoreId());
            if (spellingStore) {
                throw new BusinessException("拼单满赠，店铺非自提订单，仅允许ERP操作发货");
            }
        }
        // 店铺、非自提订单 仅允许ERP操作发货
        if ((deliveryReq.getChannel() == null || deliveryReq.getChannel() != OrderCreateChannel.CHANNEL_WMS)
                && !OrderPatternEnum.SELF_LIFT.getValue().equals(orderPO.getOrderPattern())) {
            Boolean spellingStore = shopIntegration.isStoreOnList(WhiteListEnum.ERP_LOGISTICS_DELIVERY, orderPO.getStoreId());
            if (spellingStore) {
                throw new BusinessException("店铺非自提订单，仅允许ERP操作发货！");
            }
        }

        /**
         * 如果快递单号有效，但是该单号通过订单查询已完结发货，或者在全域内被重复使用到不同的订单上≥3次， 则提示【该物流单号已重复使用，涉嫌虚假物流】，告警但不进行拦截
         */
        if (StringUtils.isNotEmpty(deliveryReq.getExpressNumber()) && deliveryReq.isExpressNumberValid()) {
            orderService.validExpressNumberReuseCount(deliveryReq.getExpressNumber(), vendor);
        }
        //满赠订单是否捆绑发货校验
        if (orderPO.getOrderType().equals(OrderTypeEnum.FULL_GIFT.getValue())) {
            //本次发货的商品分组，根据赠品分组id进行分组
            Map<Integer, List<OrderProductPO>> deliveryOrderProductMap = orderProductPOList.stream()
                    .filter(x -> x.getGiftGroup() > 0)
                    .collect(Collectors.groupingBy(OrderProductPO::getGiftGroup));

            List<OrderProductPO> orderProductPOS =
                    orderProductService.lambdaQuery().eq(OrderProductPO::getOrderSn, orderPO.getOrderSn())
                            .list();
            Map<Integer, List<OrderProductPO>> orderProductDb = orderProductPOS.stream()
                    .collect(Collectors.groupingBy(OrderProductPO::getGiftGroup));

            for (Map.Entry<Integer, List<OrderProductPO>> deliveryOrderProduct : deliveryOrderProductMap.entrySet()) {
                Integer key = deliveryOrderProduct.getKey();
                if (orderProductDb.get(key).size() != deliveryOrderProduct.getValue().size()) {
                    throw new BusinessException("赠品商品行必须与赠品同时发货");
                }
            }
        }

        return orderPO;
    }

    @Deprecated
    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "masterdbTx")
    public Boolean delivery(OrderDeliveryReq deliveryReq, Vendor vendor) {

        // 参数校验
        BizAssertUtil.isTrue(!OrderDeliverTypeEnum.isDeliverType(deliveryReq.getDeliverType()), "发货类型错误");
        BizAssertUtil.isTrue(CollectionUtils.isEmpty(deliveryReq.getOrderProductIds())
                && CollectionUtils.isEmpty(deliveryReq.getProductIds()), "发货商品不能为空");
        BizAssertUtil.isTrue(!OrderDeliverTypeEnum.isLogisticsDelivery(deliveryReq.getDeliverType())
                && !deliveryReq.getAllowNoLogistics(), "非白名单商家不支持无物流发货");
        if (deliveryReq.getDeliverType().equals(OrderDeliverTypeEnum.LOGISTICS_DELIVERY.getValue())) {
            if ((deliveryReq.getExpressId() == null || deliveryReq.getExpressId() == 0)
                    && StringUtils.isBlank(deliveryReq.getExpressCompanyCode())) {
                throw new BusinessException("快递公司id/编号不能为空");
            }
            BizAssertUtil.notEmpty(deliveryReq.getExpressNumber(), "快递单号不能为空");
        }

        // 数据校验
        OrderPO orderPODb = orderModel.getOrderByOrderSn(deliveryReq.getOrderSn());
        BizAssertUtil.notNull(orderPODb, "订单不存在，请重试");

        // BAPP客户经理发货,仅允许对特定商家发货
        if (deliveryReq.isManagerDeliver()) {
            BizAssertUtil.isTrue(
                    ObjectUtils.isNotEmpty(orderPODb.getStoreId())
                            && ObjectUtils.isNotEmpty(storeForDeliverConfig.getStoreId())
                            && !storeForDeliverConfig.getStoreId().contains(String.valueOf(orderPODb.getStoreId())),
                    "客户经理发货，仅允许对特定商家发货，该商家不支持发货");
        }

        // 站长发货,仅允许对特定商家发货
        if (deliveryReq.isStationMasterDeliver()) {
            BizAssertUtil.isTrue(
                    ObjectUtils.isNotEmpty(orderPODb.getStoreId())
                            && ObjectUtils.isNotEmpty(storeForDeliverConfig.getStoreId())
                            && !storeForDeliverConfig.getStoreId().contains(String.valueOf(orderPODb.getStoreId())),
                    "站长发货，仅允许对特定商家发货，该商家不支持发货");
        }

        // 非客户经理发货，非站长发货和特定渠道单发货不校验权限，Andy，增加非客户经理发货判断
        if (!deliveryReq.isStationMasterDeliver() &&
                !deliveryReq.isManagerDeliver() && (deliveryReq.getChannel() == null || (deliveryReq.getChannel() != null
                && ChannelEnum.getValue(deliveryReq.getChannel().getValue()) == null))) {
            // 权限校验
            OrderLocalUtils.checkOrderPermissions(vendor.getStoreId(), orderPODb.getStoreId(),
                    orderPODb.getRecommendStoreId(), deliveryReq.getDistribution(), orderPODb.getOrderType());
            // 供应商履约模式的订单不允许商家操作发货
            BizAssertUtil.isTrue(
                    StringUtils.isNotEmpty(orderPODb.getPerformanceModes()) && orderPODb.getPerformanceModes()
                            .contains(OrderPerformanceModeEnum.PERFORMANCE_MODE_SUPPLIER.getValue().toString()),
                    "抱歉，受外部渠道限制，无法操作!");
        }

        // 【发货】校验，修改物流信息不校验
        if (orderPODb.getOrderState() < OrderStatusEnum.WAIT_RECEIPT.getValue()
                || OrderStatusEnum.TRADE_CLOSE.getValue().equals(orderPODb.getOrderState())) {
            String verifyResult = this.orderDeliveryVerify(orderPODb, vendor);
            if (!OrderConst.CHECK_SUCCESS.equals(verifyResult)) {
                throw new BusinessException(verifyResult);
            }
        }

        // 保存物流信息
        OrderLogisticPO logisticPO = orderLogisticService.saveLogistic(orderPODb, deliveryReq, vendor.getVendorName(), 0);

        if (CollectionUtils.isEmpty(deliveryReq.getOrderProductIds())) {
            LambdaQueryWrapper<OrderProductPO> productQuery = new LambdaQueryWrapper<>();
            productQuery.eq(OrderProductPO::getOrderSn, deliveryReq.getOrderSn())
                    .in(OrderProductPO::getProductId, deliveryReq.getProductIds())
                    .eq(OrderProductPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                    .select(OrderProductPO::getOrderProductId);
            List<OrderProductPO> productPOS = orderProductService.list(productQuery);
            List<Long> orderProductIds =
                    productPOS.stream().map(OrderProductPO::getOrderProductId).collect(Collectors.toList());
            deliveryReq.setOrderProductIds(orderProductIds);
        }

        // 订单商品关联物流
        orderProductService.updateLogistic(deliveryReq.getOrderSn(), deliveryReq.getOrderProductIds(),
                logisticPO.getLogisticId());

        /**
         * 如果快递单号有效，但是该单号通过订单查询已完结发货，或者在全域内被重复使用到不同的订单上≥3次， 则提示【该物流单号已重复使用，涉嫌虚假物流】，告警但不进行拦截
         */
        if (StringUtils.isNotEmpty(deliveryReq.getExpressNumber()) && deliveryReq.isExpressNumberValid()) {
            orderService.validExpressNumberReuseCount(deliveryReq.getExpressNumber(), vendor);
        }

        /**
         * 发货操作：订单状态在待收货之前 1. 更新订单状态，发货时间 2. 记录发货日志 3. 推送发货消息
         */
        if (orderPODb.getOrderState() < OrderStatusEnum.WAIT_RECEIPT.getValue()) {
            this.orderDelivery(orderPODb, vendor, deliveryReq.getChannel(), OrderConst.LOG_ROLE_VENDOR,
                    deliveryReq.getOrderProductIds());
            CompletableFuture.runAsync(() -> {
                this.deliveryMessage(orderPODb, logisticPO, vendor);
            });
        } else {
            // 异步写日志
            CompletableFuture.runAsync(() -> {
                orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_VENDOR, vendor.getVendorId(), vendor.getVendorName(),
                        orderPODb.getOrderSn(), orderPODb.getOrderState(), orderPODb.getOrderState(),
                        orderPODb.getLoanPayState(), "商品发货", deliveryReq.getChannel());
            });
        }

        return Boolean.TRUE;
    }

    @Override
    public Result<Boolean> deliveryForErp(ErpOrderDeliveryRequest erpDeliveryReq) {

        // 查询商户信息
        AssertUtil.notNullOrZero(erpDeliveryReq.getStoreId(), "商户ID不能为空");
        VendorExample vendorExample = new VendorExample();
        vendorExample.setStoreId(erpDeliveryReq.getStoreId());
        List<Vendor> vendorList = vendorFeignClient.getVendorList(vendorExample);
        AssertUtil.notEmpty(vendorList, "商户不存在");
        Vendor vendor = vendorList.get(0);

        // 调用发货
        OrderDeliveryReq deliveryReq = new OrderDeliveryReq();
        deliveryReq.setOrderSn(erpDeliveryReq.getOrderSn());
        deliveryReq.setOrderProductIds(erpDeliveryReq.getOrderProductIds());
        deliveryReq.setDeliverType(erpDeliveryReq.getDeliverType());
        deliveryReq.setExpressId(erpDeliveryReq.getExpressId());
        deliveryReq.setExpressNumber(erpDeliveryReq.getExpressNumber());
        deliveryReq.setDeliverName(erpDeliveryReq.getDeliverName());
        deliveryReq.setDeliverMobile(erpDeliveryReq.getDeliverMobile());
        this.deliveryV2(deliveryReq, vendor);

        // 操作日志
        String opt = "【订单发货成功】orderSn: " + erpDeliveryReq.getOrderSn();
        VendorLogSendVO vendorLogSendVO =
                new VendorLogSendVO(vendor.getVendorId(), vendor.getVendorName(), "ERP", opt, "ERP");
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_SELLERLOG_MSG, vendorLogSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(vendorLogSendVO), e);
        }

        return ResultUtils.buildSuccessResult(Boolean.TRUE);
    }

    /**
     * 批量发货（都是无物流发货）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchDeliveryV2(OrderBatchDeliveryDTO orderBatchDeliveryDTO, Vendor vendor) {
        // 记录失败数
        AtomicReference<Integer> failCount = new AtomicReference<>(0);

        orderBatchDeliveryDTO.getOrderSnList().forEach(orderSn -> {
            // 查询订单信息
            OrderPO orderPODb = orderModel.getOrderByOrderSn(orderSn);
            BizAssertUtil.notNull(orderPODb, "订单不存在，请重试");

            // 查询订单商品信息
            LambdaQueryWrapper<OrderProductPO> orderProductQuery = new LambdaQueryWrapper<>();
            orderProductQuery.eq(OrderProductPO::getOrderSn, orderSn);
            List<OrderProductPO> orderProductPOList = orderProductMapper.selectList(orderProductQuery);
            BizAssertUtil.notEmpty(orderProductPOList, orderPODb.getOrderSn() + "订单不存在商品，请重试");
            List<Long> orderProductIds =
                    orderProductPOList.stream().map(OrderProductPO::getOrderProductId).collect(Collectors.toList());

            List<Long> productIds =
                    orderProductPOList.stream().map(OrderProductPO::getProductId).collect(Collectors.toList());


            // 查询订单分支
            OrderExtendPO orderExtendPO =
                    orderExtendService.lambdaQuery().eq(OrderExtendPO::getOrderSn, orderPODb.getOrderSn())
                            .select(OrderExtendPO::getExtendId, OrderExtendPO::getBranch).one();

            // 组装参数
            Boolean allowNoLogistics =
                    storeIsolateWhiteListFeignClient.isEmptyLogisticsInfoAllowed(orderPODb.getStoreId());
            OrderDeliveryReq deliveryReq = new OrderDeliveryReq(orderSn, orderProductIds, productIds, allowNoLogistics,
                    orderBatchDeliveryDTO.getDeliverName(), orderBatchDeliveryDTO.getDeliverMobile(), OrderCreateChannel.WEB);

            //获取可发货数量，根据orderProductId组装参数 orderProductDetail
            //可发货数量 = 购买数量 - 已发货数量 - 仅退款数量
            List<OrderProductDeliverDTO> orderProductDeliveryList = this.getProductDeliveryListByProductId(deliveryReq.getOrderSn(), deliveryReq.getProductIds());
            deliveryReq.setOrderProductDetail(orderProductDeliveryList);

            // 数据校验--参数检验、权限校验、物流单号校验
            deliveryParamValidate(orderPODb, orderProductPOList, orderExtendPO, vendor, deliveryReq);

            if (orderPODb.getOrderState() < OrderStatusEnum.WAIT_RECEIPT.getValue()) {
                /*Long logisticId = orderProductPOList.get(0).getLogisticId();
                if (Objects.nonNull(logisticId) && !CommonConst.DEFAULT_LONG.equals(logisticId)) {
                    // update 物流信息
                    updateDeliveryInfo(logisticId, deliveryReq, orderPODb, vendor);
                } else {
                    //erp履约渠道,销售出库可用库存校验；增加发货中状态
                    boolean outboundFlag = orderProductPOList.get(0).getPerformanceChannel().equals(OrderPerformanceChannelEnum.PERFORMANCE_CHANNEL_ERP.getValue());
                    boolean erpCheckResult = true;
                    if(outboundFlag) {
                        //erp履约渠道,销售出库可用库存校验
                        try {
                            performanceService.erpOutBoundCheck(orderPODb, orderProductPOList, orderExtendPO);
                        } catch (Exception e) {
                            log.warn("批量发货，erp履约渠道,销售出库可用库存校验异常,",e.getMessage());
                            erpCheckResult = false;
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    }
                    // add 物流信息
                    if(erpCheckResult) {
                        String deliveryResult = addDeliveryInfo(orderProductIds, deliveryReq, orderPODb, vendor,false);
                        if (!OrderConst.CHECK_SUCCESS.equals(deliveryResult)) {
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    }
                }*/

                //erp履约渠道,销售出库可用库存校验；增加发货中状态
                boolean outboundFlag = orderProductPOList.get(0).getPerformanceChannel().equals(OrderPerformanceChannelEnum.PERFORMANCE_CHANNEL_ERP.getValue()) && tianjieOrderSnCheck(orderPODb.getOrderSn());
                boolean erpCheckResult = true;
                if (outboundFlag) {
                    //erp履约渠道,销售出库可用库存校验
                    try {
                        performanceService.erpOutBoundCheck(orderPODb, orderProductPOList, orderExtendPO, deliveryReq);
                    } catch (Exception e) {
                        log.warn("批量发货，erp履约渠道,销售出库可用库存校验异常,", e.getMessage());
                        erpCheckResult = false;
                        failCount.getAndSet(failCount.get() + 1);
                    }
                }
                // add 物流信息
                if (erpCheckResult) {
                    String deliveryResult = addDeliveryInfo(orderProductIds, deliveryReq, orderPODb, vendor, outboundFlag);
                    if (!OrderConst.CHECK_SUCCESS.equals(deliveryResult)) {
                        failCount.getAndSet(failCount.get() + 1);
                    }
                }

            }

        });

        String returnMsg = "订单批量发货成功";
        if (failCount.get() > 0) {
            returnMsg = failCount.get() + "笔订单因处于退款中等原因，无法发货";
        }

        return returnMsg;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public String batchDelivery(String orderSns, Vendor vendor, OrderCreateChannel channel) {
        String[] orderArr = orderSns.split(",");

        AtomicReference<Integer> failCount = new AtomicReference<>(0);

        Arrays.stream(orderArr).forEach(orderSn -> {

           /* OrderDeliveryReq deliveryReq = new OrderDeliveryReq(orderSn, null, Boolean.TRUE,
                    orderOfflineDeliveryName, orderOfflineDeliveryMobile, channel);
            this.deliveryV2(deliveryReq, vendor);*/
            // 查询订单
            OrderPO orderPO = orderModel.getOrderByOrderSn(orderSn);

            // 订单【发货】校验
            String verifyResult = this.orderDeliveryVerify(orderPO, vendor);

            if (!OrderConst.CHECK_SUCCESS.equals(verifyResult)) {
                failCount.getAndSet(failCount.get() + 1);

            } else {
                // 订单商品发货状态：已发货
                orderProductService.orderProductDelivery(orderSn);
                // 订单发货
                this.orderDelivery(orderPO, vendor, channel, OrderConst.LOG_ROLE_ADMIN, null);
            }
        });

        String returnMsg = "订单批量发货成功";
        if (failCount.get() > 0) {
            returnMsg = failCount.get() + "笔订单因处于退款中等原因，无法发货";
        }

        return returnMsg;
    }

    /**
     * 发货订单校验
     *
     * @param order 订单
     * @return 校验结果
     */
    private String orderDeliveryVerify(OrderPO order, Vendor vendor) {
        if (Objects.isNull(order)) {
            return "订单不存在，请重试";
        }
        if (order.getLockState() > 0) {
            return "有商品退款中，待退款处理完成再操作。";
        }

        if (orderReturnModel.whetherHasReturningProduct(order.getOrderSn())) {
            if (order.getPerformanceModes() != null && order.getPerformanceModes()
                    .contains(OrderPerformanceModeEnum.PERFORMANCE_MODE_SUPPLIER.getValue().toString())) {
                log.warn("【云中鹤-发货失败】订单号：" + order.getOrderSn() + ",原因：该订单处于退款中！");
            }
            return "该订单处于退款中";
        }
        if (OrderConst.IS_DELIVER_1 != order.getIsDelivery()) {
            return "该订单不允许进行发货";
        }
        if (!OrderStatusEnum.WAIT_DELIVER.getValue().equals(order.getOrderState())
                && !OrderStatusEnum.PART_DELIVERED.getValue().equals(order.getOrderState())) {
            return "该订单状态不允许操作发货";
        }

        // 订单自动发货校验：金融规则订单、自动发货类型，不允许人工发货
        if (PayMethodEnum.isLoanPay(PayMethodEnum.getValue(order.getPaymentCode()))) {
            OrderExtendFinancePO financePO = financeService.getByOrderSn(order.getOrderSn());
            if (financePO != null && financePO.getDeliverMethod().equals(1)
                    && !OrderConst.LOG_USER_ID.equals(vendor.getVendorId())) {
                return "该订单为金融规则自动发货订单，不允许人工发货";
            }
        }
        // 卡券订单不允许发货
        if (Objects.equals(order.getOrderPattern(), OrderPatternEnum.COUPON_CENTRE.getValue())) {
            return "卡券订单不允许发货";
        }
        return OrderConst.CHECK_SUCCESS;
    }

    /**
     * 发货操作：订单状态在待收货之前 1. 更新订单状态，发货时间 2. 记录发货日志 3. 推送发货消息
     */
    public Boolean orderDelivery(OrderPO orderPODb, Vendor vendor, OrderCreateChannel channel, Integer logRole,
                                 List<Long> orderProductIds) {
        OrderPO orderPO = new OrderPO();
        orderPO.setOrderId(orderPODb.getOrderId());
        orderPO.setOrderSn(orderPODb.getOrderSn());
        orderPO.setStoreId(orderPODb.getStoreId());
        orderPO.setDeliverTime(new Date());
        // 判断订单是否全部发货：是-待收货 、 否-部分发货
        if (orderProductService.isAllDelivery(orderPODb.getOrderSn())) {
            orderPODb.setDeliverTime(new Date());
            this.orderAllDeliveryItems(orderPODb);
        } else {
            orderPO.setOrderState(OrderStatusEnum.PART_DELIVERED.getValue());
            // 推送发货消息（订单状态的变更）
            orderCreateHelper.addOrderChangeEvent(orderPO, OrderEventEnum.PART_DELIVERY, orderPO.getDeliverTime());
        }
        // 发货MQ推送消息
        orderCreateHelper.addOrderDelivery(orderPODb.getOrderSn(), orderProductIds);
        boolean update = updateById(orderPO);
        if (!update) {
            throw new MallException("更新订单状态失败");
        }


        CompletableFuture.runAsync(() -> {
            orderLogModel.insertOrderLog(logRole, vendor.getVendorId(), vendor.getVendorName(), orderPO.getOrderSn(),
                    orderPODb.getOrderState(), orderPO.getOrderState(), LoanStatusEnum.APPLY_SUCCESS.getValue(), "商品发货",
                    channel);
            //换货日志记录
            orderModel.insertExchangeOrderLog(orderPODb, logRole, vendor.getVendorId(), vendor.getVendorName(), "商品发货", channel);
        });
        return Boolean.TRUE;
    }

    /**
     * 订单商品全部发货完成后处理的事项
     *
     * @param orderPO 订单信息
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = "masterdbTx")
    public void orderAllDeliveryItems(OrderPO orderPO) {
        log.info("orderAllDeliveryItems orderSn = {}", orderPO.getOrderSn());
        LambdaUpdateWrapper<OrderPO> orderUpdateWrapper = Wrappers.lambdaUpdate(OrderPO.class);
        orderUpdateWrapper.eq(OrderPO::getOrderSn, orderPO.getOrderSn())
                .eq(OrderPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .set(OrderPO::getOrderState, OrderStatusEnum.WAIT_RECEIPT.getValue())
                .set(OrderPO::getAutoReceiveDay, systemSettingObtainHelper.getTimeLimitOfAutoReceive());

        // 设置多久后自动收货,默认是7天；到家服务订单15天
        List<Integer> performanceModeList = JSONObject.parseArray(orderPO.getPerformanceModes(), Integer.class);
        if (performanceModeList.contains(OrderPerformanceModeEnum.PERFORMANCE_MODE_HOME_SERVICE.getValue())) {
            orderUpdateWrapper.set(OrderPO::getAutoReceiveDay, homeServiceAutoReceiveDays);
        }
        List<OrderLogisticPO> logisticPOList = orderLogisticService.getOrderLogisticByOrderSn(orderPO.getOrderSn(), null);
        //白名单店铺及无物流发货方式，延迟签收时间为45天
        if (CollectionUtils.isNotEmpty(logisticPOList) && performanceService.isExtendReceiveStore(orderPO.getStoreId())) {
            long deliverType = logisticPOList.stream().filter(x -> x.getDeliverType().equals(OrderConst.DELIVER_TYPE_1)).count();
            if (deliverType > 0) {
                orderUpdateWrapper.set(OrderPO::getAutoReceiveDay, extendAutoReceiveDays);
                //如果是金融规则的订单，将金融规则里面的默认签收时间也改为45天
                financeService.updateAutoReceiveDays(orderPO.getOrderSn(), extendAutoReceiveDays);
            }
        }
        if (!OrderConst.STORE_TYPE_SELF_1.equals(orderPO.getStoreIsSelf()) && PayMethodEnum.isLoanPay(orderPO.getPaymentCode())) {
            log.info("非自营且是贷款类支付，设置自动收货天数,orderSn:{}", orderPO.getOrderSn());
            // 非自营且贷款类支付
            List<DictionaryItemVO> dictionary = bmsIntegration.listDictionaryItemsByTypeCode(CommonConst.AUTO_RECEIVE_DAYS, CommonConst.MALL_SYSTEM_MANAGE_ID);
            if (!CollectionUtils.isEmpty(dictionary)) {
                DictionaryItemVO dictionaryItemVO = dictionary.get(0);
                Integer autoReceiveDays = Integer.valueOf(dictionaryItemVO.getItemCode());
                log.info("非自营且是贷款类支付，设置自动收货天数,days:{}", autoReceiveDays);
                orderUpdateWrapper.set(OrderPO::getAutoReceiveDay, autoReceiveDays);
            } else {
                log.info("非自营且是贷款类支付，设置自动收货天数失败，bms未配置字典");
            }
        }
        boolean updateResult = orderService.update(orderUpdateWrapper);

        if (updateResult) {
            // 订单变为待签收，记录佣金激励费和佣金服务费资金项
            if (orderAmountValidation.needCommissionIncentive(orderPO)) {
                orderAmountRecordService.initOrderAmountState(orderPO, OrderAmountTypeEnum.ORDER_COMMISSION_INCENTIVE_FEE);
                orderAmountRecordService.initOrderAmountState(orderPO, OrderAmountTypeEnum.COMMISSION_INCENTIVE_SERVICE_FEE);
            }

            // 推送发货消息
            orderCreateHelper.addOrderChangeEvent(orderPO, OrderEventEnum.DELIVERY, orderPO.getDeliverTime());
        }
    }

    @Override
    public void deliveryMessage(OrderPO orderPO, OrderLogisticPO logisticPO, Vendor vendor) {

        /**
         * 消息推送
         */
        // 会员消息通知
        List<MessageSendProperty> messageSendPropertyList = new ArrayList<>();
        messageSendPropertyList.add(new MessageSendProperty("orderSn", orderPO.getOrderSn()));
        // 微信通知
        String result = String.format("订单号%s已发货，请前往乡助查看", orderPO.getOrderSn());
        List<MessageSendProperty> messageSendPropertyList4CashWx = new ArrayList<>();
        messageSendPropertyList4CashWx.add(new MessageSendProperty("first", "您的订单已发货"));
        messageSendPropertyList4CashWx.add(new MessageSendProperty("keyword1", orderPO.getMemberName()));
        messageSendPropertyList4CashWx.add(new MessageSendProperty("keyword2", "发货提醒通知"));
        messageSendPropertyList4CashWx.add(new MessageSendProperty("keyword3", result));
        messageSendPropertyList4CashWx.add(new MessageSendProperty("keyword4", TimeUtil.getDateTimeString(new Date())));
        messageSendPropertyList4CashWx.add(new MessageSendProperty("remark", "服务农村最后一百米"));

        String msgLinkInfo = "{\"type\":\"order_news\",\"orderSn\":\"" + orderPO.getOrderSn() + "\"}";
        MessageSendVO messageSendVO = new MessageSendVO(messageSendPropertyList, messageSendPropertyList4CashWx,
                "deliveryTime", orderPO.getMemberId(), MemberTplConst.GOODS_DELIVERY_REMINDER, msgLinkInfo);
        // 发送到mq
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_MEMBER_MSG, messageSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(messageSendVO), e);
        }

        // 操作日志
        HttpServletRequest request =
                ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String opt = "【订单发货成功】orderSn: " + orderPO.getOrderSn();
        VendorLogSendVO vendorLogSendVO = new VendorLogSendVO(vendor.getVendorId(), vendor.getVendorName(),
                request.getRequestURI(), opt, WebUtil.getRealIp(request));
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_SELLERLOG_MSG, vendorLogSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(vendorLogSendVO), e);
        }

    }

    @Override
    public List<DeliveryFailureVO> batchReplenishDeliveryV2(List<OrderDeliveryMessageReq> deliveryMessageList,
                                                            Vendor vendor) {
        List<DeliveryFailureVO> failOrder = new LinkedList<>();
        Boolean allowNoLogistics = storeIsolateWhiteListFeignClient.isEmptyLogisticsInfoAllowed(vendor.getStoreId());
        List<String> orderSnList = deliveryMessageList.stream()
                .map(OrderDeliveryMessageReq::getOrderSn)
                .distinct().collect(Collectors.toList());
        Set<String> giftOrderSnList = this.lambdaQuery()
                .eq(OrderPO::getOrderType, OrderTypeEnum.FULL_GIFT.getValue())
                .in(OrderPO::getOrderSn, orderSnList)
                .list().stream().map(OrderPO::getOrderSn)
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(giftOrderSnList)) {
            String giftOrderSns = String.join(",", giftOrderSnList);
            throw new BusinessException("导入订单中:" + giftOrderSns + "为满赠订单，满赠请手动发货");
        }

        deliveryMessageList.parallelStream().forEach(deliveryMessageReq -> {
            // 查询订单商品信息
            LambdaQueryWrapper<OrderProductPO> orderProductQuery = new LambdaQueryWrapper<>();
            orderProductQuery.eq(OrderProductPO::getOrderSn, deliveryMessageReq.getOrderSn())
                    .eq(OrderProductPO::getProductId, deliveryMessageReq.getProductId())
                    .eq(OrderProductPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                    .select(OrderProductPO::getOrderProductId, OrderProductPO::getDeliveryDeadline);

            OrderProductPO orderProductPO;
            try {
                orderProductPO = orderProductService.getOne(orderProductQuery);
                if (null == orderProductPO) {
                    this.saveDeliverFailOrder(deliveryMessageReq, failOrder, "查询订单商品为空");
                    return;
                }
                if (orderProductPO.getDeliveryDeadline().after(new Date(0))) {
                    this.saveDeliverFailOrder(deliveryMessageReq, failOrder, "现款现货商品不支持批量发货");
                    return;
                }
            } catch (Exception e) {
                this.saveDeliverFailOrder(deliveryMessageReq, failOrder, "查询订单商品失败");
                return;
            }

            // 单个商品发货/修改物流
            OrderDeliveryReq deliveryReq = new OrderDeliveryReq(deliveryMessageReq);
            deliveryReq.setChannel(OrderCreateChannel.WEB);
            deliveryReq.setOrderProductIds(Collections.singletonList(orderProductPO.getOrderProductId()));
            deliveryReq.setAllowNoLogistics(allowNoLogistics);
            try {
                orderService.deliveryV2(deliveryReq, vendor);
            } catch (Exception e) {
                this.saveDeliverFailOrder(deliveryMessageReq, failOrder, e.getMessage());
            }
        });

        log.warn("发货失败数量：{} 明细: {}", failOrder.size(), JSON.toJSONString(failOrder));
        return failOrder;
    }

    /**
     * 保存失败的导入发货单
     *
     * @param deliveryMessageReq 发货信息
     * @param failOrders         失败的订单集合
     * @param failReason         失败原因
     */
    private void saveDeliverFailOrder(OrderDeliveryMessageReq deliveryMessageReq, List<DeliveryFailureVO> failOrders,
                                      String failReason) {
        DeliveryFailureVO failureVO = new DeliveryFailureVO();
        BeanUtils.copyProperties(deliveryMessageReq, failureVO);
        failureVO.setDeliverAddress(failReason);
        failOrders.add(failureVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "masterdbTx")
    public List<String> batchReplenishDelivery(List<OrderDeliveryMessageReq> deliveryMessageList, Vendor vendor) {

        List<String> failOrder = new LinkedList();

        Boolean allowNoLogistics = storeIsolateWhiteListFeignClient.isEmptyLogisticsInfoAllowed(vendor.getStoreId());

        for (OrderDeliveryMessageReq deliveryMessageReq : deliveryMessageList) {
            // 查询订单商品信息
            LambdaQueryWrapper<OrderProductPO> query = new LambdaQueryWrapper<>();
            query.eq(OrderProductPO::getOrderSn, deliveryMessageReq.getOrderSn())
                    .eq(OrderProductPO::getProductId, deliveryMessageReq.getProductId())
                    .eq(OrderProductPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                    .select(OrderProductPO::getOrderProductId);

            OrderProductPO orderProductPO;
            try {
                orderProductPO = orderProductService.getOne(query);
                if (null == orderProductPO) {
                    failOrder.add(deliveryMessageReq.getOrderSn() + "：查询订单商品为空");
                    continue;
                }
            } catch (Exception e) {
                failOrder.add(deliveryMessageReq.getOrderSn() + "：查询订单商品失败");
                continue;
            }

            // 单个商品发货/修改物流
            OrderDeliveryReq deliveryReq = new OrderDeliveryReq();
            deliveryReq.setOrderProductIds(Arrays.asList(orderProductPO.getOrderProductId()));
            deliveryReq.setDeliverType(deliveryMessageReq.getDeliveryType());
            deliveryReq.setAllowNoLogistics(allowNoLogistics);

            BeanUtils.copyProperties(deliveryMessageReq, deliveryReq);
            try {
                deliveryV2(deliveryReq, vendor);
            } catch (Exception e) {
                failOrder.add(deliveryMessageReq.getOrderSn() + "：" + e.getMessage());
            }
        }

        log.warn(failOrder.toString());
        return failOrder;
    }

    @Override
    public List<TracesResult> getOrderTrace(String orderSn, Long orderProductId) {

        List<OrderProductDeliveryVO> productDeliveryVOS =
                orderProductMapper.orderProductDeliveryTrace(orderSn, orderProductId);
        AssertUtil.notEmpty(productDeliveryVOS, "订单商品为空");
        log.info("查询订单物流，物流信息：{}", productDeliveryVOS);

        List<TracesResult> tracesResultList = new ArrayList<>(productDeliveryVOS.size());
        for (OrderProductDeliveryVO deliveryVO : productDeliveryVOS) {
            TracesResult tracesResult = getTracesResult(deliveryVO);
            tracesResultList.add(tracesResult);
        }

        return tracesResultList;
    }

    @Override
    public List<OrderFrontDeliveryVO> frontDeliveryList(String orderSn, Long orderProductId) {

        List<OrderProductDeliveryVO> productDeliveryVOS =
                orderProductMapper.orderProductDelivery(orderSn, orderProductId);
        AssertUtil.notEmpty(productDeliveryVOS, "订单商品为空");
        log.info("查询订单物流，物流信息：{}", productDeliveryVOS);

        // 查询订单收货信息
        OrderExtendPO orderExtendPO = orderExtendModel.getOrderExtendByOrderSn(orderSn);
        OrderExtendVO orderExtendVO = new OrderExtendVO();
        BeanUtils.copyProperties(orderExtendPO, orderExtendVO);
        String mobileLastFour = OrderLocalUtils.subMobileLastFour(orderExtendPO.getReceiverMobile());

        // 筛出物流单为空，以发货时间降序
        productDeliveryVOS = productDeliveryVOS.stream().filter(vo -> vo.getLogisticId() != null)
                .sorted(Comparator.comparing(OrderProductDeliveryVO::getDeliverTime).reversed())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productDeliveryVOS)) {
            return new ArrayList<>();
        }

        // 以物流单维度返回
        List<OrderFrontDeliveryVO> deliveryVOS = new ArrayList<>(productDeliveryVOS.size());
        Map<String, OrderFrontDeliveryVO> deliveryNumber = new HashMap<>();
        for (OrderProductDeliveryVO productDeliveryVO : productDeliveryVOS) {

            OrderFrontDeliveryVO deliveryVO = deliveryNumber.get(productDeliveryVO.getExpressNumber());

            if (Objects.isNull(deliveryVO)) {
                deliveryVO = new OrderFrontDeliveryVO();
                deliveryVO.setOrderExtendVO(orderExtendVO);
                BeanUtils.copyProperties(productDeliveryVO, deliveryVO);
                deliveryVO.setDeliveryState(productDeliveryVO.getDeliveryState());
                List<Long> orderProductIds = new ArrayList<>();
                orderProductIds.add(productDeliveryVO.getOrderProductId());
                deliveryVO.setOrderProductIds(orderProductIds);
                List<String> productImages = new ArrayList<>();
                productImages.add(productDeliveryVO.getProductImage());
                deliveryVO.setProductImages(productImages);
                List<OrderProductBaseVO> orderProductBaseVOS = new ArrayList<>();
                OrderProductBaseVO orderProductBaseVO = new OrderProductBaseVO();
                BeanUtils.copyProperties(productDeliveryVO, orderProductBaseVO);
                orderProductBaseVO.setProductImage(productDeliveryVO.getProductImageSuffix());
                orderProductBaseVOS.add(orderProductBaseVO);
                deliveryVO.setOrderProductBaseVOS(orderProductBaseVOS);
                // 物流发货，查询物流信息
                if (productDeliveryVO.getDeliverType() == 0) {
                    ExpressDTO expressDTO = this.getLogistic(productDeliveryVO, mobileLastFour);
                    deliveryVO.setTracesResult(expressDTO);
                }

                deliveryVOS.add(deliveryVO);
                deliveryNumber.put(productDeliveryVO.getExpressNumber(), deliveryVO);
            } else {
                deliveryVO.getOrderProductIds().add(productDeliveryVO.getOrderProductId());
                deliveryVO.getProductImages().add(productDeliveryVO.getProductImage());
                OrderProductBaseVO orderProductBaseVO = new OrderProductBaseVO();
                BeanUtils.copyProperties(productDeliveryVO, orderProductBaseVO);
                orderProductBaseVO.setProductImage(productDeliveryVO.getProductImageSuffix());
                deliveryVO.getOrderProductBaseVOS().add(orderProductBaseVO);
            }
        }

        return deliveryVOS;
    }

    @Override
    public List<OrderDeliveryInfoVO> batchOrderDeliveryInfo(List<String> orderSnList, Integer memberId) {
        LambdaQueryWrapper<OrderPO> queryOrder = Wrappers.lambdaQuery();
        queryOrder.eq(OrderPO::getMemberId, memberId).in(OrderPO::getOrderSn, orderSnList)
                .eq(OrderPO::getEnabledFlag, CommonEnum.YES.getCode()).select(OrderPO::getOrderSn, OrderPO::getOrderState);
        List<OrderPO> orderPOS = this.list(queryOrder);
        if (CollectionUtils.isEmpty(orderPOS)) {
            return new ArrayList<>();
        }

        List<OrderDeliveryInfoVO> orderDeliveryInfoVOS = new ArrayList<>(orderPOS.size());
        for (OrderPO orderPO : orderPOS) {
            OrderDeliveryInfoVO deliveryInfoVO = new OrderDeliveryInfoVO();
            deliveryInfoVO.setOrderSn(orderPO.getOrderSn());
            deliveryInfoVO.setDeliverTime(orderPO.getDeliverTime());
            deliveryInfoVO.setOrderState(orderPO.getOrderState());
            // 订单物流信息
            deliveryInfoVO.setOrderFrontDeliveryVOList(this.frontDeliveryList(orderPO.getOrderSn(), null));

            orderDeliveryInfoVOS.add(deliveryInfoVO);
        }
        return orderDeliveryInfoVOS;
    }

    private ExpressDTO getLogistic(OrderProductDeliveryVO deliveryVO, String customerName) {
        // 获取配置表内快递鸟的EBusinessID和AppKey
        String eBusinessId = stringRedisTemplate.opsForValue().get("express_ebusinessid");
        AssertUtil.notNull(eBusinessId, "请完善快递鸟配置信息：EBusinessID");
        String appKey = stringRedisTemplate.opsForValue().get("express_apikey");
        AssertUtil.notNull(appKey, "请完善快递鸟配置信息：AppKey");

        ExpressTrackQuery query = new ExpressTrackQuery();
        query.setBizCode(deliveryVO.getOrderSn());
        query.setSourceChannel(LogisticConst.MALL);
        query.setLogisticCode(deliveryVO.getExpressNumber());
        query.setShipperCode(deliveryVO.getExpressCompanyCode());
        query.setCustomerName(customerName);

        return logisticIntegration.getTracks(query);
    }

    private TracesResult getTracesResult(OrderProductDeliveryVO deliveryVO) {
        TracesResult tracesResult = new TracesResult();

        if (deliveryVO.getLogisticId() == null || deliveryVO.getLogisticId() == 0) {
            /**
             * 无物流信息
             */

        } else if (deliveryVO.getDeliverType().equals(OrderConst.DELIVER_TYPE_0)) {
            /**
             * 物流发货
             */
            // 获取配置表内快递鸟的EBusinessID和AppKey
            String EBusinessID = stringRedisTemplate.opsForValue().get("express_ebusinessid");
            AssertUtil.notNull(EBusinessID, "请完善快递鸟配置信息：EBusinessID");
            String AppKey = stringRedisTemplate.opsForValue().get("express_apikey");
            AssertUtil.notNull(AppKey, "请完善快递鸟配置信息：AppKey");

            // 查询订单收货手机号
            OrderExtendPO orderExtend = orderExtendModel.getOrderExtendByOrderSn(deliveryVO.getOrderSn());

            tracesResult = TrackUtil.getKdniaoTrack(deliveryVO.getOrderSn(), deliveryVO.getExpressCompanyCode(),
                    deliveryVO.getExpressName(), deliveryVO.getExpressNumber(), EBusinessID, AppKey,
                    OrderLocalUtils.subMobileLastFour(orderExtend.getReceiverMobile()));

        } else {
            /**
             * 无需物流
             */
            tracesResult.setExpressName(deliveryVO.getDeliverName());
            tracesResult.setExpressNumber(deliveryVO.getDeliverMobile());
            tracesResult.setType("1");
        }

        tracesResult.setGoodsImage(deliveryVO.getProductImage());
        return tracesResult;
    }

    @Override
    public boolean cleanPromotion() {

        LambdaQueryWrapper<OrderPO> orderQuery = new LambdaQueryWrapper();
        orderQuery.select(OrderPO::getOrderId, OrderPO::getActivityDiscountDetail);
        orderQuery.isNotNull(OrderPO::getActivityDiscountDetail);
        List<OrderPO> orderPOList = this.list(orderQuery);

        for (OrderPO orderPO : orderPOList) {
            try {
                List<OrderSubmitDTO.PromotionInfo> promotionInfoList =
                        JSON.parseArray(orderPO.getActivityDiscountDetail(), OrderSubmitDTO.PromotionInfo.class);

                BigDecimal storeVoucherAmount = BigDecimal.ZERO;
                for (OrderSubmitDTO.PromotionInfo promotionInfo : promotionInfoList) {
                    if (promotionInfo.getIsStore()
                            && promotionInfo.getPromotionType().equals(PromotionConst.PROMOTION_TYPE_402)) {
                        storeVoucherAmount = storeVoucherAmount.add(promotionInfo.getDiscount());
                    }
                }

                BigDecimal storeActivityAmount = new BigDecimal("0.00");
                for (OrderSubmitDTO.PromotionInfo promotionInfo : promotionInfoList) {
                    if (promotionInfo.getIsStore()
                            && !promotionInfo.getPromotionType().equals(PromotionConst.PROMOTION_TYPE_402)) {
                        storeActivityAmount = storeActivityAmount.add(promotionInfo.getDiscount());
                    }
                }

                BigDecimal platformActivityAmount = new BigDecimal("0.00");
                for (OrderSubmitDTO.PromotionInfo promotionInfo : promotionInfoList) {
                    if (!promotionInfo.getIsStore()
                            && !promotionInfo.getPromotionType().equals(PromotionConst.PROMOTION_TYPE_402)) {
                        platformActivityAmount = platformActivityAmount.add(promotionInfo.getDiscount());
                    }
                }

                BigDecimal platformVoucherAmount = new BigDecimal("0.00");
                for (OrderSubmitDTO.PromotionInfo promotionInfo : promotionInfoList) {
                    if (!promotionInfo.getIsStore()
                            && promotionInfo.getPromotionType().equals(PromotionConst.PROMOTION_TYPE_402)) {
                        platformVoucherAmount = platformVoucherAmount.add(promotionInfo.getDiscount());
                    }
                }

                OrderPO updateOrder = new OrderPO();
                updateOrder.setOrderId(orderPO.getOrderId());
                updateOrder.setPlatformActivityAmount(platformActivityAmount);
                updateOrder.setPlatformVoucherAmount(platformVoucherAmount);
                updateOrder.setStoreActivityAmount(storeActivityAmount);
                updateOrder.setStoreVoucherAmount(storeVoucherAmount);
                this.updateById(updateOrder);
            } catch (Exception e) {
                log.error("修正订单优惠金额异常", e);
            }

        }
        return false;
    }

    @Override
    public boolean setOrdersDeliverable(List<String> orderSns) {
        if (CollectionUtils.isEmpty(orderSns)) {
            return false;
        }
        LambdaUpdateWrapper<OrderPO> updateWrapper = Wrappers.lambdaUpdate(OrderPO.class);
        updateWrapper.in(OrderPO::getOrderSn, orderSns).eq(OrderPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y)
                .set(OrderPO::getIsDelivery, OrderConst.IS_DELIVER_1);

        this.update(updateWrapper);

        // 发送变更消息
        for (String orderSn : orderSns) {
            OrderPO orderPONew = this.getByOrderSn(orderSn);
            orderCreateHelper.addOrderChangeEvent(orderPONew, OrderEventEnum.SPELL_SUCCESS, new Date());
        }

        return Boolean.TRUE;
    }

    @Override
    public void updateAddress(String orderSn, Integer addressId, Member member) {
        OrderPO orderPO = orderModel.getOrdersWithOpByOrderSn(orderSn);
        BizAssertUtil.isTrue(!orderPO.getMemberId().equals(member.getMemberId()), "您无权修改他人订单");
        BizAssertUtil.isTrue(!orderPO.getOrderState().equals(OrderConst.ORDER_STATE_5)
                && !orderPO.getOrderState().equals(OrderConst.ORDER_STATE_10)
                && !orderPO.getOrderState().equals(OrderConst.ORDER_STATE_20), "该订单地址无法修改");

        MemberAddress memberAddress = memberAddressFeignClient.getMemberAddressByAddressIdV2(addressId);
        BizAssertUtil.notNull(memberAddress, "地址不存在");


        List<OrderProductPO> productPOS =
                orderProductService.lambdaQuery().eq(OrderProductPO::getOrderSn, orderSn).list();
        for (OrderProductPO productPO : productPOS) {
            String areaCode = "";
            FrontStoreRegionRequestVO regionRequestVO = new FrontStoreRegionRequestVO();
            regionRequestVO.setProvince(memberAddress.getProvinceName());
            regionRequestVO.setCity(memberAddress.getCityName());
            regionRequestVO.setCounty(memberAddress.getDistrictName());
            regionRequestVO.setTown(memberAddress.getTownName());
            regionRequestVO.setStoreId(productPO.getStoreId().toString());
            FrontStoreRegionVo accurateAreaByGps = storeRegionFeignClient.getAccurateAreaByGps(regionRequestVO);
            if (Objects.nonNull(accurateAreaByGps)
                    && !org.springframework.util.CollectionUtils.isEmpty(accurateAreaByGps.getRegionVOList())) {
                areaCode = accurateAreaByGps.getRegionVOList().get(0).getCode();
            } else {
                throw new BusinessException("存在商品该地区不可售");
            }
            ProductPriceVO productPriceByProductId =
                    orderLocalUtils.getProductPrice(productPO.getProductId(), areaCode, productPO.getFinanceRuleCode());
            if (!productPriceByProductId.getJudgeFlag()) {
                throw new BusinessException("存在商品该地区不可售");
            }
        }

        // 构造计算运费的dto
        CalculateExpressDTO calculateExpressDTO = new CalculateExpressDTO();
        calculateExpressDTO.setCityCode(memberAddress.getCityCode());
        calculateExpressDTO.setProductList(new ArrayList<>());
        for (OrderProductPO orderProductPO : orderPO.getOrderProductPOList()) {
            calculateExpressDTO.getProductList().add(new ProductInfoDTO(orderProductPO.getGoodsId(),
                    orderProductPO.getProductId(), orderProductPO.getProductShowPrice(), orderProductPO.getProductNum()));
        }
        BigDecimal totalFee = goodsFreightTemplateFeignClient.calculateExpressFee(calculateExpressDTO);
        BizAssertUtil.isTrue(totalFee.compareTo(orderPO.getExpressFee()) != 0, "地址修改后运费发生变化，暂不支持修改");

        // 修改扩展表信息
        OrderExtendPO orderExtendPO = orderExtendModel.getOrderExtendByOrderSnLambda(orderSn);
        orderExtendPO.setReceiverAreaInfo(
                memberAddress.getProvinceName() + memberAddress.getCityName() + memberAddress.getDistrictName());
        orderExtendPO.setReceiverAddress(memberAddress.getDetailAddress());
        orderExtendPO.setReceiverMobile(memberAddress.getTelMobile());
        JsonResult<ReceiveListInfoVo> addressInfo = memberAddressFeignClient.getAddressByAddressId(addressId);
        if (addressInfo.getState() == ResponseConst.STATE_SUCCESS && Objects.nonNull(addressInfo.getData())) {
            ReceiveListInfoVo addressInfoData = addressInfo.getData();
            orderExtendPO.setReceiverName(addressInfoData.getName());
            orderExtendPO.setReceiverProvinceCode(addressInfoData.getProvinceName());
            orderExtendPO.setReceiverCityCode(addressInfoData.getCityName());
            orderExtendPO.setReceiverDistrictCode(addressInfoData.getCountyName());
            orderExtendPO.setReceiverTownCode(addressInfoData.getTownName());
            orderExtendPO.setReceiverInfo(addressInfoData.getProvinceName() + addressInfoData.getCityName()
                    + addressInfoData.getCountyName() + addressInfoData.getTownName() + addressInfoData.getAddr());
        }
        orderExtendModel.updateOrderExtend(orderExtendPO);
        try {
            if (orderProductModel.isContainHsfOrder(orderPO.getOrderSn())) {
                log.info("{} is hsq order", orderPO.getOrderSn());
                BzOrderAddressDTO bzOrderAddressDTO = new BzOrderAddressDTO();
                bzOrderAddressDTO.setOrderSn(orderPO.getOrderSn());
                bzOrderAddressDTO.setReceiverAddress(orderExtendPO.getReceiverAddress());
                bzOrderAddressDTO.setReceiverCity(orderExtendPO.getReceiverCityCode());
                bzOrderAddressDTO.setReceiverName(orderExtendPO.getReceiverName());
                bzOrderAddressDTO.setReceiverMobile(orderExtendPO.getReceiverMobile());
                bzOrderAddressDTO.setReceiverTown(orderExtendPO.getReceiverTownCode());
                bzOrderAddressDTO.setReceiverDistrict(orderExtendPO.getReceiverDistrictCode());
                bzOrderAddressDTO.setReceiverProvince(orderExtendPO.getReceiverProvinceCode());
                JsonResult<Boolean> jsonResult = dtsBzOrderOperateClient.updateAddress(bzOrderAddressDTO);
                log.info("updateAddress jsonResult = {}", JSONObject.toJSONString(jsonResult));
            }

        } catch (Exception e) {
            log.warn("updateAddress 更新Dts-center渠道订单收货信息失败,{}", e.getMessage());
            log.warn("dts-cetener updateAddress fail");
            log.warn("", e);
        }
    }

    @Override
    public void updatePurchaseOrderAddress(PurchaseOrderAddressUpdateRequest address, Member member) {
        OrderPO orderPO = this.getByOrderSn(address.getOrderSn());
        BizAssertUtil.isTrue(Objects.isNull(orderPO), String.format("订单号%s不存在", address.getOrderSn()));
        BizAssertUtil.isTrue(!orderPO.getMemberId().equals(member.getMemberId()), "您无权修改他人订单");
        BizAssertUtil.isTrue(!orderPO.getOrderPattern().equals(OrderPatternEnum.PURCHASE_CENTRE.getValue()),
                "该订单非采购订单");
        BizAssertUtil.isTrue(!orderPO.getOrderState().equals(OrderConst.ORDER_STATE_20)
                && !orderPO.getOrderState().equals(OrderConst.ORDER_STATE_10), "采购订单待支付/代发货状态才允许修改地址");
        BizAssertUtil.isTrue(orderReturnModel.whetherHasReturningProduct(address.getOrderSn()), "退款中采购订单不可修改地址");

        OrderExtendPO orderExtendPO = orderExtendService.getOrderExtendByOrderSn(address.getOrderSn());
        LambdaUpdateWrapper<OrderExtendPO> updateWrapper = Wrappers.lambdaUpdate(OrderExtendPO.class);
        updateWrapper.set(OrderExtendPO::getReceiverName, address.getReceiverName())
                .set(OrderExtendPO::getReceiverMobile, address.getReceiverPhone())
                .set(OrderExtendPO::getReceiverAddress, address.getCompanyAddress())
                .set(OrderExtendPO::getReceiverInfo,
                        orderExtendPO.getReceiverProvinceCode() + orderExtendPO.getReceiverCityCode()
                                + orderExtendPO.getReceiverDistrictCode() + orderExtendPO.getReceiverTownCode()
                                + address.getCompanyAddress())
                .eq(OrderExtendPO::getOrderSn, address.getOrderSn());
        orderExtendService.update(updateWrapper);

    }

    /**
     * @param orderBatchId
     * @return com.cfpamf.common.ms.result.Result<com.cfpamf.mallpayment.facade.request.loan.GetMallProductElementRequest>
     * @description : 构建获取产品要素参数
     */
    @Override
    public Result<GetMallProductElementRequest> buildProductElementRequest(String orderBatchId) {
        OrderPayPO orderPayPO = iOrderPayService
                .getOne(Wrappers.lambdaQuery(OrderPayPO.class).eq(OrderPayPO::getPaySn, orderBatchId).last("limit 1"));
        // 订单列表信息
        List<OrderPO> orderPOS = this.lambdaQuery().eq(OrderPO::getPaySn, orderBatchId).list();
        // 订单扩展信息
        OrderExtendPO orderExtendPO = orderExtendService.getOrderExtendByOrderSn(orderPOS.get(0).getOrderSn());

        GetMallProductElementRequest elementRequest = new GetMallProductElementRequest();
        elementRequest.setOrderBatchId(orderBatchId);
        elementRequest.setLoanCustId(orderExtendPO.getCustomerId());
        elementRequest.setBranchCode(orderExtendPO.getBranch());
        elementRequest.setBranchName(orderExtendPO.getBranchName());
        elementRequest.setAreaCode(orderExtendPO.getAreaCode());
        elementRequest.setAreaName(orderExtendPO.getAreaName());
        elementRequest.setMallChannel(orderPOS.get(0).getChannel());
        elementRequest.setAmount(orderPayPO.getPayAmount());

        List<MallCommodityBriefInfo> commodityList = initCommodityBriefInfoList(orderPOS);

        elementRequest.setCommodityBriefInfos(commodityList);

        return ResultUtils.buildSuccessResult(elementRequest);
    }

    /**
     * @param orderBatchId
     * @return com.cfpamf.common.ms.result.Result<com.cfpamf.mallpayment.facade.request.loan.GetMallProductRateRequest>
     * @description : 构建获取产品利率
     */
    @Override
    public Result<GetMallProductRateRequest> buildMallProductRateRequest(String orderBatchId) {
        OrderPayPO orderPayPO = iOrderPayService
                .getOne(Wrappers.lambdaQuery(OrderPayPO.class).eq(OrderPayPO::getPaySn, orderBatchId).last("limit 1"));

        // 订单列表信息
        List<OrderPO> orderPOS = this.lambdaQuery().eq(OrderPO::getPaySn, orderBatchId).list();
        // 订单扩展信息
        OrderExtendPO orderExtendPO = orderExtendService.getOrderExtendByOrderSn(orderPOS.get(0).getOrderSn());

        GetMallProductRateRequest rateRequest = new GetMallProductRateRequest();
        rateRequest.setOrderBatchId(orderBatchId);
        rateRequest.setLoanCustId(orderExtendPO.getCustomerId());
        rateRequest.setAmount(orderPayPO.getPayAmount());
        rateRequest.setBranchCode(orderExtendPO.getBranch());
        rateRequest.setBranchName(orderExtendPO.getBranchName());
        rateRequest.setAreaCode(orderExtendPO.getAreaCode());
        rateRequest.setAreaName(orderExtendPO.getAreaName());

        List<MallCommodityBriefInfo> commodityList = initCommodityBriefInfoList(orderPOS);
        rateRequest.setCommodityBriefInfos(commodityList);
        return ResultUtils.buildSuccessResult(rateRequest);
    }

    /**
     * @param orderPOS
     * @return java.util.List<com.cfpamf.ms.loan.facade.request.external.mall.MallCommodityBriefInfo>
     * @description : 初始化订单商品信息
     */
    private List<MallCommodityBriefInfo> initCommodityBriefInfoList(List<OrderPO> orderPOS) {
        List<MallCommodityBriefInfo> commodityList = new ArrayList<>();

        for (OrderPO orderPO : orderPOS) {
            // 查询订单商品信息
            List<OrderProductPO> orderProductPOS =
                    orderProductService.lambdaQuery().eq(OrderProductPO::getOrderSn, orderPO.getOrderSn()).list();

            Map<Long, Product> longProductMap = orderLocalUtils.getLongProductMap(orderProductPOS);

            for (OrderProductPO orderProductPO : orderProductPOS) {
                // 商家信息
                MallCommodityBriefInfo mallCommodityBriefInfo = new MallCommodityBriefInfo();
                mallCommodityBriefInfo.setMerchantId(orderPO.getStoreId().toString());
                mallCommodityBriefInfo.setMerchantName(orderPO.getStoreName());
                mallCommodityBriefInfo.setMerchantType(orderPO.getStoreIsSelf().toString());
                mallCommodityBriefInfo.setRecommendStoreId(orderPO.getRecommendStoreId());
                // 商品信息
                List<String> strings = Arrays.asList(orderProductPO.getGoodsCategoryPath().split("->"));
                mallCommodityBriefInfo.setCommodityId(orderProductPO.getGoodsId().toString());
                mallCommodityBriefInfo.setCommodityName(orderProductPO.getGoodsName());
                mallCommodityBriefInfo.setFirstItemCategoryName(strings.size() > 0 ? strings.get(0) : "");
                mallCommodityBriefInfo.setSecondItemCategoryName(strings.size() > 1 ? strings.get(1) : "");
                mallCommodityBriefInfo.setThreeItemCategoryName(strings.size() > 2 ? strings.get(2) : "");
                // 处理类目id
                if (longProductMap.containsKey(orderProductPO.getProductId())) {
                    Product product = longProductMap.get(orderProductPO.getProductId());
                    mallCommodityBriefInfo.setFirstItemCategoryId(product.getCategoryId1().toString());
                    mallCommodityBriefInfo.setSecondItemCategoryId(product.getCategoryId2().toString());
                    mallCommodityBriefInfo.setThreeItemCategoryId(product.getCategoryId3().toString());
                }
                commodityList.add(mallCommodityBriefInfo);
            }
        }
        return commodityList;
    }

    @Deprecated
    @Override
    public Boolean updateCommissionFee4Order(String orderSn) {

        // 查询支付单号下所有待支付的订单
        OrderPO orderPO = this.getByOrderSn(orderSn);

        // 预付订单只支付了定金（订单状态10和15），不会产生分销佣金，不需要去分销系统查询
        if (OrderTypeEnum.PRE_SELL_DEPOSIT.getValue() == orderPO.getOrderType()
                && (OrderStatusEnum.WAIT_PAY.getValue().equals(orderPO.getOrderState())
                || OrderStatusEnum.DEAL_PAY.getValue().equals(orderPO.getOrderState()))) {
            return Boolean.TRUE;
        }
        // OMS的订单,不产生佣金,不需要去分销系统查询
        if (StringUtils.equals(orderPO.getChannel(), OrderCreateChannel.OMS.getValue())) {
            return Boolean.TRUE;
        }
        // 线下补录的订单,不产生佣金,不需要去分销系统查询
        if (OrderTypeEnum.ORDER_TYPE_5.getValue() == orderPO.getOrderType()
                || OrderTypeEnum.ORDER_TYPE_6.getValue() == orderPO.getOrderType()) {
            return Boolean.TRUE;
        }
        // 赠品类订单,不产生佣金,不需要去分销系统查询
        if (OrderTypeEnum.isGiftAll(orderPO.getOrderType())) {
            return Boolean.TRUE;
        }

        if (!Objects.isNull(orderPO.getPayTime())) {
            // 因为分销系统监听支付完成MQ进行处理,在3分钟内不去查询分销,进行冲突避让
            long payTimestamp = orderPO.getPayTime().getTime();
            long nowTimestamp = System.currentTimeMillis();
            if (nowTimestamp - payTimestamp < 1000 * 60) {
                return Boolean.FALSE;
            }
        }

        Result<List<OrderItemCommissionDTO>> result = dbcServiceFeign.getOrderCommissionV2(orderPO.getOrderSn());
        if (result == null || !result.isSuccess()) {
            log.error("查询分销相关佣金接口失败,orderSn:{},", orderSn);
            return Boolean.FALSE;
        }
        if (CollectionUtils.isEmpty(result.getData())) {
            log.warn("分销订单不存在 orderSn:{}", orderSn);
            return Boolean.TRUE;
        }
        BigDecimal orderCommission = BigDecimal.ZERO;
        for (OrderItemCommissionDTO datum : result.getData()) {
            orderCommission = orderCommission.add(datum.getOrderOrdinaryCommission());

            LambdaUpdateWrapper<OrderProductPO> orderProductUpdater = new LambdaUpdateWrapper<>();
            orderProductUpdater.set(OrderProductPO::getOrderCommission, datum.getOrderOrdinaryCommission());
            orderProductUpdater.set(OrderProductPO::getCommissionAmount, datum.getOrderOrdinaryCommission());

            orderProductUpdater.eq(OrderProductPO::getOrderSn, orderPO.getOrderSn());
            orderProductUpdater.eq(OrderProductPO::getProductId, datum.getProductId());
            orderProductService.update(orderProductUpdater);
        }
        LambdaUpdateWrapper<OrderPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(OrderPO::getOrderCommission, orderCommission);
        updateWrapper.set(OrderPO::getSettlementPrice, orderPO.getSettlementPrice().subtract(orderCommission));

        updateWrapper.eq(OrderPO::getOrderId, orderPO.getOrderId());
        return this.update(updateWrapper);
    }

    @Override
    @Transactional
    public Boolean updateCommissionFeeV3(String orderSn) {
        // 查询支付单号下所有待支付的订单
        OrderPO orderPO = this.getByOrderSn(orderSn);
        log.info("[updateCommissionFeeV3]orderSn:{} old_orderCommission:{}", orderSn, orderPO.getOrderCommission());
        BizAssertUtil.notNull(orderPO, "订单分销资金更新订单未找到，异常！");
        Result<List<OrderItemCommissionDTO>> result = dbcServiceFeign.getOrderOffsetCommission(orderPO.getOrderSn());
        BizAssertUtil.isTrue(result == null || !result.isSuccess() || CollectionUtils.isEmpty(result.getData()), "查询分销相关佣金接口失败，异常！");
        log.info("[updateCommissionFeeV3]orderSn:{} result:{}", orderSn, result);
        BigDecimal orderCommission = BigDecimal.ZERO;
        for (OrderItemCommissionDTO datum : result.getData()) {
            orderCommission = orderCommission.add(datum.getOrderOrdinaryCommission());

            LambdaUpdateWrapper<OrderProductPO> orderProductUpdater = new LambdaUpdateWrapper<>();
            orderProductUpdater.set(OrderProductPO::getOrderCommission, datum.getOrderOrdinaryCommission());
            orderProductUpdater.set(OrderProductPO::getCommissionAmount, datum.getOrderOrdinaryCommission());

            orderProductUpdater.eq(OrderProductPO::getOrderSn, orderPO.getOrderSn());
            orderProductUpdater.eq(OrderProductPO::getProductId, datum.getProductId());
            boolean orderProductUpdaterState = orderProductService.update(orderProductUpdater);
            BizAssertUtil.isTrue(!orderProductUpdaterState, "订单分销资金更新订单商品异常！");
        }
        LambdaUpdateWrapper<OrderPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(OrderPO::getOrderCommission, orderCommission);
        updateWrapper.set(OrderPO::getSettlementPrice, orderPO.getSettlementPrice().subtract(orderCommission));

        updateWrapper.eq(OrderPO::getOrderId, orderPO.getOrderId());

        // andy增加代码
        OrderAmountStateRecordPO orderAmountStateRecord = new OrderAmountStateRecordPO();
        orderAmountStateRecord.setAmount(orderCommission);
        orderAmountStateRecord.setAmountType(OrderAmountTypeEnum.ORDER_COMMISSION.getName());
        orderAmountStateRecord.setOrderSn(orderSn);
        orderAmountStateRecord.setCreateBy("commission_update");
        orderAmountStateRecord.setUpdateTime(new Date());
        boolean statusOrderAmountState =
                orderAmountStateRecordService.updateOrderAmountStateRecord(orderAmountStateRecord);
        BizAssertUtil.isTrue(!statusOrderAmountState, "订单分销资金更新异常！");
        log.info("[updateCommissionFeeV3]orderSn:{} old_orderCommission:{} new_orderCommission:{}", orderSn, orderPO.getOrderCommission(), orderCommission);
        return this.update(updateWrapper);
    }

    @Override
    public void dealZeroEnjoyPayOrderFail(String paySn) {
        LambdaQueryWrapper<OrderPO> orderQuery = new LambdaQueryWrapper<>();
        orderQuery.eq(OrderPO::getPaySn, paySn);
        List<OrderPO> orderPOList = this.list(orderQuery);

        for (OrderPO orderPO : orderPOList) {
            OrderStatusEnum orderStatus = OrderStatusEnum.valueOf(orderPO.getOrderState());
            if (OrderBizUtils.isZeroOrder(orderPO) && (OrderStatusEnum.WAIT_PAY_DEPOSIT == orderStatus
                    || OrderStatusEnum.WAIT_PAY == orderStatus || OrderStatusEnum.DEAL_PAY == orderStatus)) {
                // 1.关闭订单
                orderReturnService.orderCancelWithoutRefund(orderPO, "用呗订单支付失败", "用呗订单支付失败-系统取消",
                        OrderConst.LOG_USER_ID, OrderConst.LOG_USER_NAME);

                // 2.记录订单日志
                orderLogModel.insertOrderLog(OrderConst.LOG_ROLE, OrderConst.LOG_USER_ID, OrderConst.LOG_USER_NAME,
                        orderPO.getOrderSn(), orderPO.getOrderState(), OrderConst.ORDER_STATE_0,
                        LoanStatusEnum.APPLY_FAIL.getValue(), "用呗订单支付失败-系统取消", OrderCreateChannel.WEB);
            }

        }
    }

    @Override
    public void dealZeroEnjoyPayOrder(String paySn, String payNo) {

        LambdaQueryWrapper<OrderPO> orderQuery = new LambdaQueryWrapper<>();
        orderQuery.eq(OrderPO::getPaySn, paySn);
        List<OrderPO> orderPOList = this.list(orderQuery);

        // 待处理0元订单
        List<OrderPO> zeroOrderList = new ArrayList<>(orderPOList.size());

        for (OrderPO orderPO : orderPOList) {
            if (OrderBizUtils.isZeroOrder(orderPO)) {
                // 0元订单
                zeroOrderList.add(orderPO);
            } else if (!OrderStatusEnum.isPaid(orderPO.getOrderState())) {
                // 存在未支付的非0元订单, 直接返回
                return;
            }
        }

        if (CollectionUtils.isEmpty(zeroOrderList)) {
            return;
        }

        // 逐个处理0元订单
        for (OrderPO orderPO : zeroOrderList) {
            orderPayModel.orderPaySuccess(orderPO, null, null, null, new Date(), payNo);
        }
    }

    /**
     * @param dto
     * @param cardCodeList
     * @param userNo
     * @return com.cfpamf.ms.mallorder.vo.OrderSubmitPageVO
     * @description :计算乡助卡金额 订单维度分摊计算： 1.(子单商品金额+运费)/(总单商品金额+运费)得出 包含运费情况下 子单分配的总金额 2.子单商品金额/(子单商品金额+运费) 得出
     *              子单商品金额占比，子单商品金额*子单商品金额占比 得出 子单商品优惠金额 3.子单分配的总金额-子单商品优惠金额 得出 子单运费优惠总金额 商品行分摊计算： 1.商品行金额/子单商品金额
     */
    @Override
    public OrderSubmitDTO calculateCard(OrderSubmitDTO dto, List<String> cardCodeList, String userNo) {
        if (CollectionUtils.isEmpty(cardCodeList)
            || dto.getTotalAmount().add(dto.getExpressFee()).compareTo(BigDecimal.ZERO) == 0) {
            dto.setXzCardAmount(BigDecimal.ZERO);
            dto.setXzCardExpressFeeAmount(BigDecimal.ZERO);
            dto.setTotalAmount(dto.getTotalAmount());
            for (OrderSubmitDTO.OrderInfo orderInfo : dto.getOrderInfoList()) {
                orderInfo.setXzCardExpressFeeAmount(BigDecimal.ZERO);
                orderInfo.setXzCardAmount(BigDecimal.ZERO);
                orderInfo.setTotalAmount(orderInfo.getTotalAmount());
                for (OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo : orderInfo.getOrderProductInfoList()) {
                    orderProductInfo.setXzCardAmount(BigDecimal.ZERO);
                    orderProductInfo.setMoneyAmount(orderProductInfo.getMoneyAmount());
                }
            }
            return dto;
        }
        // 按金额排序
        for (OrderSubmitDTO.OrderInfo orderInfo : dto.getOrderInfoList()) {
            List<OrderSubmitDTO.OrderInfo.OrderProductInfo> productInfos = orderInfo.getOrderProductInfoList().stream()
                .sorted(Comparator.comparing(OrderSubmitDTO.OrderInfo.OrderProductInfo::getMoneyAmount))
                .collect(Collectors.toList());
            orderInfo.setOrderProductInfoList(productInfos);
        }
        List<OrderSubmitDTO.OrderInfo> sortedOrderInfo = dto.getOrderInfoList().stream()
            .sorted(Comparator.comparing(OrderSubmitDTO.OrderInfo::getTotalAmount).reversed())
            .collect(Collectors.toList());
        dto.setOrderInfoList(sortedOrderInfo);
        // 构建试算请求
        CardTryPayRequest payRequest = new CardTryPayRequest();
        payRequest.setCardNoList(cardCodeList);
        payRequest.setOrderAmount(dto.getTotalAmount().add(dto.getExpressFee()));
        // 卡片试算
        CardTryPayResponse cardTryPayResponse = cardPayIntegration.tryPay(payRequest, userNo);

        dto.setXzCardList(cardCodeList);

        /**
         * 1.取总单数据、用于计算店铺间金额占比
         */
        // 原始总可分配乡助卡金额
        BigDecimal totalCanDealCardAmount = cardTryPayResponse.getCardPayAmount();
        // 原始未分配总支付金额+总运费
        BigDecimal oldExpressTotalAmount = dto.getTotalAmount().add(dto.getExpressFee());

        /**
         * 2.总支付金额+运费 等于 乡助卡抵扣金额，处理特殊情况
         */
        if (dealCardAmountZero(dto, totalCanDealCardAmount, oldExpressTotalAmount)) {
            return dto;
        }

        // 总已分配金额
        BigDecimal alreadyDealTotalCardAmount = BigDecimal.ZERO;

        BigDecimal totalCardExpressFeeAmount = BigDecimal.ZERO;
        BigDecimal totalExpressFee = BigDecimal.ZERO;
        BigDecimal totalCardAmount = BigDecimal.ZERO;

        /**
         * 4.分配店铺乡助卡金额
         */
        for (int i = 0, s = dto.getOrderInfoList().size(); i < s; i++) {
            OrderSubmitDTO.OrderInfo orderInfo = dto.getOrderInfoList().get(i);
            /**
             * 取店铺数据、用于计算店铺内商品行金额占比，此处取是因为之后取的店铺数据为已分摊
             */
            // 原始店铺商品金额
            BigDecimal storeOldTotalAmount = orderInfo.getTotalAmount();
            // 原始未分配店铺支付金额+总运费
            BigDecimal oldExpressStoreAmount = orderInfo.getTotalAmount().add(orderInfo.getExpressFee());
            if (oldExpressStoreAmount.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            /**
             * 4.1 计算店铺分摊金额 订单可分配支付金额优惠计算公式：（订单金额/订单金额+订单运费）* 订单金额 订单可分配运费优惠计算公式：订单乡助卡可分配金额-订单可分配支付金额优惠
             */
            // 只有一个店铺
            if (s == 1) {
                // 订单商品金额占比
                BigDecimal divide = orderInfo.getTotalAmount().divide(oldExpressStoreAmount, 6, BigDecimal.ROUND_UP);
                // 订单商品金额可分配到的乡助卡金额（总乡助卡金额*订单商品金额占比）
                BigDecimal orderCanDealCardAmount =
                    totalCanDealCardAmount.multiply(divide).setScale(2, BigDecimal.ROUND_HALF_UP);
                // 仍需支付商品金额= 订单商品金额- 订单商品金额可分配到的乡助卡金额
                // orderInfo.setTotalAmount(orderInfo.getTotalAmount().subtract(orderCanDealCardAmount));
                // 订单可分配运费优惠计算公式：原始总可分配乡助卡金额-订单商品可分配支付金额优惠
                orderInfo.setXzCardExpressFeeAmount(totalCanDealCardAmount.subtract(orderCanDealCardAmount));
                // 订单仍需支付运费 = 订单运费-订单运费可分配乡助卡金额
                orderInfo.setExpressFee(orderInfo.getExpressFee().subtract(orderInfo.getXzCardExpressFeeAmount()));
                // 订单乡助卡优惠总额 = 原始总可分配乡助卡金额
                orderInfo.setXzCardAmount(totalCanDealCardAmount);
            } else if (i == s - 1) {
                // 最后一个店铺 钆差
                // 店铺可分配总金额（总可分配金额-总已分配金额）
                BigDecimal lastStoreCanDidAmount = totalCanDealCardAmount.subtract(alreadyDealTotalCardAmount);

                // 订单商品金额占比
                BigDecimal orderCanDid =
                    orderInfo.getTotalAmount().divide(oldExpressStoreAmount, 6, BigDecimal.ROUND_UP);
                // 订单商品可分配支付金额优惠（店铺可分配总金额*商品金额占比）
                BigDecimal orderCanDidAmount =
                    lastStoreCanDidAmount.multiply(orderCanDid).setScale(2, BigDecimal.ROUND_HALF_UP);

                // 仍需支付商品金额= 订单商品金额- 订单商品金额可分配到的乡助卡金额
                // orderInfo.setTotalAmount(orderInfo.getTotalAmount().subtract(orderCanDidAmount));
                // 订单可分配运费优惠计算公式：店铺可分配总金额-商品可分配支付金额优惠
                orderInfo.setXzCardExpressFeeAmount(lastStoreCanDidAmount.subtract(orderCanDidAmount));
                // 订单仍需支付运费 = 订单运费-订单运费可分配乡助卡金额
                orderInfo.setExpressFee(orderInfo.getExpressFee().subtract(orderInfo.getXzCardExpressFeeAmount()));
                // 订单乡助卡优惠额，（包含优惠的运费）
                orderInfo.setXzCardAmount(lastStoreCanDidAmount);
            } else {
                // 除去s和s-1个店铺之外的店铺计算
                // 店铺此次可分配金额占比 (原始未分配店铺支付金额+总运费)/原始未分配总支付金额+总运费
                BigDecimal divide = oldExpressStoreAmount.divide(oldExpressTotalAmount, 6, BigDecimal.ROUND_UP);
                // 店铺此次可分配金额 = 原始总可分配乡助卡金额 * 店铺此次可分配金额占比
                BigDecimal storeCanTotalDid =
                    totalCanDealCardAmount.multiply(divide).setScale(2, BigDecimal.ROUND_HALF_UP);

                // 订单商品金额占比
                BigDecimal orderCanDid =
                    orderInfo.getTotalAmount().divide(oldExpressStoreAmount, 6, BigDecimal.ROUND_UP);
                // 商品可分配支付金额优惠（店铺此次可分配金额 * 商品金额占比）
                BigDecimal orderCanDidAmount =
                    storeCanTotalDid.multiply(orderCanDid).setScale(2, BigDecimal.ROUND_HALF_UP);

                // 仍需支付商品金额= 订单商品金额- 订单商品金额可分配到的乡助卡金额
                // orderInfo.setTotalAmount(orderInfo.getTotalAmount().subtract(orderCanDidAmount));
                // 订单可分配运费优惠计算公式：店铺可分配总金额-商品可分配支付金额优惠
                orderInfo.setXzCardExpressFeeAmount(storeCanTotalDid.subtract(orderCanDidAmount));
                // 订单仍需支付运费 = 订单运费-订单运费可分配乡助卡金额
                orderInfo.setExpressFee(orderInfo.getExpressFee().subtract(orderInfo.getXzCardExpressFeeAmount()));
                // 订单乡助卡优惠额，（包含优惠的运费）
                orderInfo.setXzCardAmount(storeCanTotalDid);
                // 累加
                alreadyDealTotalCardAmount = alreadyDealTotalCardAmount.add(storeCanTotalDid);
            }
            /**
             * 4.2 取店铺内乡助卡可分配金额
             */
            // 此处为已计算乡助卡金额,运费优惠不参与商品行分摊计算
            BigDecimal storeCanDealCardAmount =
                orderInfo.getXzCardAmount().subtract(orderInfo.getXzCardExpressFeeAmount());
            // 店铺内商品行已分配乡助卡金额
            BigDecimal alreadyDealStoreCardAmount = BigDecimal.ZERO;
            /**
             * 4.3 计算店铺内商品行乡助卡平摊金额
             */
            for (int j = 0, t = orderInfo.getOrderProductInfoList().size(); j < t; j++) {
                OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo = orderInfo.getOrderProductInfoList().get(j);
                // 只有一个商品行
                if (t == 1) {
                    // 店铺乡助卡金额
                    orderProductInfo.setXzCardAmount(storeCanDealCardAmount);
                    // 店铺-乡助卡之后的金额
                    // orderProductInfo.setMoneyAmount(orderInfo.getTotalAmount());
                    // 最后一个商品行 钆差
                } else if (j == t - 1) {
                    // 店铺乡助卡总额-店铺已分配乡助卡
                    orderProductInfo.setXzCardAmount(storeCanDealCardAmount.subtract(alreadyDealStoreCardAmount));
                    // 商品行-商品行乡助卡
                    // orderProductInfo.setMoneyAmount(orderProductInfo.getMoneyAmount());
                    // 除去s和s-1个商品行之外的乡助卡计算
                } else {
                    BigDecimal xzCardAmount = BigDecimal.ZERO;
                    // 店铺内支付金额大于0判断，防止除0异常
                    if (storeOldTotalAmount.compareTo(BigDecimal.ZERO) > 0) {
                        // 占比计算：商品行原始未分配总额 ÷ 原始未分配店铺金额
                        BigDecimal scaleAmount =
                            orderProductInfo.getMoneyAmount().divide(storeOldTotalAmount, 6, BigDecimal.ROUND_UP);
                        // 使用占比乘乡助卡金额得出此次乡助卡分配金额
                        xzCardAmount =
                            scaleAmount.multiply(storeCanDealCardAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                        orderProductInfo.setXzCardAmount(xzCardAmount);
                    }
                    // 商品行金额-商品行乡助卡金额
                    // orderProductInfo.setMoneyAmount(orderProductInfo.getMoneyAmount());
                    // 累加
                    alreadyDealStoreCardAmount = alreadyDealStoreCardAmount.add(xzCardAmount);
                }
            }
            totalCardExpressFeeAmount = totalCardExpressFeeAmount.add(orderInfo.getXzCardExpressFeeAmount());
            totalExpressFee = totalExpressFee.add(orderInfo.getExpressFee());
            totalCardAmount = totalCardAmount.add(orderInfo.getXzCardAmount());
        }
        // dto.setTotalAmount(totalAmount);
        dto.setXzCardExpressFeeAmount(totalCardExpressFeeAmount);
        dto.setExpressFee(totalExpressFee);
        dto.setXzCardAmount(totalCardAmount);
        log.info("计算后乡助卡信息:{}", JSON.toJSONString(dto));
        return dto;
    }

    /***
     * 全额乡助卡处理，运费和金额分摊到子单
     *
     * @param dto
     * @param totalCanDealCardAmount
     * @param oldExpressTotalAmount
     * @return boolean
     * @description :总支付金额+运费等于乡助卡抵扣金额，则分摊金额=待支付金额 处理特殊情况
     */
    private boolean dealCardAmountZero(OrderSubmitDTO dto, BigDecimal totalCanDealCardAmount,
        BigDecimal oldExpressTotalAmount) {
        if (totalCanDealCardAmount.compareTo(oldExpressTotalAmount) != 0) {
            return false;
        }
        // 优惠的运费
        dto.setXzCardExpressFeeAmount(dto.getExpressFee());
        // 总乡助卡金额（包含优惠的运费）
        dto.setXzCardAmount(totalCanDealCardAmount);
        dto.setExpressFee(BigDecimal.ZERO);

        for (OrderSubmitDTO.OrderInfo orderInfo : dto.getOrderInfoList()) {

            orderInfo.setXzCardExpressFeeAmount(orderInfo.getExpressFee());
            // 订单乡助卡优惠总额，（包含优惠的运费）
            orderInfo.setXzCardAmount(orderInfo.getTotalAmount().add(orderInfo.getXzCardExpressFeeAmount()));
            orderInfo.setExpressFee(BigDecimal.ZERO);

            for (OrderSubmitDTO.OrderInfo.OrderProductInfo orderProductInfo : orderInfo.getOrderProductInfoList()) {
                orderProductInfo.setXzCardAmount(orderProductInfo.getMoneyAmount());
            }
        }
        return true;
    }

    /**
     * @param orderSubmitDTO
     * @param expressFeeList
     * @return void
     * @description : 处理运费
     */
    @Override
    public void dealExpress(OrderSubmitDTO orderSubmitDTO, List<BigDecimal> expressFeeList) {
        // 总运费
        orderSubmitDTO.setExpressFee(expressFeeList.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
        // 子单运费
        for (int i = 0; i < orderSubmitDTO.getOrderInfoList().size(); i++) {
            orderSubmitDTO.getOrderInfoList().get(i).setExpressFee(expressFeeList.get(i));
            orderSubmitDTO.getOrderInfoList().get(i).setExpressFeeTotal(expressFeeList.get(i));
        }
    }

    @Override
    public List<OrderPO> listByPaySn(String paySn) {
        LambdaQueryWrapper<OrderPO> queryWrapper = Wrappers.lambdaQuery(OrderPO.class);
        queryWrapper.eq(OrderPO::getPaySn, paySn);
        return this.list(queryWrapper);
    }

    @Override
    @Transactional
    public void extendOrderAutoReceiveTime(String orderSn) {
        OrderPO orderPO = orderModel.getOrderByOrderSnLambda(orderSn);
        BizAssertUtil.notNull(orderPO, MessageFormat.format("订单【{0}】不存在", orderSn));
        BizAssertUtil.isTrue(orderPO.getOrderState() != OrderConst.ORDER_STATE_30,
                MessageFormat.format("订单【{0}】为非待收货的订单，不可延长发货", orderSn));
        BizAssertUtil.isTrue(orderPO.getDelayTimes() >= CommonConst.MAX_EXTEND_RECEIVE_TIMES,
                MessageFormat.format("订单【{0}】延长收货次数已达【{1}】次", orderSn, CommonConst.MAX_EXTEND_RECEIVE_TIMES));

        LambdaUpdateWrapper<OrderPO> updateWrapper = Wrappers.lambdaUpdate(OrderPO.class);
        updateWrapper.eq(OrderPO::getOrderSn, orderSn).set(OrderPO::getDelayTimes, orderPO.getDelayTimes() + 1)
                .set(OrderPO::getDelayDays,
                        Optional.ofNullable(orderPO.getDelayDays()).orElse(0)
                                + systemSettingObtainHelper.getTimeLimitOfAutoReceive())
                .set(OrderPO::getAutoReceiveDay,
                        Optional.ofNullable(orderPO.getAutoReceiveDay())
                                .orElse(systemSettingObtainHelper.getTimeLimitOfAutoReceive())
                                + systemSettingObtainHelper.getTimeLimitOfAutoReceive())
                .set(OrderPO::getUpdateTime, new Date());
        if (!OrderConst.STORE_TYPE_SELF_1.equals(orderPO.getStoreIsSelf()) && PayMethodEnum.isLoanPay(orderPO.getPaymentCode())) {
            log.info("非自营且是贷款类支付，设置自动收货天数,orderSn:{}", orderPO.getOrderSn());
            // 非自营且贷款类支付
            List<DictionaryItemVO> dictionary = bmsIntegration.listDictionaryItemsByTypeCode(CommonConst.AUTO_RECEIVE_DAYS, CommonConst.MALL_SYSTEM_MANAGE_ID);
            if (!CollectionUtils.isEmpty(dictionary)) {
                DictionaryItemVO dictionaryItemVO = dictionary.get(0);
                Integer autoReceiveDays = Integer.valueOf(dictionaryItemVO.getItemCode());
                log.info("非自营且是贷款类支付，设置自动收货天数,days:{}", autoReceiveDays);
                updateWrapper.set(OrderPO::getAutoReceiveDay, autoReceiveDays);
            } else {
                log.info("非自营且是贷款类支付，设置自动收货天数失败，bms未配置字典");
            }
        }
        orderMapper.update(null, updateWrapper);

        // 发送通知消息
        orderCreateHelper.addOrderChangeEvent(orderPO, OrderEventEnum.EXTEND_RECEIVE_TIME, new Date());
    }

    @Override
    public OrderPO getByOrderSn(String orderSn) {
        if (StringUtils.isEmpty(orderSn)) {
            return null;
        }
        LambdaQueryWrapper<OrderPO> orderQuery = new LambdaQueryWrapper<>();
        orderQuery.eq(OrderPO::getOrderSn, orderSn);
        orderQuery.eq(OrderPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y);
        return this.getOne(orderQuery);
    }

    @Override
    public BigDecimal getStoreValidOrderAmountSum(String storeId) {
        return orderMapper.getStoreValidOrderAmountSum(storeId);
    }

    /**
     * 该单号通过订单查询已完结发货，或者在全域内被重复使用到不同的订单上≥3次
     *
     * @param expressNumber 快递单号
     * @param vendor        商户
     * @return
     */
    @Override
    public boolean validExpressNumberReuseCount(String expressNumber, Vendor vendor) {
        // 该单号通过订单查询已完结发货次数
        try {
            vendor = Optional.ofNullable(vendor).orElse(new Vendor());
            Store store = Optional.ofNullable(vendor.getStore()).orElse(new Store());
            String storeTypeName = store.getStoreType() == null ? "" : (store.getStoreType() == 1 ? "自营店铺" : "入驻店铺");

            List<String> orderList;
            orderList = orderLogisticMapper.expressNumberIsUsed(expressNumber);
            if (orderList.size() > 0) {
                if (vendor.getVendorId() == null) {
                    log.error("【该物流单号已重复使用，涉嫌虚假物流，是否继续提交】");
                } else {
                    log.error("【标准电商】发现物流信息异常，{}【{}-{}】填写虚假物流信息 ≥ 1次，核查订单【{}】【提醒】", storeTypeName, vendor.getVendorId(),
                            store.getStoreName(), orderList);
                }
                return true;
            }
            orderList = orderLogisticMapper.expressNumberUseCount(expressNumber);
            orderList = orderList.stream().distinct().collect(Collectors.toList());
            if (orderList.size() >= 3) {
                if (vendor.getVendorId() == null) {
                    log.error("【该物流单号已重复使用，涉嫌虚假物流，是否继续提交】");
                } else {
                    log.error("【标准电商】发现物流信息异常，{}【{}-{}】上传重复物流信息 ≥ 3次，核查订单【{}】【提醒】", storeTypeName, vendor.getVendorId(),
                            store.getStoreName(), orderList);
                }
                return true;
            }
        } catch (Exception e) {
            log.error("OrderServiceImpl.validExpressNumberReuseCount(),校验订单号是否被使用方法异常", e);
        }
        return false;
    }

    /**
     * 该单号通过订单查询已完结发货，或者在全域内被重复使用到不同的订单上≥3次
     *
     * @param expressNumber 快递单号
     * @return
     */
    @Override
    public boolean validExpressNumberReuseCount(String expressNumber) {
        try {
            List<String> orderList;
            orderList = orderLogisticMapper.expressNumberIsUsed(expressNumber);
            if (orderList.size() > 0) {
                return true;
            }
            orderList = orderLogisticMapper.expressNumberUseCount(expressNumber);
            orderList = orderList.stream().distinct().collect(Collectors.toList());
            if (orderList.size() >= 3) {
                return true;
            }
        } catch (Exception e) {
            log.error("OrderServiceImpl.validExpressNumberReuseCount(),校验订单号是否被使用方法异常", e);
        }
        return false;
    }

    /**
     * 根据userNo获取待安装的订单数
     */
    @Override
    public Integer getInstallOrderCountByUserNo(String userNo) {
        if (StringUtils.isEmpty(userNo)) {
            return 0;
        }
        return orderMapper.getInstallOrderCountByUserNo(userNo);
    }

    @Override
    public String homeServiceHistoryDataDeal() {
        Date startTime = DateUtil.parse(homeServiceStartTimeStr, DateUtil.FORMAT_TIME);
        LambdaQueryWrapper<OrderProductPO> queryProductWrapper = Wrappers.lambdaQuery(OrderProductPO.class);
        queryProductWrapper.eq(OrderProductPO::getPerformanceMode, 2);

        List<OrderProductPO> orderProductPOList = orderProductService.list(queryProductWrapper);

        Map<String, List<OrderProductPO>> orderProductMap =
                orderProductPOList.stream().collect(Collectors.groupingBy(OrderProductPO::getOrderSn));
        List<String> orderSnList = orderProductPOList.stream().map(x -> x.getOrderSn()).collect(Collectors.toList());

        LambdaQueryWrapper<OrderPO> queryWrapper = Wrappers.lambdaQuery(OrderPO.class);
        queryWrapper.in(OrderPO::getOrderSn, orderSnList);
        queryWrapper.ge(OrderPO::getCreateTime, startTime);
        List<OrderPO> list = this.list(queryWrapper);

        List<OrderPO> orderPOList = new ArrayList<>();

        list.stream().forEach(x -> {
            List<Integer> performanceList = JSONObject.parseArray(x.getPerformanceModes(), Integer.class);
            if (!performanceList.contains(2)) {
                performanceList.add(2);
                // 更新order
                LambdaUpdateWrapper<OrderPO> updateWrapper = Wrappers.lambdaUpdate(OrderPO.class);
                updateWrapper.set(OrderPO::getPerformanceModes, list.toString()).eq(OrderPO::getOrderSn,
                        x.getOrderSn());
            }
            if (x.getOrderState().equals(OrderStatusEnum.WAIT_DELIVER.getValue())
                    || x.getOrderState().equals(OrderStatusEnum.PART_DELIVERED.getValue())
                    || x.getOrderState().equals(OrderStatusEnum.WAIT_RECEIPT.getValue())) {
                orderPOList.add(x);
            }
        });

        int pages = orderPOList.size() % homeServiceBatchCount == 0 ? orderPOList.size() / homeServiceBatchCount
                : orderPOList.size() / homeServiceBatchCount + 1;
        for (int i = 0; i < pages; i++) {

            int start = i * homeServiceBatchCount;
            int end = (i + 1) * homeServiceBatchCount >= orderPOList.size() ? orderPOList.size()
                    : (i + 1) * homeServiceBatchCount;
            List<OrderPO> pageList = orderPOList.subList(start, end);
            List<Order> resultList = new ArrayList<>();
            pageList.stream().forEach(x -> {
                Order order = new Order();
                BeanUtils.copyProperties(x, order);
                // 获取商品信息
                List<OrderProductPO> productPOList = orderProductMap.get(x.getOrderSn());
                List<OrderProduct> productList = new ArrayList<>();
                productPOList.stream().forEach(y -> {
                    OrderProduct orderProduct = new OrderProduct();
                    BeanUtils.copyProperties(y, orderProduct);
                    productList.add(orderProduct);
                });
                order.setOrderProductList(productList);
                // 获取扩展信息
                OrderExtendPO orderExtendPO = orderExtendModel.getOrderExtendByOrderSn(x.getOrderSn());
                OrderExtend orderExtend = new OrderExtend();
                BeanUtils.copyProperties(orderExtendPO, orderExtend);
                order.setOrderExtend(orderExtend);

                resultList.add(order);
            });
            log.info("homeServiceHistoryDataDeal param = {}", JSONObject.toJSONString(resultList));
            JsonResult<List<String>> result = homeServiceFeignClient.createHomeServiceOrderBatch(resultList);
            log.info("homeServiceHistoryDataDeal result = {}", JSONObject.toJSONString(result));
        }

        return "success";
    }

    @Override
    @Transactional
    public boolean setCustomerUnConfirmStatus(String orderSn) {
        if (Objects.isNull(orderSn)) {
            return false;
        }
        OrderPO orderPO = new OrderPO();
        orderPO.setCustomerConfirmStatus(CustomerConfirmStatusEnum.UNCONFIRMED.getValue());
        orderPO.setCustomerConfirmStatusDesc(CustomerConfirmStatusEnum.UNCONFIRMED.getDesc());
        LambdaUpdateWrapper<OrderPO> updateWrapper = Wrappers.lambdaUpdate(OrderPO.class);
        updateWrapper.eq(OrderPO::getOrderSn, orderSn);
        // 代客下单新增预付订单下单,增加预付定金订单状态
        List<Integer> orderState = Lists.newArrayList();
        orderState.add(OrderStatusEnum.WAIT_PAY.getValue());
        orderState.add(OrderStatusEnum.WAIT_PAY_DEPOSIT.getValue());
        updateWrapper.in(OrderPO::getOrderState, orderState);
        updateWrapper.eq(OrderPO::getCustomerConfirmStatus, CustomerConfirmStatusEnum.DRAFT.getValue());
        boolean status = super.update(orderPO, updateWrapper);
        if (Boolean.TRUE.equals(status)) {
            log.info(
                    "【setCustomerUnConfirmStatus】////////////////////待客户确认，成功开始执行后续流程////////////////////////orderSn:{}",
                    orderSn);
            OrderPO order = orderService.getByOrderSn(orderSn);
            Member member = UserUtil.getUser(request, Member.class);
            Integer logRole = OrderConst.LOG_ROLE_MEMBER;
            Long logUserId = Long.valueOf(member.getMemberId());
            String logUserName = member.getMemberName();
            BizUserInfoDTO bizUserInfoDTO = OrderBuilder.getUser(request, BizUserInfoDTO.class);// BAPP 操作
            Member stationMaster = OrderBuilder.getStationMasterUser(request);// 站长 操作
            if (Objects.nonNull(bizUserInfoDTO)) {
                logRole = OrderConst.LOG_ROLE_CUSTOMER_MANAGER;
                logUserId = Long.valueOf(bizUserInfoDTO.getEmployeeId());
                logUserName = bizUserInfoDTO.getUserName();
            } else if (Objects.nonNull(stationMaster)) {
                logRole = OrderConst.LOG_ROLE_STATIONMASTER;
                logUserId = Long.valueOf(stationMaster.getMemberId());
                logUserName = stationMaster.getMemberName();
            }
            // -bz_order_log 记录订单日志
            orderLogModel.insertOrderLog(logRole, logUserId, logUserName, orderSn, order.getOrderState(),
                    order.getOrderState(), LoanStatusEnum.DEFAULT.getValue(), "提交到客户确认", OrderCreateChannel.APP);
            log.info("【setCustomerUnConfirmStatus】////////////////////待客户确认，发送MQ消息////////////////////////orderSn:{}",
                    orderSn);
            OrderOperationEventEnum customerUnConfirm = OrderOperationEventEnum.CUSTOMER_UN_CONFIRMED;
            OrderOperationEventNotifyDTO message =
                    new OrderOperationEventNotifyDTO(customerUnConfirm.getCode(), customerUnConfirm.getDesc(), orderSn);
            orderCreateHelper.sendMqEventMessageByTransactionCommit(message,
                    RabbitMqEventEnum.EXCHANGE_ORDER_OPERATION.getExchang());
        }
        return status;
    }


    /**
     * 设置待客户确认状态(草稿--》无需确认)
     *
     * @param orderSn
     * @return
     */
    @Override
    @Transactional
    public boolean setCustomerNoNeedConfirmStatus(String orderSn) {
        if (Objects.isNull(orderSn)) {
            return false;
        }
        OrderPO orderPO = new OrderPO();
        orderPO.setCustomerConfirmStatus(CustomerConfirmStatusEnum.NO_NEED_CONFIRM.getValue());
        orderPO.setCustomerConfirmStatusDesc(CustomerConfirmStatusEnum.NO_NEED_CONFIRM.getDesc());
        LambdaUpdateWrapper<OrderPO> updateWrapper = Wrappers.lambdaUpdate(OrderPO.class);
        updateWrapper.eq(OrderPO::getOrderSn, orderSn);
        // 代客下单新增预付订单下单,增加预付定金订单状态
        List<Integer> orderState = Lists.newArrayList();
        orderState.add(OrderStatusEnum.WAIT_PAY.getValue());
        orderState.add(OrderStatusEnum.WAIT_PAY_DEPOSIT.getValue());
        updateWrapper.in(OrderPO::getOrderState, orderState);
        updateWrapper.eq(OrderPO::getCustomerConfirmStatus, CustomerConfirmStatusEnum.DRAFT.getValue());
        boolean status = super.update(orderPO, updateWrapper);
        if (Boolean.TRUE.equals(status)) {
            log.info(
                    "【setCustomerUnConfirmStatus】////////////////////无需客户确认，成功开始执行后续流程////////////////////////orderSn:{}",
                    orderSn);
            OrderPO order = orderService.getByOrderSn(orderSn);
            Member member = UserUtil.getUser(request, Member.class);
            Integer logRole = OrderConst.LOG_ROLE_MEMBER;
            Long logUserId = Long.valueOf(member.getMemberId());
            String logUserName = member.getMemberName();
            BizUserInfoDTO bizUserInfoDTO = OrderBuilder.getUser(request, BizUserInfoDTO.class);// BAPP 操作
            Member stationMaster = OrderBuilder.getStationMasterUser(request);// 站长 操作
            if (Objects.nonNull(bizUserInfoDTO)) {
                logRole = OrderConst.LOG_ROLE_CUSTOMER_MANAGER;
                logUserId = Long.valueOf(bizUserInfoDTO.getEmployeeId());
                logUserName = bizUserInfoDTO.getUserName();
            } else if (Objects.nonNull(stationMaster)) {
                logRole = OrderConst.LOG_ROLE_STATIONMASTER;
                logUserId = Long.valueOf(stationMaster.getMemberId());
                logUserName = stationMaster.getMemberName();
            }
            // -bz_order_log 记录订单日志
            orderLogModel.insertOrderLog(logRole, logUserId, logUserName, orderSn, order.getOrderState(),
                    order.getOrderState(), LoanStatusEnum.DEFAULT.getValue(), "无需客户确认", OrderCreateChannel.APP);
            log.info("【setCustomerUnConfirmStatus】////////////////////无需客户确认////////////////////////orderSn:{}",
                    orderSn);
        }
        return status;
    }

    @Override
    @Transactional
    public boolean setCustomerConfirmStatus(String orderSn, String facePhotoDocId) {
        log.info("setCustomerConfirmStatus orderSn:{},facePhotoDocId:{}", orderSn, facePhotoDocId);
        if (Objects.isNull(orderSn)) {
            return false;
        }
        OrderPO orderPO = new OrderPO();
        orderPO.setCustomerConfirmStatus(CustomerConfirmStatusEnum.CONFIRM.getValue());
        orderPO.setCustomerConfirmStatusDesc(CustomerConfirmStatusEnum.CONFIRM.getDesc());
        LambdaUpdateWrapper<OrderPO> updateWrapper = Wrappers.lambdaUpdate(OrderPO.class);
        updateWrapper.eq(OrderPO::getOrderSn, orderSn);
        // 等于待付款或者（预付定金且为待支付定金）
        updateWrapper.and(uw -> uw.eq(OrderPO::getOrderState, OrderStatusEnum.WAIT_PAY.getValue())
                .or(uw2 -> uw2.eq(OrderPO::getOrderType, PromotionConst.PROMOTION_TYPE_107)
                        .eq(OrderPO::getOrderState, OrderStatusEnum.WAIT_PAY_DEPOSIT.getValue()))
        );
//        updateWrapper.eq(OrderPO::getOrderState, OrderStatusEnum.WAIT_PAY.getValue());
        updateWrapper.eq(OrderPO::getCustomerConfirmStatus, CustomerConfirmStatusEnum.UNCONFIRMED.getValue());
        boolean status = super.update(orderPO, updateWrapper);
        if (Boolean.TRUE.equals(status)) {
            log.info("【setCustomerConfirmStatus】////////////////////客户确认，成功开始执行后续流程////////////////////////orderSn:{}",
                    orderSn);
            OrderPO order = orderService.getByOrderSn(orderSn);
            Member member = UserUtil.getUser(request, Member.class);
            // -bz_order_log 记录订单日志
            orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_MEMBER, Long.valueOf(member.getMemberId()),
                    member.getMemberName(), orderSn, order.getOrderState(), order.getOrderState(),
                    LoanStatusEnum.DEFAULT.getValue(), "客户确认完成", OrderCreateChannel.APP);
            log.info("【setCustomerConfirmStatus】////////////////////客户确认，发送MQ消息////////////////////////orderSn:{}",
                    orderSn);
            OrderOperationEventEnum customerConfirm = OrderOperationEventEnum.CUSTOMER_CONFIRM;
            OrderOperationEventNotifyDTO message =
                    new OrderOperationEventNotifyDTO(customerConfirm.getCode(), customerConfirm.getDesc(), orderSn);
            orderCreateHelper.sendMqEventMessageByTransactionCommit(message,
                    RabbitMqEventEnum.EXCHANGE_ORDER_OPERATION.getExchang());
            if (StringUtils.isNotBlank(facePhotoDocId)) {
                log.info("setCustomerConfirmStatus 更新刷脸id开始");
                // 更新刷脸id
                OrderPayPO orderPayPO = orderPayModel.getOrderPayByPaySn(order.getPaySn());
                if (Objects.nonNull(orderPayPO)){
                    JSONObject payWayExtraInfo = orderPayPO.getPayWayExtraInfo();
                    OrderPayInfoVO.EnjoyPayExtraInfo enjoyPayExtraInfo = new OrderPayInfoVO.EnjoyPayExtraInfo();
                    if (Objects.nonNull(payWayExtraInfo)) {
                        log.info("setCustomerConfirmStatus payWayExtraInfo为空");
                        enjoyPayExtraInfo = JSON.parseObject(
                                payWayExtraInfo.toJSONString(), OrderPayInfoVO.EnjoyPayExtraInfo.class);
                    }
                    enjoyPayExtraInfo.setFacePhotoDocId(facePhotoDocId);
                    OrderPayPO updateOrderPay = new OrderPayPO();
                    JSONObject enjoyPayInfo = orderPayModel.enjoyPayInfo(enjoyPayExtraInfo);
                    updateOrderPay.setPayWayExtraInfo(enjoyPayInfo);
                    LambdaUpdateWrapper<OrderPayPO> payUpdateWrapper = new LambdaUpdateWrapper<>();
                    payUpdateWrapper.eq(OrderPayPO::getPayId, orderPayPO.getPayId());
                    AssertUtil.isTrue(!orderPayService.update(updateOrderPay, payUpdateWrapper),
                            String.format("更新支付订单失败,paySn:%s", orderPayPO.getPaySn()));
                }else{
                    log.info("setCustomerConfirmStatus orderPayPo为空");
                }
                log.info("setCustomerConfirmStatus 更新刷脸id结束");
            }
        }
        return status;
    }


    @Override
    @Transactional
    public boolean setCustomerConfirmStatusForZeroOrder(String orderSn) {
        if (Objects.isNull(orderSn)) {
            return false;
        }
        OrderPO orderPO = new OrderPO();
        orderPO.setCustomerConfirmStatus(CustomerConfirmStatusEnum.CONFIRM.getValue());
        orderPO.setCustomerConfirmStatusDesc(CustomerConfirmStatusEnum.CONFIRM.getDesc());
        LambdaUpdateWrapper<OrderPO> updateWrapper = Wrappers.lambdaUpdate(OrderPO.class);
        updateWrapper.eq(OrderPO::getOrderSn, orderSn);
        updateWrapper.eq(OrderPO::getCustomerConfirmStatus, CustomerConfirmStatusEnum.DRAFT.getValue());
        boolean status = super.update(orderPO, updateWrapper);
        if (Boolean.TRUE.equals(status)) {
            log.info("【setCustomerConfirmStatus】////////////////////客户确认，成功开始执行后续流程////////////////////////orderSn:{}",
                    orderSn);
            OrderPO order = orderService.getByOrderSn(orderSn);
            Member member = UserUtil.getUser(request, Member.class);
            // -bz_order_log 记录订单日志
            orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_MEMBER, Long.valueOf(member.getMemberId()),
                    member.getMemberName(), orderSn, order.getOrderState(), order.getOrderState(),
                    LoanStatusEnum.DEFAULT.getValue(), "客户确认完成", OrderCreateChannel.APP);
            log.info("【setCustomerConfirmStatus】////////////////////客户确认，发送MQ消息////////////////////////orderSn:{}",
                    orderSn);
            OrderOperationEventEnum customerConfirm = OrderOperationEventEnum.CUSTOMER_CONFIRM;
            OrderOperationEventNotifyDTO message =
                    new OrderOperationEventNotifyDTO(customerConfirm.getCode(), customerConfirm.getDesc(), orderSn);
            orderCreateHelper.sendMqEventMessageByTransactionCommit(message,
                    RabbitMqEventEnum.EXCHANGE_ORDER_OPERATION.getExchang());
        }
        return status;
    }

    @Override
    @Transactional
    public boolean setCustomerConfirmRefuseStatus(String orderSn, Integer optRole, Long logUserId, String logUserName) {
        if (Objects.isNull(orderSn)) {
            return false;
        }
        OrderPO orderPO = new OrderPO();
        orderPO.setCustomerConfirmStatus(CustomerConfirmStatusEnum.REFUSE.getValue());
        orderPO.setCustomerConfirmStatusDesc(CustomerConfirmStatusEnum.REFUSE.getDesc());
        LambdaUpdateWrapper<OrderPO> updateWrapper = Wrappers.lambdaUpdate(OrderPO.class);
        updateWrapper.eq(OrderPO::getOrderSn, orderSn);
        updateWrapper.in(OrderPO::getOrderState, OrderStatusEnum.WAIT_PAY.getValue(),
                OrderStatusEnum.DEAL_PAY.getValue());
        updateWrapper.ne(OrderPO::getCustomerConfirmStatus, CustomerConfirmStatusEnum.CONFIRM.getValue());
        boolean status = super.update(orderPO, updateWrapper);
        if (Boolean.TRUE.equals(status)) {
            log.info(
                    "【setCustomerConfirmRefuseStatus】////////////////////客户拒绝，成功开始执行后续流程////////////////////////orderSn:{}",
                    orderSn);
            OrderPO order = orderService.getByOrderSn(orderSn);
            // -bz_order_log 记录订单日志
            orderLogModel.insertOrderLog(optRole, logUserId, logUserName, orderSn, order.getOrderState(),
                    order.getOrderState(), LoanStatusEnum.DEFAULT.getValue(), "客户拒绝", OrderCreateChannel.APP);
            log.info(
                    "【setCustomerConfirmRefuseStatus】////////////////////客户确认拒绝，发送MQ消息////////////////////////orderSn:{}",
                    orderSn);
            OrderOperationEventEnum customerConfirmRefuse = OrderOperationEventEnum.CUSTOMER_CONFIRM_REFUSE;
            OrderOperationEventNotifyDTO message = new OrderOperationEventNotifyDTO(customerConfirmRefuse.getCode(),
                    customerConfirmRefuse.getDesc(), orderSn);
            orderCreateHelper.sendMqEventMessageByTransactionCommit(message,
                    RabbitMqEventEnum.EXCHANGE_ORDER_OPERATION.getExchang());
        }
        return status;
    }

    @Override
    public boolean setCustomerConfirmClosedStatus(String orderSn, Integer optRole, Long logUserId, String logUserName) {
        if (Objects.isNull(orderSn)) {
            return false;
        }
        OrderPO orderPO = new OrderPO();
        orderPO.setCustomerConfirmStatus(CustomerConfirmStatusEnum.CLOSED.getValue());
        orderPO.setCustomerConfirmStatusDesc(CustomerConfirmStatusEnum.CLOSED.getDesc());
        LambdaUpdateWrapper<OrderPO> updateWrapper = Wrappers.lambdaUpdate(OrderPO.class);
        updateWrapper.eq(OrderPO::getOrderSn, orderSn);
        updateWrapper.in(OrderPO::getCustomerConfirmStatus, CustomerConfirmStatusEnum.UNCONFIRMED.getValue(),
                CustomerConfirmStatusEnum.DRAFT.getValue());
        boolean status = super.update(orderPO, updateWrapper);
        if (Boolean.TRUE.equals(status)) {
            log.info(
                    "【setCustomerConfirmRefuseStatus】////////////////////订单取消，确认关闭，成功开始执行后续流程////////////////////////orderSn:{}",
                    orderSn);
            OrderPO order = orderService.getByOrderSn(orderSn);
            // -bz_order_log 记录订单日志
            orderLogModel.insertOrderLog(optRole, logUserId, logUserName, orderSn, order.getOrderState(),
                    order.getOrderState(), LoanStatusEnum.DEFAULT.getValue(), "客户确认关闭", OrderCreateChannel.APP);
            log.info(
                    "【setCustomerConfirmRefuseStatus】////////////////////订单取消，确认关闭，发送MQ消息////////////////////////orderSn:{}",
                    orderSn);
            OrderOperationEventEnum customerConfirmRefuse = OrderOperationEventEnum.CUSTOMER_CONFIRM_CLOSED;
            OrderOperationEventNotifyDTO message = new OrderOperationEventNotifyDTO(customerConfirmRefuse.getCode(),
                    customerConfirmRefuse.getDesc(), orderSn);
            orderCreateHelper.sendMqEventMessageByTransactionCommit(message,
                    RabbitMqEventEnum.EXCHANGE_ORDER_OPERATION.getExchang());
        }
        return status;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "masterdbTx")
    public Result<Long> processOrderCommission(DistributionCommissionUpdateDTO dto) {
        OrderPO orderPO = this.getByOrderSn(dto.getOrderSn());
        if (Objects.isNull(orderPO)) {
            log.error("未找到分销系统通知的订单记录,orderSn:{},", dto.getOrderSn());
            Result<Long> falseResult = new Result<>();
            falseResult.setData(null);
            falseResult.setSuccess(false);
            falseResult.addError(String.valueOf(ErrorCodeEnum.C.RESULT_INVALID.getCode()), "未找到分销系统通知的订单记录", "");
            return falseResult;
        }

        if (orderPO.getExchangeFlag() != null
                && orderPO.getExchangeFlag().equals(ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2)) {
            log.error("换货后的销售订单不计算佣金, orderSn:{},", dto.getOrderSn());
            Result<Long> falseResult = new Result<>();
            falseResult.setData(null);
            falseResult.setSuccess(false);
            falseResult.addError(String.valueOf(ErrorCodeEnum.C.RESULT_INVALID.getCode()), "换货后的销售订单不计算佣金", "");
            return falseResult;
        }

        List<OrderItemCommissionDTO> data = dto.getData();
        if (CollectionUtils.isNotEmpty(data)) {
//            BigDecimal orderCommission = BigDecimal.ZERO;
            BigDecimal orderCommission = data.stream().map(OrderItemCommissionDTO::getOrderOrdinaryCommission).reduce(BigDecimal.ZERO, BigDecimal::add);

            // 校验【传递的订单佣金】是否等于【商品维度佣金的汇总】
            if (orderCommission.compareTo(dto.getOrderCommission()) != 0) {
                throw new MallException(MessageFormat.format("dbc mq message: order commission not same as the sum of " +
                        "product commission, the order commission is {0}, the sum of order product commission is {1}, " +
                        "orderSn is {2}, please check it", dto.getOrderCommission(), orderCommission, orderPO.getOrderSn()));
            }

            LambdaUpdateWrapper<OrderPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(OrderPO::getOrderCommission, orderCommission);
            updateWrapper.set(OrderPO::getSettlementPrice, orderPO.getSettlementPrice().subtract(orderCommission));
            // 更新订单佣金服务费
            if (Objects.nonNull(dto.getCommissionServiceFee())) {
                updateWrapper.set(OrderPO::getCommissionServiceFee, dto.getCommissionServiceFee());
            }

            updateWrapper.eq(OrderPO::getOrderId, orderPO.getOrderId());
            if (!this.update(updateWrapper)) {
                throw new MallException("【严重】更新订单的分销佣金失败,orderSn:" + orderPO.getOrderSn());
            }
            orderPO.setOrderCommission(orderCommission);
            orderPO.setSettlementPrice(orderPO.getSettlementPrice().subtract(orderCommission));

            for (OrderItemCommissionDTO datum : data) {
//                orderCommission = orderCommission.add(datum.getOrderOrdinaryCommission());

                LambdaUpdateWrapper<OrderProductPO> orderProductUpdater = new LambdaUpdateWrapper<>();
                orderProductUpdater.set(OrderProductPO::getOrderCommission, datum.getOrderOrdinaryCommission());
                orderProductUpdater.set(OrderProductPO::getCommissionAmount, datum.getOrderOrdinaryCommission());

                orderProductUpdater.eq(OrderProductPO::getOrderSn, orderPO.getOrderSn());
                orderProductUpdater.eq(OrderProductPO::getProductId, datum.getProductId());
                if (!orderProductService.update(orderProductUpdater)) {
                    throw new MallException("【严重】更新商品的分销佣金失败,orderSn:" + orderPO.getOrderSn());
                }
            }

        }

        // 更新佣金资金项为生效
        Result<Void> commissionRecordResult = orderAmountRecordService.modifyOrderAmountState(orderPO.getOrderSn(),
                OrderAmountTypeEnum.ORDER_COMMISSION, orderPO.getOrderCommission(), OrderAmountStateEnum.EFFECT);
        if (!commissionRecordResult.isSuccess()) {
            Result<Long> falseResult = new Result<>();
            falseResult.setData(null);
            falseResult.setSuccess(false);
            falseResult.addError(String.valueOf(ErrorCodeEnum.S.SYSTEM_EXCEPTION.getCode()), "记录订单佣金生效状态失败", "");
            return falseResult;
        }

        // 更新佣金服务费资金项为生效
        if (Objects.nonNull(dto.getCommissionServiceFee())) {
            Result<Void> commissionServiceFeeRecordResult = orderAmountRecordService.modifyOrderAmountState(orderPO.getOrderSn(),
                    OrderAmountTypeEnum.COMMISSION_SERVICE_FEE, dto.getCommissionServiceFee(), OrderAmountStateEnum.EFFECT);
            if (!commissionServiceFeeRecordResult.isSuccess()) {
                Result<Long> falseResult = new Result<>();
                falseResult.setData(null);
                falseResult.setSuccess(false);
                falseResult.addError(String.valueOf(ErrorCodeEnum.S.SYSTEM_EXCEPTION.getCode()), "记录订单佣金服务费生效状态失败", "");
                return falseResult;
            }
        }

        OrderAmountUpdateDTO amountUpdateDTO = new OrderAmountUpdateDTO(orderPO.getOrderSn(),
                BillAmountTypeEnum.EXPEND_COMMISSION.getValue(),
                orderPO.getOrderCommission().add(orderPO.getBusinessCommission()),
                BillAmountTypeEnum.EXPEND_COMMISSION_SERVICE_FEE.getValue(),
                Objects.nonNull(dto.getCommissionServiceFee()) ? dto.getCommissionServiceFee() : BigDecimal.ZERO);
        Long eventId = commonMqEventService.saveEvent(amountUpdateDTO,
                RabbitMqConfig.ORDER_RELATION_AMOUNT_UPDATE_FANOUT_EXCHANGE);
        Result<Long> trueResult = new Result<>();
        trueResult.setData(eventId);
        trueResult.setSuccess(true);
        return trueResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "masterdbTx")
    public Long processOrderCommissionIncentive(DistributionCommissionIncentiveUpdateDTO dto) {
        OrderPO orderPO = this.getByOrderSn(dto.getOrderSn());
        AssertUtil.notNull(orderPO, "分销佣金激励费通知未找到对应的订单：" + dto.getOrderSn());

        // 换货单不处理佣金激励费
        if (ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2 == orderPO.getExchangeFlag()) {
            log.info("换货订单：{} 不计算佣金激励费", orderPO.getOrderSn());
            return CommonConst.DEFAULT_LONG;
        }

        // 更新订单的佣金激励费和佣金服务费激励费资金项
        LambdaUpdateWrapper<OrderPO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(OrderPO::getOrderSn, orderPO.getOrderSn());
        updateWrapper.set(OrderPO::getOrderCommissionIncentiveFee, dto.getOrderCommissionIncentiveFee());
        updateWrapper.set(OrderPO::getCommissionIncentiveServiceFee, dto.getCommissionIncentiveServiceFee());
        AssertUtil.isTrue(!this.update(updateWrapper), "更新订单佣金激励费信息失败，订单号：" + dto.getOrderSn());

        // 更新订单资金项的有效性
        // 佣金激励费
        Result<Void> commissionIncentiveResult = orderAmountRecordService.modifyOrderAmountState(orderPO.getOrderSn(), OrderAmountTypeEnum.ORDER_COMMISSION_INCENTIVE_FEE,
                dto.getOrderCommissionIncentiveFee(), OrderAmountStateEnum.EFFECT);
        AssertUtil.isTrue(!commissionIncentiveResult.isSuccess(), "更新订单佣金激励费资金项有效性失败，订单号：" + dto.getOrderSn());
        // 佣金服务费激励费
        Result<Void> commissionServiceIncentiveResult = orderAmountRecordService.modifyOrderAmountState(orderPO.getOrderSn(), OrderAmountTypeEnum.COMMISSION_INCENTIVE_SERVICE_FEE,
                dto.getCommissionIncentiveServiceFee(), OrderAmountStateEnum.EFFECT);
        AssertUtil.isTrue(!commissionServiceIncentiveResult.isSuccess(), "更新订单佣金服务费激励费资金项有效性失败，订单号：" + dto.getOrderSn());

        // 构造对象，发送结算
        OrderAmountUpdateDTO amountUpdateDTO = new OrderAmountUpdateDTO(orderPO.getOrderSn(), BillAmountTypeEnum.EXPEND_ORDER_COMMISSION_INCENTIVE_FEE.getValue(),
                dto.getOrderCommissionIncentiveFee(), BillAmountTypeEnum.EXPEND_COMMISSION_INCENTIVE_SERVICE_FEE.getValue(),
                dto.getCommissionIncentiveServiceFee());
        return commonMqEventService.saveEvent(amountUpdateDTO, RabbitMqConfig.ORDER_RELATION_AMOUNT_UPDATE_FANOUT_EXCHANGE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "masterdbTx")
    public Result<Long> processOrderPlanDiscount(PromotionDiscountMsg dto) {
        BigDecimal notifyPlanDiscountAmount = dto.getPlanDiscountAmount();

        LambdaUpdateWrapper<OrderPO> updateWrapper = new LambdaUpdateWrapper<>();
        OrderPresellVO orderPresell = orderPresellService.findByPayNo(String.valueOf(dto.getTradeNo()));
        String orderSn = StringUtils.EMPTY;
        if (Objects.nonNull(orderPresell)) {
            orderSn = orderPresell.getOrderSn();
        } else {
            OrderPO orderPO = this.getByOrderSn(String.valueOf(dto.getTradeNo()));
            if (Objects.isNull(orderPO)) {
                log.error("营销系统推送预贴息MQ订单不存在,orderSn:{},", dto.getTradeNo());
                Result<Long> falseResult = new Result<>();
                falseResult.setData(null);
                falseResult.setSuccess(false);
                falseResult.addError(String.valueOf(ErrorCodeEnum.C.RESULT_INVALID.getCode()), "营销系统推送预贴息MQ订单不存在", "");
                return falseResult;
            }
            orderSn = orderPO.getOrderSn();
        }
        updateWrapper.eq(OrderPO::getOrderSn, orderSn);
        updateWrapper.set(OrderPO::getNotifyPlanDiscountAmount, notifyPlanDiscountAmount);

        OrderExtendFinancePO financePO = financeService.lambdaQuery().eq(OrderExtendFinancePO::getOrderSn, orderSn).one();

        BigDecimal planDiscountAmount;

        if (Objects.isNull(financePO) || financePO.getPlanDiscountType().equals(PlanDiscountTypeEnum.PLAN_DISCOUNT_AMOUNT.getValue())) {
            updateWrapper.set(OrderPO::getPlanDiscountAmount, notifyPlanDiscountAmount);
            planDiscountAmount = notifyPlanDiscountAmount;
        } else if (financePO.getPlanDiscountType().equals(PlanDiscountTypeEnum.PLAN_DISCOUNT_AMOUNT_RATE.getValue())) {
            BigDecimal planDiscountStoreRate = financePO.getPlanDiscountStoreRate();
            AssertUtil.notNull(planDiscountStoreRate, "指定预贴息总金额比例结算时比例信息为空");
            planDiscountAmount = notifyPlanDiscountAmount.multiply(planDiscountStoreRate).setScale(2, RoundingMode.HALF_UP);
            updateWrapper.set(OrderPO::getPlanDiscountAmount, planDiscountAmount);
        } else if (financePO.getPlanDiscountType().equals(PlanDiscountTypeEnum.PLAN_DISCOUNT_AMOUNT_LIMIT.getValue())) {
            BigDecimal planDiscountUpperLimit = financePO.getPlanDiscountUpperLimit();
            AssertUtil.notNull(planDiscountUpperLimit, "指定预贴息总金额上限结算时上限信息为空");
            planDiscountAmount = notifyPlanDiscountAmount.compareTo(planDiscountUpperLimit) >= 0
                    ? planDiscountUpperLimit : notifyPlanDiscountAmount;
            updateWrapper.set(OrderPO::getPlanDiscountAmount, planDiscountAmount);
        } else {
            throw new MallException("暂不支持该结算方式");
        }

        if (!this.update(updateWrapper)) {
            throw new MallException("【严重】更新订单的预贴息金额失败,tradeNo:" + dto.getTradeNo());
        }

        // 平摊计算预贴息金额到订单商品行
        orderProductModel.savePlanDiscountAmount2OrderProduct(orderSn, planDiscountAmount);

        Result<Void> recordResult = orderAmountRecordService.modifyOrderAmountState(orderSn,
                OrderAmountTypeEnum.PLAN_DISCOUNT_AMOUNT, planDiscountAmount, OrderAmountStateEnum.EFFECT);
        if (!recordResult.isSuccess()) {
            Result<Long> falseResult = new Result<>();
            falseResult.setData(null);
            falseResult.setSuccess(false);
            falseResult.addError(String.valueOf(ErrorCodeEnum.S.SYSTEM_EXCEPTION.getCode()), "记录订单预贴息金额生效状态失败", "");
            return falseResult;
        }
        OrderAmountUpdateDTO amountUpdateDTO = new OrderAmountUpdateDTO(orderSn,
                BillAmountTypeEnum.EXPEND_LOAN.getValue(), planDiscountAmount);
        Long eventId = commonMqEventService.saveEvent(amountUpdateDTO, RabbitMqConfig.ORDER_RELATION_AMOUNT_UPDATE_FANOUT_EXCHANGE);
        Result<Long> trueResult = new Result<>();
        trueResult.setData(eventId);
        trueResult.setSuccess(true);
        trueResult.addError("0", "OK", "");
        return trueResult;
    }

    /**
     * 获取支付的方式+支付时间 非贷款类支付：售后延迟时间至 支付时间+80天，贷款类支付：售后延长时间至+180天，组合支付取短 更新
     */
    @Override
    public Boolean extendedAfterSales(ExtendedAfterSalesDTO extendedAfterSalesDTO, Integer logRole, Long logUserId,
                                      String logUserName) {
        OrderPO orderPO = orderModel.getOrderByOrderSn(extendedAfterSalesDTO.getOrderSn());
        List<Integer> performanceModeList = JSONObject.parseArray(orderPO.getPerformanceModes(), Integer.class);

        BizAssertUtil.isTrue(
                performanceModeList.contains(OrderPerformanceModeEnum.PERFORMANCE_MODE_SUPPLIER.getValue()),
                "外部系统订单，不能开启超期售后!");
        BizAssertUtil.isTrue(OrderConst.ORDER_STATE_40 != orderPO.getOrderState(), "该订单未处于完成状态，不能开启超期售后!");
        // 截止日期
        Date afterSaleDeadlineDate = null;
        Date afterSaleDeadline;
        String afterSaleDeadlineDateStr;

        // 预付订金
        if (PromotionConst.PROMOTION_TYPE_107 == orderPO.getOrderType()) {
            List<OrderPresellPO> orderPreSellPOList =
                    orderPresellMapper.getPreSellOrderDetailByOrderSn(extendedAfterSalesDTO.getOrderSn());
            for (OrderPresellPO orderPreSellPO : orderPreSellPOList) {
                PayMethodEnum payMethodEnum = PayMethodEnum.getByPaymentCode(orderPreSellPO.getPaymentCode());
                if (PayMethodEnum.ALIPAY.equals(payMethodEnum)) {
                    afterSaleDeadlineDate = DateUtil.addDays(DateUtil.localDateTimeToDate(orderPreSellPO.getPayTime()),
                            afterSaleAlipayDelayDays);
                    break;
                }
                if (PayMethodEnum.isLoanPay(payMethodEnum)) {
                    afterSaleDeadlineDate = DateUtil.addDays(orderPO.getPayTime(), afterSaleLoanPayDelayDays);
                    break;
                }
                afterSaleDeadlineDate = DateUtil.addDays(DateUtil.localDateTimeToDate(orderPreSellPO.getPayTime()),
                        afterSaleCommonDelayDays);
            }

        } else {
            PayMethodEnum payMethodEnum = PayMethodEnum.getByPaymentCode(orderPO.getPaymentCode());
            if (PayMethodEnum.ALIPAY.equals(payMethodEnum)) {
                afterSaleDeadlineDate = DateUtil.addDays(orderPO.getPayTime(), afterSaleAlipayDelayDays);
            } else if (PayMethodEnum.isLoanPay(payMethodEnum)) {
                afterSaleDeadlineDate = DateUtil.addDays(orderPO.getPayTime(), afterSaleLoanPayDelayDays);
            } else {
                afterSaleDeadlineDate = DateUtil.addDays(orderPO.getPayTime(), afterSaleCommonDelayDays);
            }
        }
        afterSaleDeadlineDateStr = DateUtil.format(afterSaleDeadlineDate) + " 23:59:59";
        afterSaleDeadline = DateUtil.parse(afterSaleDeadlineDateStr, DateUtil.FORMAT_TIME);

        BizAssertUtil.isTrue(DateUtil.dealRemainTime(afterSaleDeadline, new Date()) <= 0,
                "已超系统限制的最晚售后有效期" + afterSaleDeadlineDateStr + "，开启超期售后失败");

        LambdaUpdateWrapper<OrderPO> updateWrapper = Wrappers.lambdaUpdate(OrderPO.class);
        updateWrapper.set(OrderPO::getAfterSalesDeadline, afterSaleDeadline).eq(OrderPO::getOrderSn,
                orderPO.getOrderSn());
        orderService.update(updateWrapper);

        String remark = "超期售后有效期至" + afterSaleDeadlineDateStr;
        CompletableFuture.runAsync(() -> {
            orderLogModel.insertOrderLog(logRole, logUserId, logUserName, orderPO.getOrderSn(), orderPO.getOrderState(),
                    orderPO.getOrderState(), orderPO.getLoanPayState(), "开启超期售后",
                    OrderCreateChannel.getEnumByValue(extendedAfterSalesDTO.getChannel()), remark);
        });

        return Boolean.TRUE;
    }

    /**
     * 订单改价： 1.订单校验 2.金额校验 3.更新订单金额 4.保存改价记录 5.插入改价轨迹
     *
     * @param renewalPriceReq
     * @return
     */
    @Override
    public Boolean renewalPrice(OrderRenewalPriceReq renewalPriceReq, Long operatorId, String operatorName) {
        /**
         * 订单校验
         */
        OrderPO orderPO = this.getByOrderSn(renewalPriceReq.getOrderSn());
        if (orderPO.getOrderType().equals(OrderTypeEnum.FULL_GIFT.getValue())) {
            throw new BusinessException("满赠订单不支持改价");
        }
        if (orderPO.getOrderType().equals(OrderTypeEnum.COMBINATION.getValue()) &&
                orderPO.getOrderState().equals(OrderStatusEnum.WAIT_PAY.getValue())) {
            throw new BusinessException("组合订单待支付状态不支持改价");
        }
        String verifyResult = this.orderRenewalPriceVerify(orderPO);
        if (!OrderConst.CHECK_SUCCESS.equals(verifyResult)) {
            throw new BusinessException(verifyResult);
        }

        // 订单商品信息
        List<OrderProductPO> orderProductPOS = orderProductService.listByOrderSn(orderPO.getOrderSn());
        Map<Long, OrderProductPO> orderProductPOMap =
                orderProductPOS.stream().collect(Collectors.toMap(OrderProductPO::getOrderProductId, po -> po));

        // 订单扩展信息
        OrderExtendPO orderExtendPO = orderExtendService.getOrderExtendByOrderSn(orderPO.getOrderSn());

        // 查询新批次号落地价
        for (OrderRenewalPriceReq.OrderProductRenewalPriceReq orderProductReq : renewalPriceReq.getOrderProductReqs()) {
            OrderProductPO orderProductPO = orderProductPOMap.get(orderProductReq.getOrderProductId());

            GoodsLandingPricePramaDTO landingPricePramaDTO = new GoodsLandingPricePramaDTO();
            landingPricePramaDTO.setBranchCode(orderExtendPO.getBranch());
            landingPricePramaDTO.setStoreId(orderPO.getStoreId());
            landingPricePramaDTO.setAreaCode(orderPO.getAreaCode());
            landingPricePramaDTO.setFinanceRuleCode(orderPO.getFinanceRuleCode());
            landingPricePramaDTO.setTaxPrice(orderProductReq.getRenewalProductShowPrice());

            landingPricePramaDTO.setProductId(orderProductPO.getProductId());
            landingPricePramaDTO.setSkuId(orderProductPO.getChannelSkuId());
            //如果存在旧的批次号，则可以做为请求商品的批次号候选入参
            if (StringUtils.isNotEmpty(orderProductPO.getBatchNo())) {
                landingPricePramaDTO.setBatchNo(orderProductPO.getBatchNo());
            }
            //如果存在新的批次号，不用管旧的批次是否存在，都以新的批次为准
            if (StringUtils.isNotEmpty(orderProductReq.getRenewalBatchNo())) {
                landingPricePramaDTO.setBatchNo(orderProductReq.getRenewalBatchNo());
            }
            //如果存在旧的价格子单，则可以做为请求商品的价格子单候选入参
            if (StringUtils.isNotEmpty(orderProductPO.getPurchaseSubCode())) {
                landingPricePramaDTO.setPurchaseSubCode(orderProductPO.getPurchaseSubCode());
            }
            //如果存在新的价格子单，不用管旧的价格子单是否存在，都以新的价格子单为准
            if (StringUtils.isNotEmpty(orderProductReq.getRenewalPurchaseSubCode())) {
                landingPricePramaDTO.setPurchaseSubCode(orderProductReq.getRenewalPurchaseSubCode());
            }
            //如果数据库存储的或者前端入参传入的批次号或者价格单中，只要有一项不为空，则需要调用商品的落地价试算接口。
            if (!StringUtils.isAllBlank(orderProductPO.getBatchNo(), orderProductReq.getRenewalBatchNo(), orderProductPO.getPurchaseSubCode(), orderProductReq.getRenewalPurchaseSubCode())) {
                GoodsLandingPriceResponse goodsDistributionLandingPrice = goodsFeignIntegration.getGoodsDistributionLandingPrice(landingPricePramaDTO);
                if (Objects.nonNull(goodsDistributionLandingPrice)) {
                    //如果商品域返回了试算的落地价，和前端传入的落地价进行比对，结果不一致就告警
                    if (orderProductReq.getRenewalLandingPrice().compareTo(goodsDistributionLandingPrice.getConvertLandingPrice()) != 0) {
                        throw new MSException(String.valueOf(ErrorCodeEnum.U.ILLEGAL_PARAM.getCode()), "前后端计算的试算后落地价不一致");
                    }
                    orderProductReq.setRenewalLandingPrice(goodsDistributionLandingPrice.getConvertLandingPrice().stripTrailingZeros());
                }
            }
        }

        /**
         * 金额校验
         */
        // 订单金额计算DP
        OrderAmountDP orderAmountDP = new OrderAmountDP(orderPO, orderProductPOS);

        //获取订单改价配置
        boolean switchConfigEnable = switchConfig.getEnable();
        StringBuilder remark = new StringBuilder();
        remark.append("原运费").append(orderPO.getExpressFee());
        remark.append("，修改后运费").append(renewalPriceReq.getRenewalExpressFee());
        for (OrderRenewalPriceReq.OrderProductRenewalPriceReq orderProductReq : renewalPriceReq.getOrderProductReqs()) {
            // 金额校验
            OrderProductPO orderProductPO = orderProductPOMap.get(orderProductReq.getOrderProductId());
            if (Objects.isNull(orderProductPO)) {
                throw new BusinessException("参数错误，请重试");
            }
            //农服新模型需求产品去掉校验含税与落地价校验
            if (switchConfigEnable) {
                if (orderProductReq.getRenewalProductShowPrice().compareTo(orderProductReq.getRenewalLandingPrice()) < 0) {
                    throw new BusinessException("商品" + orderProductPO.getGoodsName() + "单价不能小于落地价，请调整重试");
                }
            }
            // 获取订单金额计算DP
            OrderProductAmountDP orderProductAmountDP =
                    orderAmountDP.getOrderProductAmountDPMap().get(orderProductReq.getOrderProductId());

            // 更新商品金额：运费、落地价、单价
            orderAmountDP.setExpressFee(renewalPriceReq.getRenewalExpressFee());
            orderProductAmountDP.setProductShowPrice(orderProductReq.getRenewalProductShowPrice());
            orderProductAmountDP.setLandingPrice(orderProductReq.getRenewalLandingPrice());

            if (orderProductAmountDP.getMoneyAmount().compareTo(BigDecimal.ZERO) < 0) {
                throw new BusinessException("商品" + orderProductPO.getGoodsName() + "实付金额不能小于0，请调整重试");
            }

            // 改价备注
            remark.append("；商品【").append(orderProductPO.getGoodsName());
            remark.append("】：原单价").append(orderProductPO.getProductShowPrice());
            remark.append("，修改后单价").append(orderProductReq.getRenewalProductShowPrice());
            remark.append("，原落地价").append(orderProductPO.getLandingPrice());
            remark.append("，修改后落地价").append(orderProductReq.getRenewalLandingPrice());
            if (StringUtils.isNotEmpty(orderProductReq.getRenewalBatchNo()) && !orderProductReq.getRenewalBatchNo().equals(orderProductPO.getBatchNo())) {
                remark.append("，原批次号").append(StringUtils.isEmpty(orderProductPO.getBatchNo()) ? "空" : orderProductPO.getBatchNo());
                remark.append("，修改后批次号").append(orderProductReq.getRenewalBatchNo());
            }
            if (StringUtils.isNotEmpty(orderProductReq.getRenewalPurchaseSubCode()) && !orderProductReq.getRenewalPurchaseSubCode().equals(orderProductPO.getPurchaseSubCode())) {
                remark.append("，原价格子单").append(StringUtils.isEmpty(orderProductPO.getPurchaseSubCode()) ? "空" : orderProductPO.getPurchaseSubCode());
                remark.append("，修改后价格子单").append(orderProductReq.getRenewalPurchaseSubCode());
            }
        }

        // 校验订单应付位数
        if (orderAmountDP.getOrderAmountTotal().precision() > 10) {
            throw new BusinessException("订单应付金额最大精度为10位数！");
        }
        // 校验订单实付金额
        if (orderAmountDP.getOrderAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("订单实付金额不能小于等于0，请调整重试");
        }

        /**
         * 改价事务：更新订单金额、外部调用-批次号库存扣减
         */
        orderService.renewalPrice(renewalPriceReq, orderPO, orderExtendPO, orderProductPOMap, orderAmountDP,
                operatorName);

        // 写订单日志
        if (StringUtils.isNotEmpty(renewalPriceReq.getRemark())) {
            remark.append("；").append("备注：").append(renewalPriceReq.getRemark());
        }
        orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_VENDOR, operatorId, operatorName, orderPO.getOrderSn(),
                orderPO.getOrderState(), OrderStatusEnum.CANCELED.getValue(), LoanStatusEnum.DEFAULT.getValue(), "订单改价",
                OrderCreateChannel.WEB, remark.toString());

        return Boolean.TRUE;
    }

    @GlobalTransactional(rollbackFor = Throwable.class)
    @Override
    public Boolean renewalPrice(OrderRenewalPriceReq renewalPriceReq, OrderPO orderPO, OrderExtendPO orderExtendPO,
                                Map<Long, OrderProductPO> orderProductPOMap, OrderAmountDP orderAmountDP, String operatorName) {

        // 更新订单金额
        LambdaUpdateWrapper<OrderPO> updateOrderWrapper = Wrappers.lambdaUpdate();
        updateOrderWrapper.eq(OrderPO::getOrderSn, orderPO.getOrderSn())
                .eq(OrderPO::getOrderState, OrderStatusEnum.WAIT_PAY.getValue())
                .eq(OrderPO::getOrderAmountTotal, orderPO.getOrderAmountTotal());

        OrderPO updateOrder = this.getUpdateOrderPO(orderPO, orderAmountDP);
        boolean update = this.update(updateOrder, updateOrderWrapper);
        if (!update) {
            throw new BusinessException("更新订单金额失败，可能金额已修改，请重试");
        }

        // 更新支付表支付金额
        OrderPayPO orderPayPO = orderPayService.getByPaySn(orderPO.getPaySn());
        LambdaUpdateWrapper<OrderPayPO> updatePay = Wrappers.lambdaUpdate();
        updatePay.eq(OrderPayPO::getPaySn, orderPO.getPaySn())
                .eq(OrderPayPO::getApiPayState, OrderConst.API_PAY_STATE_0)
                .eq(OrderPayPO::getPayAmount, orderPayPO.getPayAmount());

        OrderPayPO updateOrderPay = new OrderPayPO();
        // 计算订单差额，修改订单实付 - 原订单实付
        BigDecimal marginOrderAmount = orderAmountDP.getOrderAmount().subtract(orderPO.getOrderAmount());
        updateOrderPay.setPayAmount(orderPayPO.getPayAmount().add(marginOrderAmount));
        boolean update1 = orderPayService.update(updateOrderPay, updatePay);
        if (!update1) {
            throw new BusinessException("更新订单支付表金额失败，可能金额已修改，请重试");
        }

        // 商品行服务费分摊
        if (updateOrder.getThirdpartnarFee().compareTo(BigDecimal.ZERO) > 0) {
            orderAmountDP.allocationProductFee();
        }

        List<OrderProductPO> updateOrderProductList = new ArrayList<>(orderProductPOMap.size());
        List<OrderPriceRecordPO> saveOrderPriceList = new ArrayList<>(orderProductPOMap.size());
        for (OrderRenewalPriceReq.OrderProductRenewalPriceReq orderProductReq : renewalPriceReq.getOrderProductReqs()) {
            OrderProductPO orderProductPO = orderProductPOMap.get(orderProductReq.getOrderProductId());
            OrderProductAmountDP orderProductAmountDP =
                    orderAmountDP.getOrderProductAmountDPMap().get(orderProductReq.getOrderProductId());

            // 更新商品金额
            OrderProductPO updateOrderProduct =
                    this.getUpdateOrderProductPO(orderProductAmountDP, orderProductReq.getRenewalBatchNo(), orderProductReq.getRenewalPurchaseSubCode());
            updateOrderProductList.add(updateOrderProduct);

            // 改价记录
            OrderPriceRecordPO orderPriceRecordPO = this.getSaveOrderPriceRecordPO(orderProductPO, orderProductAmountDP,
                    orderPO.getExpressFee(), renewalPriceReq.getRenewalExpressFee(), operatorName);
            saveOrderPriceList.add(orderPriceRecordPO);

        }

        // 更新商品金额
        orderProductService.updateBatchById(updateOrderProductList);

        // 保存改价记录
        orderPriceRecordService.saveBatch(saveOrderPriceList);

        // 批次号库存占用、取消占用
        for (OrderRenewalPriceReq.OrderProductRenewalPriceReq orderProductReq : renewalPriceReq.getOrderProductReqs()) {
            OrderProductPO orderProductPO = orderProductPOMap.get(orderProductReq.getOrderProductId());
            // 改价入参的价格单不为空&&改价入参的价格单!=数据库里面的价格单，就去调用商品那边的扣减新库存，退还老库存
            if (StringUtils.isNotEmpty(orderProductReq.getRenewalPurchaseSubCode()) && !orderProductReq.getRenewalPurchaseSubCode().equals(orderProductPO.getPurchaseSubCode())) {
                Integer isCombination = orderPO.getOrderType().equals(OrderTypeEnum.COMBINATION.getValue()) ? 1 : 0;
                // 新批次号库存扣减
                goodsStockService.goodsStock(orderPO.getOrderSn(), null, orderProductPO.getProductId(),
                        orderPO.getAreaCode(), orderProductPO.getProductNum(), orderPO.getFinanceRuleCode(),
                        orderProductReq.getRenewalBatchNo(), orderProductReq.getRenewalPurchaseSubCode(), orderProductPO.getChannelSkuId(), orderExtendPO.getBranch(),
                        orderExtendPO.getReceiveBranchCode(), orderExtendPO.getWarehouseCode(), EventStockTypeEnum.ORDER_CHANGE_PRICE_OUT_STOCK,
                        BizTypeEnum.RENEWAL_PRICE_REDUCE_STOCK, OrderConst.LOG_USER_NAME, orderPO, orderProductPO.getChannelSkuUnit(), null, isCombination);
                // 原批次号库存回退
                goodsStockService.goodsStock(orderPO.getOrderSn(), null, orderProductPO.getProductId(),
                        orderPO.getAreaCode(), -orderProductPO.getProductNum(), orderPO.getFinanceRuleCode(),
                        orderProductPO.getBatchNo(), orderProductPO.getPurchaseSubCode(), orderProductPO.getChannelSkuId(), orderExtendPO.getBranch(),
                        orderExtendPO.getReceiveBranchCode(), orderExtendPO.getWarehouseCode(), EventStockTypeEnum.ORDER_CHANGE_PRICE_IN_STOCK,
                        BizTypeEnum.RENEWAL_PRICE_INCREASE_STOCK, OrderConst.LOG_USER_NAME, orderPO, orderProductPO.getChannelSkuUnit(), null, isCombination);
            }
        }

        // 重推收银台
        cashierIntegration.pushToCashier(orderPO.getPaySn(), null);

        return Boolean.TRUE;
    }

    @Override
    @Transactional
    public Boolean loanStatusChange(String businessNo, String paymentCode) {
        AssertUtil.notEmpty(paymentCode, "支付方式编码为空");
        PayMethodEnum payMethodEnum;
        payMethodEnum = PayMethodEnum.getByPaymentCode(paymentCode);
        if (Objects.isNull(payMethodEnum)) {
            throw new MallException("支付回调时支付方式不匹配，请排查");
        }
        BizAssertUtil.notEmpty(businessNo, "业务订单号不能为空");

        OrderPO orderPO = getOrderPo(businessNo);
        if (!PayMethodEnum.isLoanPay(payMethodEnum.getValue())
                && !PayMethodEnum.isBankTransfer(payMethodEnum)
                && orderPO.getOrderType() != OrderTypeEnum.PRE_SELL_DEPOSIT.getValue()) {
            orderPayService.lambdaUpdate()
                    .eq(OrderPayPO::getPaySn, orderPO.getPaySn())
                    .set(OrderPayPO::getPaymentCode, payMethodEnum.getValue())
                    .set(OrderPayPO::getPaymentName, payMethodEnum.getDesc())
                    .update();
            return this.lambdaUpdate()
                    .set(OrderPO::getPaymentCode, payMethodEnum.getValue())
                    .set(OrderPO::getPaymentName, payMethodEnum.getDesc())
                    .eq(OrderPO::getPaySn, orderPO.getPaySn())
                    .eq(OrderPO::getOrderState, OrderStatusEnum.WAIT_PAY.getValue()).update();

        }

        if (orderPO.getOrderType() == OrderTypeEnum.PRE_SELL_DEPOSIT.getValue()) {
            List<OrderPresellPO> orderPresellPOS =
                    orderPresellService.lambdaQuery().eq(OrderPresellPO::getPaySn, orderPO.getPaySn()).list();
            //用呗和银行卡付款才更改为付款中
            if (PayMethodEnum.isLoanPay(payMethodEnum.getValue()) || PayMethodEnum.isBankTransfer(payMethodEnum)) {
                for (OrderPresellPO orderPresellPO : orderPresellPOS) {
                    String payNo = orderPresellPO.getPayNo();
                    // 回调时是支付定金，则只修改预售表订单状态，回调时是支付尾款，则修改预售表+订单状态
                    if (orderPresellPO.getType().equals(PresellCapitalTypeEnum.DEPOSIT.getValue())
                            && orderPresellPO.getPayStatus() == CommonConst.PAY_STATUS_1) {
                        updatePresellPayStatus(payMethodEnum, payNo);
                        break;
                    } else if (orderPresellPO.getType().equals(PresellCapitalTypeEnum.BALANCE.getValue())
                            && orderPresellPO.getPayStatus() == CommonConst.PAY_STATUS_1) {
                        orderService.lambdaUpdate().eq(OrderPO::getOrderState, OrderStatusEnum.WAIT_PAY.getValue())
                                .eq(OrderPO::getOrderSn, orderPresellPO.getOrderSn())
                                .set(OrderPO::getOrderState, OrderStatusEnum.DEAL_PAY.getValue()).update();

                        updatePresellPayStatus(payMethodEnum, payNo);
                    }
                }
            }
            return true;
        }
        orderPayService.lambdaUpdate()
                .eq(OrderPayPO::getPaySn, orderPO.getPaySn())
                .set(OrderPayPO::getPaymentCode, payMethodEnum.getValue())
                .set(OrderPayPO::getPaymentName, payMethodEnum.getDesc())
                .update();

        Boolean result = this.lambdaUpdate()
                .set(OrderPO::getOrderState, OrderStatusEnum.DEAL_PAY.getValue())
                .set(OrderPO::getPaymentCode, payMethodEnum.getValue())
                .set(OrderPO::getPaymentName, payMethodEnum.getDesc())
                .eq(OrderPO::getPaySn, orderPO.getPaySn())
                .eq(OrderPO::getOrderState, OrderStatusEnum.WAIT_PAY.getValue()).update();

        log.info("loanStatusChange orderSn:{} PAYING message start", orderPO.getOrderSn());

        //发送支付中的消息
        orderCreateHelper.addOrderChangeEvent(orderPO, OrderEventEnum.PAYING, orderPO.getCreateTime());
        return result;
    }

    private OrderPO getOrderPo(String businessNo) {
        String orderSn;
        OrderPO orderPO = this.lambdaQuery().eq(OrderPO::getOrderSn, businessNo).last("limit 1").one();
        if (Objects.isNull(orderPO)) {
            OrderPresellPO orderPresellPO = orderPresellService.lambdaQuery()
                    .eq(OrderPresellPO::getPayNo, businessNo)
                    .last("limit 1")
                    .one();
            if (Objects.isNull(orderPresellPO)) {
                OrderPayPO orderPayPO = orderPayService.lambdaQuery().eq(OrderPayPO::getPaySn, businessNo).last("limit 1").one();
                BizAssertUtil.notNull(orderPayPO, "订单不存在，请排查");
                String paySn = orderPayPO.getPaySn();
                orderPO = this.lambdaQuery().eq(OrderPO::getPaySn, paySn).last("limit 1").one();
            } else {
                orderSn = orderPresellPO.getOrderSn();
                orderPO = this.lambdaQuery().eq(OrderPO::getOrderSn, orderSn).last("limit 1").one();
            }
        }
        return orderPO;
    }

    private void updatePresellPayStatus(PayMethodEnum payMethodEnum, String payNo) {
        orderPresellService.lambdaUpdate().eq(OrderPresellPO::getPayStatus, CommonConst.PAY_STATUS_1)
                .eq(OrderPresellPO::getPayNo, payNo).set(OrderPresellPO::getPayStatus, CommonConst.PAY_STATUS_2)
                .set(OrderPresellPO::getPaymentCode, payMethodEnum == null ? null : payMethodEnum.getValue())
                .set(OrderPresellPO::getPaymentName, payMethodEnum == null ? null : payMethodEnum.getDesc()).update();
        orderPayRecordService.lambdaUpdate().eq(OrderPayRecordPO::getPayStatus, CommonConst.PAY_STATUS_1)
                .eq(OrderPayRecordPO::getPayNo, payNo).set(OrderPayRecordPO::getPayStatus, CommonConst.PAY_STATUS_2)
                .set(OrderPayRecordPO::getPaymentCode, payMethodEnum == null ? null : payMethodEnum.getValue())
                .set(OrderPayRecordPO::getPaymentName, payMethodEnum == null ? null : payMethodEnum.getDesc()).update();
    }

    /**
     * 订单商品服务费分摊计算
     *
     * @param orderPO                原订单信息
     * @param updateOrder            最新订单信息：包含最新订单服务费、订单应付、商品金额等
     * @param orderProductPOMap      原订单商品信息
     * @param updateOrderProductList 更新订单商品信息：最新分摊服务费放入该对象中
     */
    private void orderProductFeeAllocation(OrderPO orderPO, OrderPO updateOrder,
                                           Map<Long, OrderProductPO> orderProductPOMap, List<OrderProductPO> updateOrderProductList) {
        // BigDecimal fixedServiceFee = BigDecimal.ZERO; // 商品行服务费累计值
        BigDecimal fixedThirdpartnarFee = BigDecimal.ZERO; // 商品行代运营费累计值
        for (int i = 0, s = updateOrderProductList.size(); i < s; i++) {
            OrderProductPO updateOrderProduct = updateOrderProductList.get(i);
            OrderProductPO orderProductPO = orderProductPOMap.get(updateOrderProduct.getOrderProductId());

            // 只有一行商品
            if (s == 1) {
                // updateOrderProduct.setServiceFee(updateOrder.getServiceFee());
                updateOrderProduct.setThirdpartnarFee(updateOrder.getThirdpartnarFee());
            } else if (i == s - 1) { // 最后一行商品 钆差
                // updateOrderProduct.setServiceFee(updateOrder.getServiceFee().subtract(fixedServiceFee));
                updateOrderProduct.setThirdpartnarFee(updateOrder.getThirdpartnarFee().subtract(fixedThirdpartnarFee));
            } else {
                BigDecimal productPrice = updateOrderProduct.getGoodsAmountTotal()
                        .subtract(orderProductPO.getStoreActivityAmount()).subtract(orderProductPO.getStoreVoucherAmount());

                BigDecimal orderPrice = updateOrder.getGoodsAmount().subtract(orderPO.getStoreActivityAmount())
                        .subtract(orderPO.getStoreVoucherAmount());

                // BigDecimal productServiceFee = BigDecimal.ZERO;
                BigDecimal productThirdpartnarFee = BigDecimal.ZERO;
                if (orderPrice.compareTo(BigDecimal.ZERO) > 0) {
                    // productServiceFee = productPrice.multiply(updateOrder.getServiceFee())
                    // .divide(orderPrice, 2, RoundingMode.HALF_UP);

                    productThirdpartnarFee = productPrice.multiply(updateOrder.getThirdpartnarFee()).divide(orderPrice,
                            2, RoundingMode.HALF_UP);
                }

                // updateOrderProduct.setServiceFee(productServiceFee);
                updateOrderProduct.setThirdpartnarFee(productThirdpartnarFee);

                // fixedServiceFee = fixedServiceFee.add(productServiceFee);
                fixedThirdpartnarFee = fixedThirdpartnarFee.add(productThirdpartnarFee);
            }
        }
    }

    private OrderPO getUpdateOrderPO(OrderPO orderPO, OrderAmountDP orderAmountDP) {
        OrderPO updateOrder = new OrderPO();
        updateOrder.setExpressFee(orderAmountDP.getExpressFee());
        updateOrder.setExpressFeeTotal(orderAmountDP.getExpressFeeTotal());
        updateOrder.setGoodsAmount(orderAmountDP.getGoodsAmount());
        updateOrder.setOrderAmount(orderAmountDP.getOrderAmount());
        updateOrder.setOrderAmountTotal(orderAmountDP.getOrderAmountTotal());
        // 计算订单差额，修改订单实付 - 原订单实付
        BigDecimal marginOrderAmount = orderAmountDP.getOrderAmount().subtract(orderPO.getOrderAmount());
        updateOrder.setMarginOrderAmount(orderPO.getMarginOrderAmount().add(marginOrderAmount));
        // 服务费 平台服务费支付后计算
        // updateOrder.setServiceFee(orderAmountDP.getServiceFee());
        updateOrder.setThirdpartnarFee(orderAmountDP.getThirdpartnarFee());
        return updateOrder;
    }

    private OrderProductPO getUpdateOrderProductPO(OrderProductAmountDP orderProductAmountDP, String batchNo, String purchaseSubCode) {
        OrderProductPO updateOrderProduct = new OrderProductPO();
        updateOrderProduct.setOrderProductId(orderProductAmountDP.getOrderProductId());
        updateOrderProduct.setProductShowPrice(orderProductAmountDP.getProductShowPrice());
        updateOrderProduct.setTaxPrice(orderProductAmountDP.getProductShowPrice());
        updateOrderProduct.setLandingPrice(orderProductAmountDP.getLandingPrice());
        updateOrderProduct.setGoodsAmountTotal(orderProductAmountDP.getGoodsAmountTotal());
        updateOrderProduct.setMoneyAmount(orderProductAmountDP.getMoneyAmount());
        // 服务费 平台服务费支付后计算
        // updateOrderProduct.setServiceFee(orderProductAmountDP.getServiceFee());
        updateOrderProduct.setThirdpartnarFee(orderProductAmountDP.getThirdpartnarFee());
        updateOrderProduct.setProductEffectivePrice(orderProductAmountDP.getProductEffectivePrice());
        if (StringUtils.isNotBlank(batchNo)) {
            updateOrderProduct.setBatchNo(batchNo);
        }
        if (StringUtils.isNotBlank(purchaseSubCode)) {
            updateOrderProduct.setPurchaseSubCode(purchaseSubCode);
        }
        return updateOrderProduct;
    }

    private OrderPriceRecordPO getSaveOrderPriceRecordPO(OrderProductPO orderProductPO,
                                                         OrderProductAmountDP orderProductAmountDP, BigDecimal expressFee, BigDecimal renewalExpressFee,
                                                         String operatorName) {
        OrderPriceRecordPO orderPriceRecordPO = new OrderPriceRecordPO();
        orderPriceRecordPO.setOrderSn(orderProductPO.getOrderSn());
        orderPriceRecordPO.setOrderProductId(orderProductPO.getOrderProductId());
        orderPriceRecordPO.setProductShowPrice(orderProductPO.getProductShowPrice());
        orderPriceRecordPO.setRenewalProductShowPrice(orderProductAmountDP.getProductShowPrice());
        orderPriceRecordPO.setLandingPrice(orderProductPO.getLandingPrice());
        orderPriceRecordPO.setRenewalLandingPrice(orderProductAmountDP.getLandingPrice());
        orderPriceRecordPO.setExpressFee(expressFee);
        orderPriceRecordPO.setRenewalExpressFee(renewalExpressFee);
        orderPriceRecordPO.setCreateBy(operatorName);
        orderPriceRecordPO.setUpdateBy(operatorName);
        return orderPriceRecordPO;
    }

    /**
     * 订单改价校验
     *
     * @param order 订单
     * @return 校验结果
     */
    private String orderRenewalPriceVerify(OrderPO order) {
        if (Objects.isNull(order)) {
            return "订单不存在，请重试";
        }
        if (ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2 == order.getExchangeFlag()) {
            return "换货后的订单不允许进行改价";
        }
        if (!OrderStatusEnum.WAIT_PAY.getValue().equals(order.getOrderState())) {
            return "该订单状态不允许进行改价";
        }
        if (PayMethodEnum.ONLINE != PayMethodEnum.getValue(order.getPaymentCode())) {
            return "该订单已发起过支付，不允许进行改价";
        }
        // 调用支付查询是否已支付
        if (payIntegration.queryPayStatus(order.getPaySn())) {
            return "该订单已发起过支付，不允许进行改价";
        }
        if (OrderCreateChannel.OMS.getValue().equals(order.getChannel())) {
            return "OMS来源订单不允许进行改价";
        }
        if (!OrderTypeEnum.NORMAL.getValue().equals(order.getOrderType())) {
            OrderTypeEnum orderTypeEnum = OrderTypeEnum.valueOf(order.getOrderType());
            AssertUtil.notNull(orderTypeEnum, new BusinessException("未知的订单类型"));
            return orderTypeEnum.getDesc() + "不允许进行改价";
        }
        if (order.getPerformanceModes()
                .contains(OrderPerformanceModeEnum.PERFORMANCE_MODE_SUPPLIER.getValue().toString())) {
            return "供应商履约模式订单不允许改价";
        }

        return OrderConst.CHECK_SUCCESS;
    }

    @Override
    public Boolean orderInfoModify(OrderInfoModifyReq orderInfoModifyReq, Vendor vendor) {
        // 订单校验
        OrderPO orderPO = this.getByOrderSn(orderInfoModifyReq.getOrderSn());
        BizAssertUtil.notNull(orderPO, "订单不存在");
        OrderExtendPO orderExtendByOrderSn =
                orderExtendService.getOrderExtendByOrderSn(orderInfoModifyReq.getOrderSn());
        BizAssertUtil.notNull(orderExtendByOrderSn, "订单扩展信息不存在");

        StringBuilder remark = new StringBuilder();
        String content;
        OrderExtendPO updateOrderExtend = new OrderExtendPO();
        updateOrderExtend.setExtendId(orderExtendByOrderSn.getExtendId());

        if (StringUtils.isNotEmpty(orderInfoModifyReq.getRemark())) {
            updateOrderExtend.setOrderRemark(orderInfoModifyReq.getRemark());

            content = "修改订单备注";
            remark.append("原备注信息：").append(orderExtendByOrderSn.getOrderRemark());
        } else {
            updateOrderExtend.setReceiverProvinceCode(orderInfoModifyReq.getProvince());
            updateOrderExtend.setReceiverCityCode(orderInfoModifyReq.getCity());
            updateOrderExtend.setReceiverDistrictCode(orderInfoModifyReq.getDistrict());
            updateOrderExtend.setReceiverTownCode(orderInfoModifyReq.getTown());
            updateOrderExtend.setReceiverAreaInfo(
                    orderInfoModifyReq.getProvince() + orderInfoModifyReq.getCity() + orderInfoModifyReq.getDistrict());
            updateOrderExtend.setReceiverMobile(orderInfoModifyReq.getReceiverMobile());
            updateOrderExtend.setReceiverName(orderInfoModifyReq.getReceiverName());
            updateOrderExtend.setReceiverInfo(
                    orderInfoModifyReq.getProvince() + orderInfoModifyReq.getCity() + orderInfoModifyReq.getDistrict()
                            + orderInfoModifyReq.getTown() + orderInfoModifyReq.getDetailAddress());
            updateOrderExtend.setReceiverAddress(orderInfoModifyReq.getDetailAddress());

            content = "修改收货信息";
            remark.append("原收货人姓名：").append(orderExtendByOrderSn.getReceiverName())
                    .append("原收货地址信息：").append(orderExtendByOrderSn.getReceiverInfo());
        }

        // 备注、收货地址修改
        orderExtendService.updateById(updateOrderExtend);

        // 写日志
        CompletableFuture.runAsync(() -> {
            orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_VENDOR, vendor.getVendorId(), vendor.getVendorName(),
                    orderPO.getOrderSn(), orderPO.getOrderState(), orderPO.getOrderState(), orderPO.getLoanPayState(),
                    content, OrderCreateChannel.WEB, remark.toString());
        });

        return Boolean.TRUE;
    }

    @Override
    public Result<Boolean> checkOrderDeliveryMaterial(OrderPO orderPO) {
        Result<Boolean> result = new Result<>();
        SceneTypeEnum sceneTypeEnum = this.getOrderFileSceneNo(orderPO, OrderProductDeliveryEnum.WAIT_DELIVERY);
        // 订单无文件资料场景编号，说明该订单不需要上传任何文件资料
        if (Objects.isNull(sceneTypeEnum)) {
            result.setSuccess(true);
            result.setData(Boolean.TRUE);
            return result;
        }
        // 农机厂商发货资料检查
        if (SceneTypeEnum.MFR_ORDER_DELIVER == sceneTypeEnum) {
            FileScenesWithMaterialVO materialVO = fileCenterIntegration.queryScenesProofMaterial(orderPO.getOrderSn(),
                    IScenesMaterialProofConstant.ProofTypeEnum.MALL_ORDER_ORDER_SN, sceneTypeEnum);
            result.setSuccess(true);
            result.setData(Boolean.TRUE);
            return result;
        }
        // 现款现货发货资料检查
        if (SceneTypeEnum.FUNDS_BORROW_DELIVERY == sceneTypeEnum
                || SceneTypeEnum.FUNDS_BORROW_AND_MFR_ORDER_DELIVERY == sceneTypeEnum) {
            Result<Void> verifyResult = fundsBorrowBizService.verifyOrderDelivery(orderPO, sceneTypeEnum);
            if (!verifyResult.isSuccess()) {
                result.setSuccess(false);
                result.addError(verifyResult.getErrorCode(), verifyResult.getErrorMsg(), "");
                return result;
            }
        }
        result.setSuccess(true);
        result.setData(Boolean.TRUE);
        return result;
    }

    @Override
    public Result<Boolean> checkOrderReceiveMaterial(OrderPO orderPO) {
        Result<Boolean> result = new Result<>();
        SceneTypeEnum sceneTypeEnum = this.getOrderFileSceneNo(orderPO, OrderProductDeliveryEnum.DELIVERED);
        // 订单无文件资料场景编号，说明该订单不需要上传任何文件资料
        if (Objects.isNull(sceneTypeEnum)) {
            result.setSuccess(true);
            result.setData(Boolean.TRUE);
            return result;
        }
        // 农机厂商签收资料检查
        if (SceneTypeEnum.MFR_ORDER_RECEIVE == sceneTypeEnum) {
            FileScenesWithMaterialVO materialVO = fileCenterIntegration.queryScenesProofMaterial(orderPO.getOrderSn(),
                    IScenesMaterialProofConstant.ProofTypeEnum.MALL_ORDER_ORDER_SN, sceneTypeEnum);
            result.setSuccess(true);
            result.setData(Boolean.TRUE);
            return result;
        }
        // 现款现货签收资料检查
        if (SceneTypeEnum.FUNDS_BORROW_RECEIVE == sceneTypeEnum
                || SceneTypeEnum.FUNDS_BORROW_AND_MFR_ORDER_RECEIVE == sceneTypeEnum) {
            Result<Void> verifyResult = fundsBorrowBizService.verifyOrderReceive(orderPO, sceneTypeEnum);
            if (!verifyResult.isSuccess()) {
                result.setSuccess(false);
                result.addError(verifyResult.getErrorCode(), verifyResult.getErrorMsg(), "");
                return result;
            }
        }
        result.setSuccess(true);
        result.setData(Boolean.TRUE);
        return result;
    }

    @Override
    public SceneTypeEnum getOrderFileSceneNo(OrderPO orderPO, OrderProductDeliveryEnum deliveryEnum) {
        if (Objects.isNull(orderPO) || Objects.isNull(orderPO.getOrderState())
                || Objects.isNull(orderPO.getPerformanceModes()) || Objects.isNull(orderPO.getPaymentCode())) {
            throw new BusinessException("获取订单对应的场景编号列表入参为空");
        }
        OrderStatusEnum orderStatusEnum = OrderStatusEnum.valueOf(orderPO.getOrderState());
        if (Objects.isNull(orderStatusEnum) || OrderStatusEnum.UN_KNOW == orderStatusEnum) {
            throw new BusinessException(String.format("未知的订单状态,orderSn:%s", orderPO.getOrderSn()));
        }
        // 是否需要发货场景编号
        boolean deliverScene = OrderProductDeliveryEnum.WAIT_DELIVERY == deliveryEnum
                || (Objects.isNull(deliveryEnum) && OrderStatusEnum.WAIT_DELIVER == orderStatusEnum);
        // 是否需要签收场景编号
        boolean receiveScene = OrderProductDeliveryEnum.DELIVERED == deliveryEnum
                || (Objects.isNull(deliveryEnum) && OrderStatusEnum.WAIT_RECEIPT == orderStatusEnum);
        // 是否是现款现货模式
        boolean fundsBorrowFlag = SettleModeEnum.BORROW.getCode().equals(orderPO.getSettleMode());
        // 是否是农机厂商模式
        boolean mfrFlag = OrderPerformanceModeEnum.isContain(orderPO.getPerformanceModes(),
                OrderPerformanceModeEnum.PERFORMANCE_MODE_MFR_DEALER);
        // 现款现货 && 农机厂商的模式
        if (fundsBorrowFlag && mfrFlag) {
            if (deliverScene) {
                return SceneTypeEnum.FUNDS_BORROW_AND_MFR_ORDER_DELIVERY;
            } else if (receiveScene) {
                return SceneTypeEnum.FUNDS_BORROW_AND_MFR_ORDER_RECEIVE;
            }
        }
        // 仅有现款现货模式
        if (fundsBorrowFlag) {
            if (deliverScene) {
                return SceneTypeEnum.FUNDS_BORROW_DELIVERY;
            } else if (receiveScene) {
                return SceneTypeEnum.FUNDS_BORROW_RECEIVE;
            }
        }
        // 仅有农机厂商模式
        if (mfrFlag) {
            if (deliverScene) {
                return SceneTypeEnum.MFR_ORDER_DELIVER;
            } else if (receiveScene) {
                return SceneTypeEnum.MFR_ORDER_RECEIVE;
            }
        }
        // 非现款现货 && 非农机厂商 && 贷款类支付订单
        if (PayMethodEnum.isLoanPay(orderPO.getPaymentCode()) && !fundsBorrowFlag && !mfrFlag) {
            if (receiveScene) {
                return SceneTypeEnum.LOAN_ORDER_RECEIVE;
            }
        }
        return null;
    }

    @Override
    public void tradingRiskWareNing(OrderPO orderPO) {
        //只处理支付完成的订单
        if (ObjectUtil.isNull(orderPO) || orderPO.getOrderState() != 20) {
            return;
        }
        log.info("【店铺风控预警】: 接收到支付完成订单{}", JSONObject.toJSONString(orderPO));
        //只处理贷款类支付
        List<String> paymentCodeList = new ArrayList<String>() {
            {
                this.add("ENJOY_PAY");
                this.add("FOLLOW_HEART");
                this.add("CREDIT_PAY");
            }
        };
        if (!paymentCodeList.contains(orderPO.getPaymentCode())) {
            return;
        }
        //只处理非自营店铺
        if (orderPO.getStoreIsSelf() != 2) {
            return;
        }

        OrderExtendPO orderExtendPO = orderExtendService.getOrderExtendByOrderSn(orderPO.getOrderSn());
        if (ObjectUtil.isNull(orderExtendPO) || StringUtils.isEmpty(orderExtendPO.getManagerName())) {
            return;
        }
        //分支自己的单不处理
        if (orderExtendPO.getManageType() == 3) {
            return;
        }
        // TODO 抽象化流程
        List<String> excludeCategory = riskWarningConfig.getExcludeCategory();
        log.info("【店铺风控预警】需要排除的分类:{}", excludeCategory);
        //客户经理名下所有客户近30天内产生金融支付交易≥5笔
        List<RiskWarningDto> orderExtendList = orderMapper.tradingRiskWareNing(num5, days30, orderExtendPO.getManager(), excludeCategory);
        log.info("【店铺风控预警】:查询到【客户经理{}名下所有客户近30天内产生金融支付交易≥5笔】总数：{}", orderExtendPO.getManager(), orderExtendList.size());
        if (CollectionUtils.isNotEmpty(orderExtendList)) {
            for (RiskWarningDto riskWarningDto : orderExtendList) {
                try {
                    HashMap<String, String> paramMap = buildDingTalkMessageParamMap(riskWarningDto);
                    paramMap.put("day", days30.toString());
                    // 1、电商区域督导
                    //2、BD - 没有bd不发
                    //3、固定人员：罗冠玉，王芳，刘美希（去掉：谭程，乌力吉木乐）
                    List<String> storeAuditorList = getStoreAuditorList(riskWarningDto);
                    List<String> bdReceiver = getBdReceiver(riskWarningDto);
                    List<String> fixedReceiver = getFixedReceiver(OrderConst.RISK_WARNING);
                    if (!CollectionUtils.isEmpty(bdReceiver)) {
                        // 没有bd不发送钉钉消息
                        List<String> receiverList = Lists.newArrayList();
                        receiverList.addAll(storeAuditorList);
                        receiverList.addAll(bdReceiver);
                        receiverList.addAll(fixedReceiver);
                        sendDingTalkMessageV2(OrderConst.RISK_WARNING, paramMap, receiverList);
                    } else {
                        log.warn("发送钉钉消息失败，没有配置bd，dto:{}", riskWarningDto);
                    }
                } catch (Exception e) {
                    log.error("【店铺风控预警】:查询到【客户经理名下所有客户近30天内产生金融支付交易≥5笔】发送出现异常,param:{}", riskWarningDto);
                }
            }
        }

        //客户经理名下所有客户近90天内在同一家店铺产生金融支付交易≥5笔
        List<RiskWarningDto> byStoreIdList = orderMapper.tradingRiskByStoreId(num5, days90, orderExtendPO.getManager(), excludeCategory);
        log.info("【店铺风控预警】:查询到【客户经理{}名下所有客户近90天内在同一家店铺产生金融支付交易≥5笔】总数：{}", orderExtendPO.getManager(), byStoreIdList.size());
        if (CollectionUtils.isNotEmpty(byStoreIdList)) {
            for (RiskWarningDto riskWarningDto : byStoreIdList) {
                try {
                    HashMap<String, String> paramMap = buildDingTalkMessageParamMap(riskWarningDto);
                    paramMap.put("day", days90.toString());
                    // 1、电商区域督导
                    //2、BD - 没有bd不发
                    //3、固定人员：罗冠玉，王芳，刘美希（去掉：谭程，乌力吉木乐）
                    List<String> storeAuditorList = getStoreAuditorList(riskWarningDto);
                    List<String> bdReceiver = getBdReceiver(riskWarningDto);
                    List<String> fixedReceiver = getFixedReceiver(OrderConst.RISK_WARNING);
                    if (!CollectionUtils.isEmpty(bdReceiver)) {
                        // 没有bd不发送钉钉消息
                        List<String> receiverList = Lists.newArrayList();
                        receiverList.addAll(storeAuditorList);
                        receiverList.addAll(bdReceiver);
                        receiverList.addAll(fixedReceiver);
                        sendDingTalkMessageV2(OrderConst.RISK_WARNING, paramMap, receiverList);
                    } else {
                        log.warn("发送钉钉消息失败，没有配置bd，dto:{}", riskWarningDto);
                    }
//                    sendDingTalkMessage(riskWarningDto,OrderConst.RISK_WARNING,paramMap,true);
                } catch (Exception e) {
                    log.error("【店铺风控预警】:查询到【客户经理名下所有客户近90天内在同一家店铺产生金融支付交易≥5笔】发送出现异常,param:{}", riskWarningDto);
                }
            }
        }
        //客户经理名下所有客户近30天内在同一家店铺产生金融支付交易≥3笔
        List<RiskWarningDto> byStoreId2List = orderMapper.tradingRiskByStoreIdTwo(num3, days30, orderExtendPO.getManager(), excludeCategory);
        log.info("【店铺风控预警】:查询到【客户经理{}名下所有客户近30天内在同一家店铺产生金融支付交易≥3笔总数：{}", orderExtendPO.getManager(), byStoreId2List.size());
        if (CollectionUtils.isNotEmpty(byStoreId2List)) {
            for (RiskWarningDto riskWarningDto : byStoreId2List) {
                try {
                    HashMap<String, String> paramMap = buildDingTalkMessageParamMap(riskWarningDto);
                    paramMap.put("day", days30.toString());
                    // 1、电商区域督导
                    //2、BD
                    //、固定人员：罗冠玉，王芳，刘美希（去掉：谭程，乌力吉木乐）
                    List<String> storeAuditorList = getStoreAuditorList(riskWarningDto);
                    List<String> bdReceiver = getBdReceiver(riskWarningDto);
                    List<String> fixedReceiver = getFixedReceiver(OrderConst.RISK_WARNING3);
                    if (!CollectionUtils.isEmpty(bdReceiver)) {
                        // 没有bd不发送钉钉消息
                        List<String> receiverList = Lists.newArrayList();
                        receiverList.addAll(storeAuditorList);
                        receiverList.addAll(bdReceiver);
                        receiverList.addAll(fixedReceiver);
                        sendDingTalkMessageV2(OrderConst.RISK_WARNING3, paramMap, receiverList);
                    } else {
                        log.warn("发送钉钉消息失败，没有配置bd，dto:{}", riskWarningDto);
                    }
                } catch (Exception e) {
                    log.error("【店铺风控预警】:查询到【客户经理名下所有客户近30天内在同一家店铺产生金融支付交易≥3笔 发送出现异常,param:{}", riskWarningDto);
                }
            }
        }

        Integer sameStoreDays = riskWarningConfig.getSameStoreOverDays();
        Integer sameStoreNum = riskWarningConfig.getSameStoreOverNum();
        List<RiskWarningDto> sameStoreList = orderMapper.tradingRiskWarningSameStore(sameStoreNum, sameStoreDays, orderPO.getMemberId(), orderPO.getStoreId());
        log.info("【店铺风控预警】:查询到【同一个客户在同一个店铺90天内大于等于2笔（客户ID）】：{},总数:{}", orderPO.getMemberId(), sameStoreList.size());
        if (CollectionUtils.isNotEmpty(sameStoreList)) {
            for (RiskWarningDto riskWarningDto : sameStoreList) {
                try {
                    HashMap<String, String> paramMap = buildDingTalkMessageParamMap(riskWarningDto);
                    paramMap.put("day", sameStoreDays.toString());
                    // 刘美希，谭程；
                    List<String> fixedReceiver = getFixedReceiver(OrderConst.RISK_WARNING_SAME_STORE);
                    sendDingTalkMessageV2(OrderConst.RISK_WARNING_SAME_STORE, paramMap, fixedReceiver);
                } catch (Exception e) {
                    log.error("【店铺风控预警】:查询到【同一个客户在同一个店铺90天内大于等于2笔（客户ID）】发送出现异常,param:{}", riskWarningDto);

                }
            }
        }
    }

    /**
     * 获取db接收人
     *
     * @param riskWarningDto 告警dto
     * @return 接收人
     */
    private List<String> getBdReceiver(RiskWarningDto riskWarningDto) {
        Map<String, String> userMap = null;
        List<String> receiverList = Lists.newArrayList();
        try {
            userMap = storeAreaCodeConfig.getSupervisorMap();
        } catch (Exception e) {
            e.printStackTrace();
            log.info("【店铺风控预警】获取接收人配置失败{}", e.getMessage());
        }
        log.info("【店铺风控预警】构建DB接收人消息");
        String supervisor = userMap.get(riskWarningDto.getAreaCode());
        if (StringUtils.isEmpty(supervisor)) {
            return receiverList;
        }
        if (StringUtils.isNotBlank(supervisor)) {
            String[] strings = supervisor.split(",");
            receiverList.addAll(Arrays.asList(strings));
        }
        return receiverList;
    }

    /**
     * 获取固定接收人
     *
     * @return 接收人
     */
    private List<String> getFixedReceiver(String ruleCode) {
        List<String> receiverList = Lists.newArrayList();
        try {
            List<String> defaultReciverList = riskWarningConfig.getReceiverList();
            Map<String, List<String>> ruleReceiverList = riskWarningConfig.getRuleReceiverList();
            if (null != ruleReceiverList && ruleReceiverList.containsKey(ruleCode)) {
                receiverList = ruleReceiverList.get(ruleCode);
            } else {
                receiverList = defaultReciverList;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("【店铺风控预警】获取接收人配置失败{}", e.getMessage());
        }
        log.info("获取固定接收人:{}", receiverList);
        return receiverList;
    }

    /**
     * 店铺督导消息接收人
     *
     * @param riskWarningDto 风控实体
     * @return 接收人
     */
    public List<String> getStoreAuditorList(RiskWarningDto riskWarningDto) {
        log.info("【店铺风控预警】构建乡助严选配置督导消息");
        List<String> receiverList = Lists.newArrayList();
        JsonResult<List<StoreAuditorVO>> result = storeAuditorFeignClient.getStoreAuditorList(new StoreAuditorListReq());
        if (result.getState().equals(200) && CollectionUtils.isNotEmpty(result.getData())) {
            List<StoreAuditorVO> storeAuditorVOS = result.getData();
            for (StoreAuditorVO storeAuditorVO : storeAuditorVOS) {
                if (storeAuditorVO.getRecommendStoreName().equals("乡助严选") && StringUtils.isNotBlank(storeAuditorVO.getRegionCode())
                        && storeAuditorVO.getRegionCode().equals(riskWarningDto.getAreaCode())) {
                    receiverList.add(storeAuditorVO.getEmployeeNumber());
                }
            }
        }
        return receiverList;
    }

    /**
     * 封装钉钉消息参数map
     *
     * @param riskWarningDto 订单拓展实体
     * @return 参数map
     */
    private static HashMap<String, String> buildDingTalkMessageParamMap(RiskWarningDto riskWarningDto) {
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("managerName", riskWarningDto.getManagerName());//客户经理名称
        paramMap.put("manager", riskWarningDto.getManager());//客户经理工号
        paramMap.put("date", DateUtil.getNowTime());//当前时间
        paramMap.put("branch", riskWarningDto.getBranchName());//分支
        paramMap.put("total", riskWarningDto.getCount().toString());
        paramMap.put("storeId", riskWarningDto.getStoreId().toString());
        paramMap.put("storeName", riskWarningDto.getStoreName());
        paramMap.put("serialNumber", String.valueOf(System.currentTimeMillis()));
        paramMap.put("memberId", riskWarningDto.getMemberId().toString());
        paramMap.put("customerName", riskWarningDto.getCustomerName());
        return paramMap;
    }

    /**
     * 发送钉钉消息
     *
     * @param riskWarningDto 风控预警实体
     * @param bizType        业务类型
     * @param paramMap       消息模板参数
     * @param mustContainsBd 是否必须包含db人员
     */
    private void sendDingTalkMessage(RiskWarningDto riskWarningDto, String bizType, HashMap<String, String> paramMap, boolean mustContainsBd) {
        Map<String, String> userMap = null;
        List<String> receiverList = null;
        try {
            userMap = storeAreaCodeConfig.getSupervisorMap();
            receiverList = riskWarningConfig.getReceiverList();
        } catch (Exception e) {
            e.printStackTrace();
            log.info("【店铺风控预警】获取接收人配置失败{}", e.getMessage());
        }
        log.info("【店铺风控预警】DB配置人员{}", userMap.entrySet());
        log.info("【店铺风控预警】固定配置人员{}", receiverList);
        try {
            log.info("【店铺风控预警】serialNumber:{}", paramMap.get("serialNumber"));
            //发送预警消息
            BatchBizMessageTemplateReq req = new BatchBizMessageTemplateReq();
            req.setSerialNumber(paramMap.get("serialNumber"));
            req.setBizType(bizType);
            req.setReceiverTypeEnum(ReceiverTypeEnum.EMPLOYEE);

            List<BatchBizMessageSubReq> bizMessageTemplateList = new ArrayList<>();

            log.info("【店铺风控预警】构建乡助严选配置督导消息");
            JsonResult<List<StoreAuditorVO>> result = storeAuditorFeignClient.getStoreAuditorList(new StoreAuditorListReq());
            if (result.getState().equals(200) && CollectionUtils.isNotEmpty(result.getData())) {
                List<StoreAuditorVO> storeAuditorVOS = result.getData();
                for (StoreAuditorVO storeAuditorVO : storeAuditorVOS) {
                    if (storeAuditorVO.getRecommendStoreName().equals("乡助严选") && StringUtils.isNotBlank(storeAuditorVO.getRegionCode())
                            && storeAuditorVO.getRegionCode().equals(riskWarningDto.getAreaCode())) {
                        BatchBizMessageSubReq subReq = new BatchBizMessageSubReq();
                        subReq.setReceiver(storeAuditorVO.getEmployeeNumber());
                        subReq.setBizParams(paramMap);
                        bizMessageTemplateList.add(subReq);
                    }
                }
            }


            //固定接收人
            log.info("【店铺风控预警】构建固定接收人消息");
            for (String receiver : receiverList) {
                BatchBizMessageSubReq subReq2 = new BatchBizMessageSubReq();
                subReq2.setReceiver(receiver);//接收人工号-固定人员 刘美希
                subReq2.setBizParams(paramMap);
                bizMessageTemplateList.add(subReq2);
            }

            log.info("【店铺风控预警】构建DB接收人消息");
            String supervisor = userMap.get(riskWarningDto.getAreaCode());
            if (mustContainsBd && StringUtils.isEmpty(supervisor)) {
                return;
            }
            if (StringUtils.isNotBlank(supervisor)) {
                String[] strings = supervisor.split(",");
                for (String s : strings) {
                    BatchBizMessageSubReq subReq3 = new BatchBizMessageSubReq();
                    subReq3.setReceiver(s);
                    subReq3.setBizParams(paramMap);
                    bizMessageTemplateList.add(subReq3);
                }
            }

            req.setBizMessageTemplateList(bizMessageTemplateList);
            log.info("【店铺风控预警】发送钉钉消息入参{}", JSONObject.toJSONString(req));
            Result<Void> voidResult = templateMessageFacade.bizTemplateBatchSend(req);
            log.info("【店铺风控预警】发送钉钉消息出参{}", JSONObject.toJSONString(voidResult));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("【店铺风控预警】发送钉钉消息失败{}", e);
        }
    }

    /**
     * 发送钉钉消息
     *
     * @param bizType  业务类型
     * @param paramMap 消息模板参数
     */
    private void sendDingTalkMessageV2(String bizType, HashMap<String, String> paramMap, List<String> receiverList) {
        try {
            log.warn("【店铺风控预警】接收人：{}", JSONObject.toJSONString(receiverList));
            log.info("【店铺风控预警】serialNumber:{}", paramMap.get("serialNumber"));
            //发送预警消息
            BatchBizMessageTemplateReq req = new BatchBizMessageTemplateReq();
            req.setSerialNumber(paramMap.get("serialNumber"));
            req.setBizType(bizType);
            req.setReceiverTypeEnum(ReceiverTypeEnum.EMPLOYEE);

            List<BatchBizMessageSubReq> bizMessageTemplateList = new ArrayList<>();
            for (String receiver : receiverList) {
                BatchBizMessageSubReq subReq = new BatchBizMessageSubReq();
                subReq.setReceiver(receiver);
                subReq.setBizParams(paramMap);
                bizMessageTemplateList.add(subReq);
            }

            req.setBizMessageTemplateList(bizMessageTemplateList);
            log.info("【店铺风控预警】发送钉钉消息入参{}", JSONObject.toJSONString(req));
            Result<Void> voidResult = templateMessageFacade.bizTemplateBatchSend(req);
            log.info("【店铺风控预警】发送钉钉消息出参{}", JSONObject.toJSONString(voidResult));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("【店铺风控预警】发送钉钉消息失败{}", e);
        }
    }

    @Override
    public Boolean checkProductIsSelf(List<Long> productIds) {
        //存在商品开启地区价格，则不能修改地址
        List<ProductExtend> productExtendList = productExtendFeignClient.getProductExtendListByProductId(productIds);
        if (CollectionUtils.isEmpty(productExtendList)) {
            return false;
        }
        Map<Long, List<ProductExtend>> map = productExtendList.stream().collect(Collectors.groupingBy(ProductExtend::getProductId));
        for (Long productId : productIds) {
            List<ProductExtend> productExtends = map.get(productId);
            if (CollectionUtil.isEmpty(productExtends)) {
                return false;
            }
            if (ObjectUtil.isNull(productExtends.get(0).getIsSelfLift())) {
                return false;
            }
            if (productExtends.get(0).getIsSelfLift() == 0) {
                return false;
            }
        }
        return true;
    }

    @Transactional(rollbackFor = Throwable.class, transactionManager = "masterdbTx")
    public List<String> submitOrderWithLocalTransaction(OrderSubmitDTO orderSubmitDTO, Member member, OrderSubmitMqConsumerDTO consumerDTO) {
        // 保存全局事务记录
        OrderSubmitTransactionDTO submitTransactionDTO = new OrderSubmitTransactionDTO();
        submitTransactionDTO.setOrderSubmitDTO(orderSubmitDTO);
        submitTransactionDTO.setMember(member);
        submitTransactionDTO.setConsumerDTO(consumerDTO);
        TransactionLogPO logPO = transactionLogService.generateNewTransactionLogPO(TransactionLogTypeEnum.ORDER_SUBMIT.getCode(), TransactionRoleEnum.ORIGINATOR.getCode());
        // 全部订单编号列表，包含父单号，不能直接返回，返回数据中只能包含子单号
        List<String> totalOrderSnList = Lists.newArrayList();
        try {
            // 根据事务id加锁，保证同一事务同一时间同一服务只存在一个线程在处理这一个事务，保证不会并行处理同一事务
            return distributeLock.lockAndProcess(CommonConst.PREFIX_TRANSACTION_KEY + logPO.getTransactionId(), 10, 5, TimeUnit.MINUTES, () -> {
                List<String> result = orderModel.submitOrder(orderSubmitDTO, member, consumerDTO, totalOrderSnList);
                transactionLogService.updateTransactionStatusById(logPO.getTransactionId(), TransactionStatusEnum.READY.getCode(), TransactionStatusEnum.COMMITTED.getCode());
                return result;
            });
        } catch (Exception e) {
            // 提交失败，发送mq消息
            if (null != logPO) {
                // 自身事务记录不进行回滚，自己监听，在监听里面去更新
                log.warn("提交订单失败，发送mq消息,param:{},memberId:{}", orderSubmitDTO, member.getMemberId());
                orderCreateHelper.addTransactionLogEvent(logPO.getTransactionId(), TransactionLogTypeEnum.ORDER_SUBMIT, totalOrderSnList);
            }
            throw e;
        } finally {
            if (null != logPO) {
                // 保存关键参数
                TransactionRollbackDTO rollbackDTO = new TransactionRollbackDTO();
                rollbackDTO.setTransactionId(logPO.getTransactionId());
                rollbackDTO.setLogType(TransactionLogTypeEnum.ORDER_SUBMIT.getCode());
                rollbackDTO.setOrderSnList(totalOrderSnList);
                transactionLogService.updateRequestParam(logPO.getId(), JSON.toJSONString(rollbackDTO));
            }
        }
    }

    /**
     * 下单，使用全局事务
     *
     * @param orderSubmitDTO 计算优惠后的提交订单信息
     * @param member         会员信息
     * @param consumerDTO    前端提交订单参数
     * @return
     */
    @GlobalTransactional(rollbackFor = Throwable.class)
    public List<String> submitOrderWithGlobleTransaction(OrderSubmitDTO orderSubmitDTO, Member member, OrderSubmitMqConsumerDTO consumerDTO) {
        return orderModel.submitOrder(orderSubmitDTO, member, consumerDTO, Lists.newArrayList());
    }

    /**
     * 获取订单信息
     * @param orderForThemeDTO orderForThemeDTO
     * @return List<OrderInfoForThemeVO>
     */
    @Override
    public PageVO<OrderInfoForThemeVO> getOrderFilterBriefInfoForThemeActivity(OrderForThemeDTO orderForThemeDTO) {
        //获取指定店铺、指定分支、指定时间的订单
        PagerInfo pagerInfo = new PagerInfo(orderForThemeDTO.getPageSize(), orderForThemeDTO.getPageIndex());
        PageVO<OrderInfoForThemeVO> pageVO = new PageVO<>();
        List<OrderInfoForThemeVO> orderInfoForThemeVOList = orderMapper.getOrderFilterBriefByBranch(orderForThemeDTO,pagerInfo.getStart(),pagerInfo.getPageSize());
        if (CollectionUtils.isEmpty(orderInfoForThemeVOList)){
            return pageVO;
        }
        Integer total = orderMapper.countOrderFilterBriefByBranch(orderForThemeDTO, pagerInfo.getStart(), pagerInfo.getPageSize());
        pagerInfo.setRowsCount(total);
        List<OrderInfoForThemeVO> orderInfoForThemeVOListNew = Lists.newArrayList();
        for (OrderInfoForThemeVO orderInfoForThemeVO : orderInfoForThemeVOList) {
            final OrderTypeEnum orderTypeEnum = OrderTypeEnum.valueOf(orderInfoForThemeVO.getOrderType());
            if (orderTypeEnum != null){
                orderInfoForThemeVO.setOrderTypeDesc(orderTypeEnum.getDesc());
            }
            orderInfoForThemeVOListNew.add(orderInfoForThemeVO);
        }
        return new PageVO<>(orderInfoForThemeVOListNew,pagerInfo);
    }

    /**
     * 获取订单信息
     * @param orderForThemeDTO orderForThemeDTO
     * @return List<OrderInfoForThemeVO>
     */
    @Override
    public List<OrderInfoForThemeVO> listOrderFilterBriefInfoForThemeActivity(OrderForThemeDTO orderForThemeDTO) {
        List<OrderInfoForThemeVO> pageVO = new ArrayList<>();
        List<OrderInfoForThemeVO> orderInfoForThemeVOList = orderMapper.listOrderFilterBriefByBranch(orderForThemeDTO);
        if (CollectionUtils.isEmpty(orderInfoForThemeVOList)){
            return pageVO;
        }
        List<OrderInfoForThemeVO> orderInfoForThemeVOListNew = Lists.newArrayList();
        for (OrderInfoForThemeVO orderInfoForThemeVO : orderInfoForThemeVOList) {
            final OrderTypeEnum orderTypeEnum = OrderTypeEnum.valueOf(orderInfoForThemeVO.getOrderType());
            if (orderTypeEnum != null){
                orderInfoForThemeVO.setOrderTypeDesc(orderTypeEnum.getDesc());
            }
            orderInfoForThemeVOListNew.add(orderInfoForThemeVO);
        }
        return orderInfoForThemeVOListNew;
    }

    /**
     * 天杰白名单订单校验
     *
     * @param orderSn 订单编号
     * @return true 校验通过，允许进行后续操作
     */
    private Boolean tianjieOrderSnCheck(String orderSn) {
        List<DictionaryItemVO> dictionary = bmsIntegration.listDictionaryItemsByTypeCode(CommonConst.KEY_TIANJIE_ORDER_SN_WHITE_LIST, CommonConst.MALL_SYSTEM_MANAGE_ID);
        if (org.springframework.util.CollectionUtils.isEmpty(dictionary)) {
            return true;
        }
        for (DictionaryItemVO dictionaryItemVO : dictionary) {
            String itemDesc = dictionaryItemVO.getItemDesc();
            if (itemDesc.contains(orderSn)) {
                log.info("checkTianjieOrderSn 命中,orderSn:{}", orderSn);
                return false;
            }
        }
        return true;
    }

}
