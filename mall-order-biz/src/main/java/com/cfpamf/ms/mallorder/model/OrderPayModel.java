package com.cfpamf.ms.mallorder.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.exceptions.ApiException;
import com.beust.jcommander.internal.Maps;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.mallpayment.facade.request.loan.LoanLendingResult;
import com.cfpamf.mallpayment.facade.request.loan.OrderDetailInfo;
import com.cfpamf.mallpayment.facade.vo.PaymentNotifyVO;
import com.cfpamf.ms.customer.facade.request.bankcard.QueryBankcardInfoReq;
import com.cfpamf.ms.customer.facade.vo.bankcard.BankcardVo;
import com.cfpamf.ms.mall.account.api.StmAccountFacade;
import com.cfpamf.ms.mall.account.constant.AccountConstans;
import com.cfpamf.ms.mall.account.enums.AccountCardTypeEnum;
import com.cfpamf.ms.mall.account.enums.AccountTypeEnum;
import com.cfpamf.ms.mall.account.request.AccountQuery;
import com.cfpamf.ms.mall.account.vo.AccountCard;
import com.cfpamf.ms.mall.account.vo.AccountVO;
import com.cfpamf.ms.mall.settlement.enums.BillAccountTypeEnum;
import com.cfpamf.ms.mallmember.api.MemberBalanceLogFeignClient;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallmember.vo.MemberBalanceLogVO;
import com.cfpamf.ms.mallorder.common.config.ChannelFeeRateConfig;
import com.cfpamf.ms.mallorder.common.config.OrderSharingRateConfig;
import com.cfpamf.ms.mallorder.common.constant.TaskConstant;
import com.cfpamf.ms.mallorder.common.enums.OrderCreateChannel;
import com.cfpamf.ms.mallorder.common.enums.PayChannelEnum;
import com.cfpamf.ms.mallorder.common.enums.PayMethodEnum;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.common.util.DateUtil;
import com.cfpamf.ms.mallorder.common.util.ExternalApiUtil;
import com.cfpamf.ms.mallorder.common.util.OrderBizUtils;
import com.cfpamf.ms.mallorder.constant.ExchangeOrderConst;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.controller.fegin.facade.AccountCardFacade;
import com.cfpamf.ms.mallorder.controller.fegin.facade.CustomerServiceFeign;
import com.cfpamf.ms.mallorder.controller.fegin.facade.DbcServiceFeign;
import com.cfpamf.ms.mallorder.dto.BizUserInfoDTO;
import com.cfpamf.ms.mallorder.dto.OrderSubmitDTO;
import com.cfpamf.ms.mallorder.dto.OrderSubmitMqConsumerDTO;
import com.cfpamf.ms.mallorder.dto.RuleServiceFeeQueryDTO;
import com.cfpamf.ms.mallorder.enums.*;
import com.cfpamf.ms.mallorder.integration.cashier.request.InterestType;
import com.cfpamf.ms.mallorder.integration.omsbase.OmsBaseIntegration;
import com.cfpamf.ms.mallorder.integration.pay.PayIntegration;
import com.cfpamf.ms.mallorder.integration.settlement.BillOperatinIntegration;
import com.cfpamf.ms.mallorder.integration.shop.ShopIntegration;
import com.cfpamf.ms.mallorder.mapper.OrderMapper;
import com.cfpamf.ms.mallorder.mapper.OrderPayMapper;
import com.cfpamf.ms.mallorder.mapper.OrderProductMapper;
import com.cfpamf.ms.mallorder.mapper.OrderPromotionSendCouponMapper;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.PayWayChangeRequest;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.request.OrderPayExample;
import com.cfpamf.ms.mallorder.request.OrderPromotionSendCouponExample;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.service.impl.OrderCreateHelper;
import com.cfpamf.ms.mallorder.v2.domain.vo.PayInfoExtraInfoVO;
import com.cfpamf.ms.mallorder.v2.service.OrderPayRecordService;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallorder.v2.service.PayService;
import com.cfpamf.ms.mallorder.v2.strategy.OrderPayProcessStrategy;
import com.cfpamf.ms.mallorder.v2.strategy.context.OrderPayProcessStrategyContext;
import com.cfpamf.ms.mallorder.vo.OrderPayInfoVO;
import com.cfpamf.ms.mallpromotion.api.CouponFeignClient;
import com.cfpamf.ms.mallpromotion.api.CouponMemberFeignClient;
import com.cfpamf.ms.mallpromotion.api.CouponUseLogFeignClient;
import com.cfpamf.ms.mallpromotion.api.PromotionCommonFeignClient;
import com.cfpamf.ms.mallpromotion.request.Coupon;
import com.cfpamf.ms.mallpromotion.request.CouponMember;
import com.cfpamf.ms.mallpromotion.request.CouponUseLog;
import com.cfpamf.ms.mallpromotion.vo.CouponVO;
import com.cfpamf.ms.mallpromotion.vo.OrderPaySuccessReturnVO;
import com.cfpamf.ms.mallshop.api.StoreBindCategoryFeignClient;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.cfpamf.ms.mallshop.enums.WhiteListEnum;
import com.cfpamf.ms.mallshop.request.StoreBindCategoryExample;
import com.cfpamf.ms.mallshop.resp.StoreBindCategory;
import com.cfpamf.ms.mallshop.resp.StoreContractReceiptInfoVO;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.slodon.bbc.core.constant.*;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PagerInfo;
import com.slodon.bbc.core.uid.CouponCode;
import com.slodon.bbc.core.util.AssertUtil;
import com.slodon.bbc.core.util.RedBagUtils;
import com.slodon.bbc.core.util.TimeUtil;
import com.slodon.bbc.starter.mq.entity.MessageSendProperty;
import com.slodon.bbc.starter.mq.entity.MessageSendVO;
import io.seata.core.context.RootContext;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.slodon.bbc.core.constant.StarterConfigConst.MQ_EXCHANGE_NAME;
import static com.slodon.bbc.core.constant.StarterConfigConst.MQ_QUEUE_NAME_MEMBER_MSG;

@Component
@Slf4j
public class OrderPayModel {

    @Autowired
    private ChannelFeeRateConfig channelFeeRateConfig;
    @Resource
    private OrderPayMapper orderPayMapper;
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private OrderPromotionSendCouponMapper orderPromotionSendCouponMapper;
    @Resource
    private OrderProductMapper orderProductMapper;
    @Resource
    private OrderLogModel orderLogModel;
    @Resource
    private OrderProductModel orderProductModel;
    @Resource
    private MemberFeignClient memberFeignClient;
    @Resource
    private MemberBalanceLogFeignClient memberBalanceLogFeignClient;
    @Resource
    private PromotionCommonFeignClient promotionCommonFeignClient;
    @Resource
    private CouponFeignClient couponFeignClient;
    @Resource
    private CouponMemberFeignClient couponMemberFeignClient;
    @Resource
    private CouponUseLogFeignClient couponUseLogFeignClient;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private OrderModel orderModel;
    @Resource
    private IOrderService orderService;
    @Resource
    private OrderCreateHelper orderCreateHelper;
    @Autowired
    private IOrderProductService orderProductService;
    @Autowired
    private IOrderPayService orderPayService;
    @Autowired
    private IOrderReturnService orderReturnService;
    @Resource
    private IBzBankTransferService bzBankTransferService;
    @Resource
    private IOrderExtendFinanceService financeService;
    @Resource
    private ITaskQueueService taskQueueService;
    @Resource
    private StoreFeignClient storeFeignClient;
    @Resource
    private AccountCardFacade accountCardFacade;
    @Resource
    private CustomerServiceFeign customerServiceFeign;
    @Autowired
    private ILoanResultService loanResultService;
    @Resource
    private BillOperatinIntegration billOperatinIntegration;
    @Resource
    private StmAccountFacade stmAccountFacade;
    @Autowired
    private OrderPayProcessStrategyContext orderPayProcessStrategyContext;
    @Autowired
    private PayService payService;
    @Autowired
    private BankTransferModel bankTransferModel;
    @Autowired
    private OrderOfflineService orderOfflineService;
    @Autowired
    private IOrderAmountStateRecordService orderAmountRecordService;

    @Value("${ding-talk.url}")
    private String DING_TALK_URL;

    @Autowired
    private OrderPresellService orderPresellService;
    @Autowired
    private OmsBaseIntegration omsBaseIntegration;
    @Autowired
    private IOrderAmountStateRecordService iOrderAmountStateRecordService;
    @Autowired
    private IBzOldUserPoolService iBzOldUserPoolService;
    @Autowired
    private StoreBindCategoryFeignClient storeBindCategoryFeignClient;
    @Autowired
    private ShopIntegration shopIntegration;
    @Autowired
    private IOrderExtendService orderExtendService;
    @Autowired
    private DbcServiceFeign dbcServiceFeign;
    @Autowired
    private OrderSharingRateConfig orderSharingRateConfig;

    @Autowired
    private OrderPayRecordService payRecordService;

    public static void main(String[] args) {
        Date executeTime = DateUtils.addDays(DateUtil.getNow(), 1);

        Date c = DateUtils.setHours(executeTime, 3);
        Date a = DateUtils.setMinutes(c, 0);
        Date b = DateUtils.setSeconds(a, 0);

        System.out.println(DateUtil.format(b, "yyyy-MM-dd HH:mm:ss"));



    }

    /**
     * 新增订单支付表
     *
     * @param orderPayPO
     * @return
     */
    public Integer saveOrderPay(OrderPayPO orderPayPO) {
        int count = orderPayMapper.insert(orderPayPO);
        if (count == 0) {
            throw new MallException("添加订单支付表失败，请重试");
        }
        return count;
    }

    /**
     * 根据paySn删除订单支付表
     *
     * @param paySn paySn
     * @return
     */
    public Integer deleteOrderPay(String paySn) {
        if (StringUtils.isEmpty(paySn)) {
            throw new MallException("请选择要删除的数据");
        }
        int count = orderPayMapper.deleteByPrimaryKey(paySn);
        if (count == 0) {
            log.error("根据paySn：" + paySn + "删除订单支付表失败");
            throw new MallException("删除订单支付表失败,请重试");
        }
        return count;
    }

    /**
     * 根据paySn更新订单支付表
     *
     * @param orderPayPO
     * @return
     */
    public Integer updateOrderPay(OrderPayPO orderPayPO) {
        if (StringUtils.isEmpty(orderPayPO.getPaySn())) {
            throw new MallException("请选择要修改的数据");
        }
        int count = orderPayMapper.updateByPrimaryKeySelective(orderPayPO);
        if (count == 0) {
            log.error("根据paySn：" + orderPayPO.getPaySn() + "更新订单支付表失败");
            throw new MallException("更新订单支付表失败,请重试");
        }
        return count;
    }

    /**
     * 根据paySn获取订单支付表详情
     *
     * @param paySn paySn
     * @return
     */
    public OrderPayPO getOrderPayByPaySn(String paySn) {
        return orderPayMapper.getByPrimaryKey(paySn);
    }

    /**
     * 根据条件获取订单支付表列表
     *
     * @param example 查询条件信息
     * @param pager   分页信息
     * @return
     */
    public List<OrderPayPO> getOrderPayList(OrderPayExample example, PagerInfo pager) {
        List<OrderPayPO> orderPayPOList;
        if (pager != null) {
            pager.setRowsCount(orderPayMapper.countByExample(example));
            orderPayPOList = orderPayMapper.listPageByExample(example, pager.getStart(), pager.getPageSize());
        } else {
            orderPayPOList = orderPayMapper.listByExample(example);
        }
        return orderPayPOList;
    }

    /**
     * 提交订单-保存支付信息
     *
     * @param pOrderSn
     * @param paySn
     * @param payAmount
     * @param memberId
     */
    public OrderPayPO buildOrderPayPO(OrderSubmitMqConsumerDTO consumerDTO, OrderSubmitDTO orderSubmitDTO,
                                      String pOrderSn, String paySn, BigDecimal payAmount, Integer memberId) {
        OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPaySn(paySn);
        orderPayPO.setOrderSn(pOrderSn);
        orderPayPO.setPayAmount(payAmount);
        orderPayPO.setMemberId(memberId);
        orderPayPO.setApiPayState(OrderConst.API_PAY_STATE_0);
        orderPayPO.setPaymentName(OrderPaymentConst.PAYMENT_NAME_ONLINE);
        orderPayPO.setPaymentCode(OrderPaymentConst.PAYMENT_CODE_ONLINE);
        if (Objects.nonNull(orderSubmitDTO.getSinglePromotionType()) && orderSubmitDTO
                .getSinglePromotionType() == com.cfpamf.ms.mallpromotion.constant.PromotionConst.PROMOTION_TYPE_107) {
            orderPayPO.setPaymentCode(PayMethodEnum.COMBINATION_PAY.getValue());
            orderPayPO.setPaymentName(PayMethodEnum.COMBINATION_PAY.getDesc());
        }

        // 幂等键：本地订单 -> 使用默认支付单号
        orderPayPO.setOutBizSource(consumerDTO.getParamDTO().getChannel());
        orderPayPO.setOutBizId(paySn);

        // 幂等键：渠道订单 -> 使用外部传入的业务单号
        if (consumerDTO.getChannelOrderSubmitDTO() != null) {
            orderPayPO.setOutBizSource(consumerDTO.getChannelOrderSubmitDTO().getOutBizSource());
            orderPayPO.setOutBizId(consumerDTO.getChannelOrderSubmitDTO().getOutBizId());
        }
        return orderPayPO;
    }

    public void orderPayCheck(OrderPayPO orderPayPO,
                              Member member,
                              BizUserInfoDTO bizUserInfoDTO,
                              Member stationMaster,
                              List<OrderPO> orderPOList,
                              PayMethodEnum payMethodReq) {

        // 权限校验，支付状态校验
        BizAssertUtil.isTrue(!orderPayPO.getMemberId().equals(member.getMemberId()), "您不能操作他人订单");
        BizAssertUtil.isTrue(OrderConst.API_PAY_STATE_1.equals(orderPayPO.getApiPayState()),
                new BusinessException("订单已支付，请勿重复支付"));

        BizAssertUtil.notEmpty(orderPOList, "待支付订单为空，请勿重复支付");

        OrderPO orderPO = orderPOList.get(0);
        if ("OMS".equals(orderPO.getChannel())) {
            throw new BusinessException("物资订单无需人工付款");
        }

        // 代客下单校验：未确认不可支付
        long unConfirmOrder = orderPOList.stream().filter(
                po -> CustomerConfirmStatusEnum.isDraftOrUnconfirmed(po.getCustomerConfirmStatus())).count();
        BizAssertUtil.isTrue(unConfirmOrder > 0,
                new BusinessException("存在代客下单订单未确认，请确认后支付"));

        //客户经理代客下单标识
        boolean valetFlag = OrderPlaceUserRole.isCustomerManagerOrderPlace(orderPO.getOrderPlaceUserRoleCode())
                && Objects.nonNull(bizUserInfoDTO);

        //站长代客下单标识
        boolean stationMasterFlag = OrderPlaceUserRole.isStationMasterOrder(orderPO.getOrderPlaceUserRoleCode())
                && Objects.nonNull(stationMaster);

        // 代客下单校验：客户经理支付，支付方式不可修改
        if (valetFlag || stationMasterFlag) {

            BizAssertUtil.isTrue(PayMethodEnum.valueOf(orderPayPO.getPaymentCode()) != payMethodReq,
                    "代客下单订单不可修改支付方式");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean payWayChange(Member member, PayWayChangeRequest payRequest) {
        // 修改支付方式记录额外信息
        JSONObject payWayExtraInfo = this.checkAndGetPayWayExtraInfo(payRequest);

        // 查询支付信息
        OrderPayPO orderPayPO = this.getOrderPayByPaySn(payRequest.getPaySn());
        BizAssertUtil.isTrue(!orderPayPO.getMemberId().equals(member.getMemberId()), "您不能操作他人订单");
        BizAssertUtil.isTrue(!OrderConst.API_PAY_STATE_0.equals(orderPayPO.getApiPayState()),
                new BusinessException("订单已支付，不可修改支付方式"));
        String paymentCode = orderPayPO.getPaymentCode();
        // 查询支付单下所有订单
        List<OrderPO> orderPOList = orderService.listByPaySn(payRequest.getPaySn());
        if (PayMethodEnum.isCombinationPay(paymentCode)){
            log.info("正在修改组合支付的支付方式,member:{},payRequest:{}",member,payRequest);
            List<OrderPayRecordPO> payRecordList = payRecordService.queryOrderPayByPaySn(payRequest.getPaySn());
            if (CollectionUtils.isEmpty(payRecordList)){
                throw new BusinessException("获取组合支付支付记录失败");
            }
            OrderPayRecordPO depositPayRecord = payRecordList.stream().filter(p -> PresellCapitalTypeEnum.DEPOSIT.getValue().equals(p.getPayOrder())).findFirst().orElse(null);
            if (Objects.isNull(depositPayRecord)){
                throw new BusinessException("获取组合支付定金支付记录失败");
            }
            if (com.cfpamf.ms.mallorder.common.constant.CommonConst.PAY_STATUS_1 != depositPayRecord.getPayStatus()){
                // 不等于待支付，不允许修改支付方式
                throw new BusinessException("组合支付定金不是待支付状态，不允许修改支付方式");
            }
            depositPayRecord.setPaymentCode(payRequest.getPayMethod().getValue());
            depositPayRecord.setPaymentName(payRequest.getPayMethod().getDesc());
            payRecordService.updateById(depositPayRecord);
            LambdaUpdateWrapper<OrderPresellPO> presellUpdate = Wrappers.lambdaUpdate(OrderPresellPO.class);
            presellUpdate.eq(OrderPresellPO::getPaySn,payRequest.getPaySn());
            presellUpdate.eq(OrderPresellPO::getType,1);
            presellUpdate.set(OrderPresellPO::getPaymentCode,payRequest.getPayMethod().getValue());
            presellUpdate.set(OrderPresellPO::getPaymentName,payRequest.getPayMethod().getDesc());
            boolean update = orderPresellService.update(presellUpdate);
            if (!update){
                throw new BusinessException("组合支付定金支付方式修改失败，请联系管理员");
            }
            // 组合支付，修改支付方式时，改为修改定金支付方式
            for (OrderPO orderPO : orderPOList) {
                // 代客下单，草稿状态变更为待客户确认
                if (OrderPlaceUserRole.isValetOrder(orderPO.getOrderPlaceUserRoleCode())
                        && CustomerConfirmStatusEnum.isDraft(orderPO.getCustomerConfirmStatus())) {
                    orderService.setCustomerUnConfirmStatus(orderPO.getOrderSn());
                }
            }
            return Boolean.TRUE;
        }
//        BizAssertUtil.isTrue(PayMethodEnum.isCombinationPay(paymentCode),
//                new BusinessException("订单为组合支付，不可修改支付方式"));

        for (OrderPO orderPO : orderPOList) {

            boolean valetFlag = OrderPlaceUserRole.isValetOrder(orderPO.getOrderPlaceUserRoleCode())
                    && CustomerConfirmStatusEnum.isConfirm(orderPO.getCustomerConfirmStatus());

            // 客户经理代客下单，订单确认后不可修改支付方式
            if (valetFlag) {
                throw new BusinessException("代客下单订单已确认，不可修改支付方式");
            }

            // 更新订单支付方式
            OrderPO orderPoUpdate = new OrderPO();
            orderPoUpdate.setPaymentCode(payRequest.getPayMethod().getValue());
            orderPoUpdate.setPaymentName(payRequest.getPayMethod().getDesc());

            LambdaUpdateWrapper<OrderPO> updateOrder = Wrappers.lambdaUpdate();
            updateOrder.eq(OrderPO::getOrderState, OrderStatusEnum.WAIT_PAY.getValue());
            updateOrder.eq(OrderPO::getOrderId, orderPO.getOrderId());
            boolean update = orderService.update(orderPoUpdate, updateOrder);
            if (!update) {
                throw new BusinessException("修改订单支付方式失败，请确认订单状态重试");
            }

            // 代客下单，草稿状态变更为待客户确认
            if (OrderPlaceUserRole.isValetOrder(orderPO.getOrderPlaceUserRoleCode())
                    && CustomerConfirmStatusEnum.isDraft(orderPO.getCustomerConfirmStatus())) {
                orderService.setCustomerUnConfirmStatus(orderPO.getOrderSn());
            }
        }

        // 修改payInfo支付方式
        OrderPayPO payPOUpdate = new OrderPayPO();
        payPOUpdate.setPaymentCode(payRequest.getPayMethod().getValue());
        payPOUpdate.setPaymentName(payRequest.getPayMethod().getDesc());
        if (Objects.nonNull(payRequest.getEnjoyPayExtraInfo())) {
            payPOUpdate.setEnjoyPayVipFlag(payRequest.getEnjoyPayExtraInfo().getEnjoyPayVipFlag());
        }
        payPOUpdate.setPayWayExtraInfo(payWayExtraInfo);

        LambdaUpdateWrapper<OrderPayPO> updatePay = Wrappers.lambdaUpdate();
        updatePay.eq(OrderPayPO::getPayId, orderPayPO.getPayId());
        updatePay.eq(OrderPayPO::getApiPayState, OrderConst.API_PAY_STATE_0);
        boolean update = orderPayService.update(payPOUpdate, updatePay);
        if (!update) {
            throw new BusinessException("修改支付方式失败，请确认支付状态重试");
        }

        return Boolean.TRUE;
    }

    public JSONObject checkAndGetPayWayExtraInfo(PayWayChangeRequest payRequest) {
        if (PayMethodEnum.isLoanPay(payRequest.getPayMethod())) {
            if (Objects.isNull(payRequest.getEnjoyPayExtraInfo())
                    || StringUtils.isEmpty(payRequest.getEnjoyPayExtraInfo().getRepaymentDay())
                    || StringUtils.isEmpty(payRequest.getEnjoyPayExtraInfo().getRepaymentMode())
                    || StringUtils.isEmpty(payRequest.getEnjoyPayExtraInfo().getRepaymentModeDesc())
                    || StringUtils.isEmpty(payRequest.getEnjoyPayExtraInfo().getLoanPeriod())
                    || Objects.isNull(payRequest.getEnjoyPayExtraInfo().getLimitAmountParamRequest())
                    || StringUtils.isEmpty(payRequest.getEnjoyPayExtraInfo().getEnjoyPayVipFlag())) {
                throw new BusinessException("信贷支付分期信息不能为空");
            }
            return this.enjoyPayInfo(payRequest.getEnjoyPayExtraInfo());
        }

        if (PayMethodEnum.BANK_TRANSFER == payRequest.getPayMethod()) {
            if (Objects.isNull(payRequest.getBankTransferInfo())
                    || StringUtils.isEmpty(payRequest.getBankTransferInfo().getPaymentAccount())
                    || StringUtils.isEmpty(payRequest.getBankTransferInfo().getPaymentName())) {
                throw new BusinessException("银行卡转账付款信息不能为空");
            }
            return this.bankTransferInfo(payRequest.getBankTransferInfo());
        }

        return null;
    }

    public JSONObject enjoyPayInfo(OrderPayInfoVO.EnjoyPayExtraInfo enjoyPayExtraInfo) {
        /*JSONObject jsonObject = new JSONObject();
        jsonObject.put("loanPeriod", enjoyPayExtraInfo.getLoanPeriod());
        jsonObject.put("repaymentDay", enjoyPayExtraInfo.getRepaymentDay());
        jsonObject.put("repaymentMode", enjoyPayExtraInfo.getRepaymentMode());
        jsonObject.put("repaymentModeDesc", enjoyPayExtraInfo.getRepaymentModeDesc());
        jsonObject.put("enjoyPayVipFlag", enjoyPayExtraInfo.getEnjoyPayVipFlag());*/
        return JSONObject.parseObject(JSON.toJSONString(enjoyPayExtraInfo));
    }

    public JSONObject bankTransferInfo(OrderPayInfoVO.BankTransferInfo bankTransferInfo) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("paymentName", bankTransferInfo.getPaymentName());
        jsonObject.put("paymentBankCode", bankTransferInfo.getPaymentBankCode());
        jsonObject.put("paymentAccount", bankTransferInfo.getPaymentAccount());
        jsonObject.put("paymentBankName", bankTransferInfo.getPaymentBankName());
        return jsonObject;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean setUpSettleChannel(String paySn) {
        // 查询订单信息
        LambdaQueryWrapper<OrderPO> orderQuery = Wrappers.lambdaQuery();
        orderQuery.eq(OrderPO::getPaySn, paySn)
                .select(OrderPO::getOrderSn, OrderPO::getOrderState, OrderPO::getNewOrder,
                        OrderPO::getStoreId, OrderPO::getServiceFee, OrderPO::getThirdpartnarFee, OrderPO::getPayChannel,
                        OrderPO::getOrderAmountTotal, OrderPO::getStoreActivityAmount, OrderPO::getStoreVoucherAmount);
        List<OrderPO> orderPOList = orderService.list(orderQuery);
        if (CollectionUtils.isEmpty(orderPOList)) {
            return Boolean.FALSE;
        }

        for (OrderPO orderPO : orderPOList) {
            Integer settleChannel = SettleChannelEnum.UN_KNOW.getCode();

            // 微信支付渠道为云直通，标记为正常结算
            if (PayChannelEnum.YZT_WX.getValue().equals(orderPO.getPayChannel())
                    || PayChannelEnum.JS_WX.getValue().equals(orderPO.getPayChannel())) {
                settleChannel = SettleChannelEnum.STANDARD.getCode();
            } else {
                // 判断店铺是否在超分白名单内，在白名单：协议支付，允许微信支付
                Boolean storeOnList = shopIntegration.isStoreOnList(WhiteListEnum.OVER_MARK_SETTLE, orderPO.getStoreId());
                if (storeOnList) {
                    settleChannel = SettleChannelEnum.AGREEMENT.getCode();
                }
            }
            if (!SettleChannelEnum.UN_KNOW.getCode().equals(settleChannel)){
                JSONObject disableChannelList = new JSONObject();
                OrderExtendPO update = new OrderExtendPO();
                update.setSettleChannel(settleChannel);
                update.setDisableChannelList(disableChannelList);
                LambdaUpdateWrapper<OrderExtendPO> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.eq(OrderExtendPO::getOrderSn, orderPO.getOrderSn());
                orderExtendService.update(update, updateWrapper);
                continue;
            }

            // 不在白名单内，判断分账比例
            BigDecimal trialOrderCommission = ExternalApiUtil.callResultApi(() ->
                            dbcServiceFeign.trialOrderCommission(orderPO.getOrderSn()), orderPO.getOrderSn(),
                    "/dbc/order/orderCommissionTrial", "获取分销试算佣金");
            if (Objects.isNull(trialOrderCommission)) {
                trialOrderCommission = BigDecimal.ZERO;
            }
            // 总平台分账费用：平台服务费 + 代运营服务费 + 佣金
            BigDecimal totalPlatformServiceFee =
                    orderPO.getServiceFee().add(orderPO.getThirdpartnarFee()).add(trialOrderCommission);
            // 总费用：应付-店铺优惠
            BigDecimal totalSharingAmount = orderPO.getOrderAmountTotal()
                    .subtract(orderPO.getStoreActivityAmount()).subtract(orderPO.getStoreVoucherAmount());
            // 最大分账比例
            BigDecimal maxSharingRate = new BigDecimal(orderSharingRateConfig.getMaxSharingRate())
                    .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);

            // 最大可分账金额 < 总平台分账金额（超出设定比例）：禁止微信支付
            JSONObject disableChannelList = new JSONObject();
            log.info("setUpSettleChannel for calculator order maxSharingRate,orderSn:{},totalPlatformServiceFee:{},totalSharingAmount:{},maxSharingRate:{}",
                    orderPO.getOrderSn(), totalPlatformServiceFee, totalSharingAmount, maxSharingRate);
            if (totalSharingAmount.multiply(maxSharingRate).compareTo(totalPlatformServiceFee) < 0) {
                disableChannelList.put(PayWayEnum.WX_PAY.getValue(),
                        "当前订单包含特殊商品，因微信支付官方限制，暂时无法使用，请使用其他支付方式");
            }
            OrderExtendPO update = new OrderExtendPO();
            update.setSettleChannel(SettleChannelEnum.STANDARD.getCode());
            update.setDisableChannelList(disableChannelList);
            LambdaUpdateWrapper<OrderExtendPO> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(OrderExtendPO::getOrderSn, orderPO.getOrderSn());
            orderExtendService.update(update, updateWrapper);
        }

        return Boolean.TRUE;
    }

    /**
     * 订单余额支付， 1.使用用户余额顺序支付订单，能支付几单就支付几单，剩余未能支付的使用三方支付 2.计算需要三方支付的金额
     *
     * @param orderPOList 要支付的订单列表
     * @param memberDb    会员
     * @return 需要三方支付的金额
     */
    @GlobalTransactional
    public BigDecimal balancePay(List<OrderPO> orderPOList, Member memberDb) {
        BigDecimal needPay = new BigDecimal("0.00");// 最终返回值，订单仍需支付金额
        BigDecimal balanceAvailable = memberDb.getBalanceAvailable();// 会员可用余额
        BigDecimal balanceFrozen = memberDb.getBalanceFrozen();// 会员冻结金额

        for (OrderPO orderPO : orderPOList) {
            // 当前订单还需支付金额
            BigDecimal currentNeedPay =
                    orderPO.getOrderAmount().subtract(orderPO.getBalanceAmount()).subtract(orderPO.getPayAmount());
            if (currentNeedPay.compareTo(BigDecimal.ZERO) == 0) {
                // 订单已支付
                this.orderPaySuccess(orderPO, null, orderPO.getPaymentCode(), orderPO.getPaymentName(), new Date(),
                        null);
                break;
            }
            if (balanceAvailable.compareTo(BigDecimal.ZERO) <= 0) {
                // 可用余额用尽 needPay增加
                needPay = needPay.add(currentNeedPay);

            } else if (balanceAvailable.compareTo(currentNeedPay) < 0) {
                /*
                 *余额不够支付当前订单:
                 * 1.修改订单余额使用数量,
                 * 2.记录余额冻结日志，
                 * 3.needPay增加
                 * 4.冻结金额增加，
                 * 5.可用金额减少
                 */

                // 1修改订单余额使用数量,
                OrderPO updateOrderPO = new OrderPO();
                updateOrderPO.setBalanceAmount(balanceAvailable);
                OrderExample orderExample = new OrderExample();
                orderExample.setOrderSn(orderPO.getOrderSn());
                int count = orderMapper.updateByExampleSelective(updateOrderPO, orderExample);
                AssertUtil.isTrue(count == 0, "更新订单支付金额失败");

                // 2记录余额冻结日志，
                MemberBalanceLogVO memberBalanceLog = new MemberBalanceLogVO();
                memberBalanceLog.setMemberId(memberDb.getMemberId());
                memberBalanceLog.setMemberName(memberDb.getMemberName());
                memberBalanceLog.setAfterChangeAmount(balanceFrozen.add(balanceAvailable));
                memberBalanceLog.setChangeValue(BigDecimal.ZERO);
                memberBalanceLog.setFreezeAmount(balanceFrozen.add(balanceAvailable));
                memberBalanceLog.setFreezeValue(balanceAvailable);
                memberBalanceLog.setCreateTime(new Date());
                memberBalanceLog.setType(MemberConst.TYPE_7);
                memberBalanceLog.setDescription("订单支付冻结，订单号：" + orderPO.getOrderSn());
                memberBalanceLog.setAdminId(0);
                memberBalanceLog.setAdminName(memberDb.getMemberName());
                memberBalanceLogFeignClient.saveMemberBalanceLog(memberBalanceLog);

                // 3.needPay增加
                needPay = needPay.add(currentNeedPay.subtract(balanceAvailable));

                // 4.冻结金额增加，
                balanceFrozen = balanceFrozen.add(balanceAvailable);

                // 5.可用金额减少
                balanceAvailable = BigDecimal.ZERO;

            } else {
                /*
                 *余额够支付当前订单,直接扣除余额:
                 * 1.记录余额使用日志，
                 * 2.完成订单，
                 * 3.减少余额，
                 */

                // 1.记录余额使用日志，
                MemberBalanceLogVO memberBalanceLog = new MemberBalanceLogVO();
                memberBalanceLog.setMemberId(memberDb.getMemberId());
                memberBalanceLog.setMemberName(memberDb.getMemberName());
                memberBalanceLog.setAfterChangeAmount(balanceFrozen.add(balanceAvailable).subtract(currentNeedPay));
                memberBalanceLog.setChangeValue(currentNeedPay);
                memberBalanceLog.setFreezeAmount(balanceFrozen);
                memberBalanceLog.setFreezeValue(BigDecimal.ZERO);
                memberBalanceLog.setCreateTime(new Date());
                memberBalanceLog.setType(MemberConst.TYPE_3);
                memberBalanceLog.setDescription("订单消费扣除，订单号：" + orderPO.getOrderSn());
                memberBalanceLog.setAdminId(0);
                memberBalanceLog.setAdminName(memberDb.getMemberName());
                memberBalanceLogFeignClient.saveMemberBalanceLog(memberBalanceLog);

                // 2.完成订单，
                this.orderPaySuccess(orderPO, null, OrderPaymentConst.PAYMENT_CODE_BALANCE,
                        OrderPaymentConst.PAYMENT_NAME_BALANCE, new Date(), null);

                // 3.减少余额，
                balanceAvailable = balanceAvailable.subtract(currentNeedPay);
            }
        }

        // 统一更新用户余额
        Member updateMember = new Member();
        updateMember.setMemberId(memberDb.getMemberId());
        updateMember.setBalanceAvailable(balanceAvailable);
        updateMember.setBalanceFrozen(balanceFrozen);
        updateMember.setLastPaymentCode(OrderPaymentConst.PAYMENT_CODE_BALANCE);
        updateMember.setUpdateTime(new Date());
        memberFeignClient.updateMember(updateMember);

        // 发送余额变动消息通知
        this.sendMsgAccountChange(updateMember,
                memberDb.getBalanceAvailable().subtract(updateMember.getBalanceAvailable()));

        return needPay;
    }

    /**
     * 订单支付完成： 1.更改订单状态 2.记录订单日志 3.增加货品销量 4.解冻会员余额（订单为部分余额支付时） 5.记录余额日志（订单为部分余额支付时） 6.支付单号下所有订单都已支付，修改订单支付表状态
     * 7.非普通订单，执行自定义操作 8.活动赠送优惠券，记录优惠券日志 9.付款成功-记录结算流水
     *
     * @param tradeSn 第三方支付流水号
     */
    @GlobalTransactional
    public void orderPaySuccess(OrderPO orderPO, String tradeSn, String paymentCode, String paymentName, Date payTime,
                                String payNo) {
        // 组合支付方式
        String composeWay = "";

        OrderStatusEnum orderStatus = OrderStatusEnum.valueOf(orderPO.getOrderState());

        if (OrderStatusEnum.WAIT_PAY_DEPOSIT != orderStatus && OrderStatusEnum.WAIT_PAY != orderStatus
                && OrderStatusEnum.DEAL_PAY != orderStatus) {
            log.error("订单支付成功 校验失败-{}状态为:{} paySn:{}", orderPO.getOrderSn(), orderStatus.getDesc(), orderPO.getPaySn());
            return;
        }

        // 0元订单 && 支付方式为在线支付  支付方式更改为卡券支付
        if (OrderBizUtils.isZeroOrder(orderPO)
                && PayMethodEnum.ONLINE.isTrue(paymentCode)) {
            paymentCode = PayMethodEnum.CARD_VOUCHER.getValue();
            paymentName = PayMethodEnum.CARD_VOUCHER.getDesc();
        }

        int orderType = orderPO.getOrderType();
        // Andy.预售
        OrderEventEnum orderEventEnum = OrderEventEnum.PAY;// 默认支付消息为支付成功
        OrderPayProcessStrategy orderPayProcessStrategy = orderPayProcessStrategyContext.getStrategy(orderType);
        // 识别策略模式相关订单类型，作出额外的支付成功处理，Andy.2022.5.20增加
        if (ObjectUtils.isNotEmpty(orderPayProcessStrategy)) {
            log.info("订单orderSn:{}，orderType：{}，开启支付成功策略模式处理", orderPO.getOrderSn(), orderType, orderPO.getPaySn());
            JsonResult<OrderEventEnum> updateSuccess = orderPayProcessStrategy.orderPayProcess(orderPO, payNo, tradeSn,
                    paymentCode, paymentName, composeWay, payTime);
            AssertUtil.isTrue(updateSuccess.getState() != 200, "更新活动订单支付状态失败！");
            orderEventEnum = updateSuccess.getData();
        } else {
            //更新订单状态及支付方式
            boolean updateSuccess = this.orderInfoProcess(orderPO, paymentCode, paymentName, payTime, composeWay);

            AssertUtil.isTrue(!updateSuccess, "更新订单支付状态失败！");

            // 2.记录订单日志（预售和阶梯团订单额外处理）
            if (!OrderBizUtils.isPreSellOrLadderGroup(orderType)) {
                orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_MEMBER, Long.valueOf(orderPO.getMemberId()),
                        orderPO.getMemberName(), orderPO.getOrderSn(), orderPO.getOrderState(), OrderConst.ORDER_STATE_20,
                        LoanStatusEnum.APPLY_SUCCESS.getValue(), "订单支付完成",
                        OrderCreateChannel.H5);
            }

            // 3.增加货品销量
            orderProductModel.orderPaySuccessAddSales(orderPO.getOrderSn());

            if (!OrderBizUtils.isPreSellOrLadderGroup(orderType)) {
                // 6.支付单号下所有订单都已支付，修改订单支付表状态
                LambdaQueryWrapper<OrderPO> orderQuery = new LambdaQueryWrapper<>();
                orderQuery.eq(OrderPO::getPaySn, orderPO.getPaySn());
                orderQuery.in(OrderPO::getOrderState,
                        Arrays.asList(OrderConst.ORDER_STATE_10, OrderConst.ORDER_STATE_15));
                List<OrderPO> orderPOList = orderService.list(orderQuery);
                if (CollectionUtils.isEmpty(orderPOList)) {
                    // 该支付单号下没有待支付的订单，修改支付表支付状态
                    OrderPayPO orderPayPO = new OrderPayPO();
                    orderPayPO.setPaySn(orderPO.getPaySn());
                    orderPayPO.setApiPayState(OrderConst.API_PAY_STATE_1);
                    orderPayPO.setCallbackTime(new Date());
                    orderPayPO.setTradeSn(tradeSn);
                    orderPayPO.setPaymentName(paymentName);
                    orderPayPO.setPaymentCode(paymentCode);
                    int count = orderPayMapper.updateByPrimaryKeySelective(orderPayPO);
                    AssertUtil.isTrue(count == 0, "更新支付状态失败");
                }
            }
        }

        // 活动类型订单，调用促销接口记录活动信息
        if (OrderTypeEnum.isPromotionType(orderType) ) {
            // 7.非普通订单，执行自定义操作
//            if(CommonConfig.enableTcc()) {目前拼团有问题，暂时去掉
//                promotionManager.orderPaySuccessSubmitPromotionActivities(orderType, orderPO.getOrderSn(), paymentName, tradeSn, paymentName, paymentCode);
//            }
            JsonResult<OrderPaySuccessReturnVO> promotionReturn = promotionCommonFeignClient
                    .orderPaySuccess(orderPO.getOrderSn(), orderType, orderPO.getPaySn(), tradeSn, paymentName, paymentCode);
            try {
                paySuccessHandleOrderDelivery(promotionReturn, orderPO.getOrderSn(), orderType);
            } catch (Exception e) {
                log.error("订单发货状态异常:{}，返回信息：{}", e.getMessage(), promotionReturn.toString());
            }
        }

        // 8.活动赠送优惠券，记录优惠券日志
        this.sendCouponToMember(orderPO.getOrderSn(), orderPO.getMemberId(), orderPO.getMemberName());

        // 9.初始化订单金额状态记录
        OrderPO orderPONew = orderService.getById(orderPO.getOrderId());
        Result<Void> RecordResult = orderAmountRecordService.initOrderAmountState(orderPONew);
        AssertUtil.isTrue(!RecordResult.isSuccess(),
                String.format("记录订单金额初始化状态失败,orderSn:%s,原因:%s", orderPO.getOrderSn(), RecordResult.getMessage()));

        // 10. 发送变更消息
        orderCreateHelper.addOrderChangeEvent(orderPONew, orderEventEnum, orderPONew.getPayTime());
        // 发送付款成功消息通知
        try {
            this.sendMsgPaySuccess(orderPONew.getMemberId(), orderPO.getMemberName(), orderPONew.getStoreId(), orderPONew.getOrderSn(), orderPONew.getBalanceAmount().add(orderPONew.getPayAmount()));
        } catch (Exception e) {
            log.info("发送付款成功消息通知异常,{}", JSON.toJSONString(orderPONew), e);
        }

    }

    /**
     * 更新订单
     * @param orderPO
     * @param paymentCode
     * @param paymentName
     * @param payTime
     * @param composeWay
     * @return
     */
    public boolean orderInfoProcess(OrderPO orderPO, String paymentCode, String paymentName, Date payTime, String composeWay) {

        //根据支付方式计算渠道服务费，by bone需求
        BigDecimal channelServiceFee = this.calculateChannelServiceFee(orderPO, paymentCode);
        //查询是否需要计算平台服务费
        Boolean ruleServiceFeeFlag = this.queryRuleServiceFeeFlag(orderPO, paymentCode);
        BigDecimal orderPlatformServiceFee = BigDecimal.ZERO;
        // 需要计算平台服务费
        // 需要计算平台服务费,其中预占订单跟线下订单不需要计算
        if (Boolean.TRUE.equals(ruleServiceFeeFlag)) {
            BigDecimal priceInFee =
                    orderPO.getGoodsAmount().add(orderPO.getExpressFee()).add(orderPO.getXzCardExpressFeeAmount())
                            .subtract(orderPO.getStoreVoucherAmount()).subtract(orderPO.getStoreActivityAmount());
            orderPlatformServiceFee = priceInFee.multiply(orderPO.getServiceFeeRate());

            //补充分摊平台服务费至商品行以及订单
            this.updatePlatformAmount(orderPO, orderPlatformServiceFee);
        }
        //新增平台服务费orderAmount
        iOrderAmountStateRecordService.saveServiceFeeAmount(orderPO.getOrderSn(), orderPO.getOrderSn(),
                orderPlatformServiceFee);

        // 1.更改订单状态
        OrderPO updateOrderPO = new OrderPO();
        updateOrderPO.setChannelServiceFee(channelServiceFee);
        // andy预售
        if (ObjectUtils.isNotEmpty(orderPO.getChannelServiceFee()) && orderPO.getChannelServiceFee().compareTo(BigDecimal.ZERO) > 0) {
            updateOrderPO.setChannelServiceFee(channelServiceFee.add(orderPO.getChannelServiceFee()));
        }
        updateOrderPO.setBankPayTrxNo(orderPO.getBankPayTrxNo());
        updateOrderPO.setOrderId(orderPO.getOrderId());
        // 信贷支付状态
        if (PayMethodEnum.isLoanPay(PayMethodEnum.getValue(paymentCode))) {
            updateOrderPO.setLoanPayState(LoanStatusEnum.APPLY_SUCCESS.getValue());
        }
        if (OrderTypeEnum.isPreOccupiedOrder(orderPO.getOrderType())) {
            updateOrderPO.setOrderType(OrderTypeEnum.ORDER_TYPE_6.getValue());
        }
        log.info("orderSn：{} paySn:{} 支付成功，更新订单支付结果开始处理，非策略执行。", orderPO.getOrderSn(), orderPO.getPaySn());
        // 预售和阶梯团订单状态额外处理
        if (!OrderBizUtils.isPreSellOrLadderGroup(orderPO.getOrderType())) {
            if (null == payTime) {
                updateOrderPO.setPayTime(new Date());
            } else {
                updateOrderPO.setPayTime(payTime);
            }
            updateOrderPO.setPayUpdateTime(new Date());
            updateOrderPO.setOrderState(OrderConst.ORDER_STATE_20);
        }
        // 非拼团订单，付款成功则直接可发货
        if (orderPO.getOrderType() != PromotionConst.PROMOTION_TYPE_102) {
            updateOrderPO.setIsDelivery(OrderConst.IS_DELIVER_1);
        }
        updateOrderPO.setPaymentCode(paymentCode);
        updateOrderPO.setPaymentName(paymentName);
        updateOrderPO.setPayAmount(orderPO.getOrderAmount());
        updateOrderPO.setComposePayName(composeWay);
        updateOrderPO.setSellerId(this.parseSellerId(orderPO, paymentCode));
        return orderService.updateById(updateOrderPO);
    }

    /**
     * 平摊计算平台服务费
     * @param orderPO
     * @param orderPlatformServiceFee
     */
    public void updatePlatformAmount(OrderPO orderPO, BigDecimal orderPlatformServiceFee) {
        log.info("分摊计算平台服务费，订单号:{},金额:{}", orderPO.getOrderSn(), orderPlatformServiceFee);
        if (orderPlatformServiceFee.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }

        OrderPO updateOrderPO = new OrderPO();
        updateOrderPO.setOrderId(orderPO.getOrderId());
        updateOrderPO.setServiceFee(orderPlatformServiceFee);

        List<OrderProductPO> orderProductPOS = orderProductService.lambdaQuery()
                .eq(OrderProductPO::getOrderSn, orderPO.getOrderSn())
                .eq(OrderProductPO::getIsGift, OrderConst.IS_GIFT_NO)
                .list();

        // 商品行代运营费累计值
        BigDecimal fixedServiceFee = BigDecimal.ZERO;
        List<OrderProductPO> updateOrderProductS = new ArrayList<>();

        for (int i = 0, s = orderProductPOS.size(); i < s; i++) {
            OrderProductPO orderProductPO = orderProductPOS.get(i);

            OrderProductPO updateProduct = new OrderProductPO();
            updateProduct.setOrderProductId(orderProductPO.getOrderProductId());
            // 只有一行商品
            if (s == 1) {
                updateProduct.setServiceFee(orderPlatformServiceFee);
            } else if (i == s - 1) { // 最后一行商品 钆差
                updateProduct.setServiceFee(orderPlatformServiceFee.subtract(fixedServiceFee));
            } else {
                BigDecimal productPrice = orderProductPO.getProductShowPrice()
                        .multiply(BigDecimal.valueOf(orderProductPO.getProductNum()))
                        .subtract(orderProductPO.getStoreActivityAmount())
                        .subtract(orderProductPO.getStoreVoucherAmount());
                BigDecimal orderPrice = orderPO.getGoodsAmount().subtract(orderPO.getStoreActivityAmount())
                        .subtract(orderPO.getStoreVoucherAmount());
                BigDecimal productServiceFee = BigDecimal.ZERO;
                if (orderPrice.compareTo(BigDecimal.ZERO) > 0) {
                    productServiceFee = productPrice.multiply(orderPO.getServiceFeeRate()).setScale(2, RoundingMode.HALF_UP);
                }
                updateProduct.setServiceFee(productServiceFee);

                fixedServiceFee = fixedServiceFee.add(productServiceFee);

            }
            updateOrderProductS.add(updateProduct);
        }
        boolean orderUpdateResult = orderService.updateById(updateOrderPO);
        BizAssertUtil.isTrue(!orderUpdateResult,"更新订单失败");
        boolean updateBatchById = orderProductService.updateBatchById(updateOrderProductS);
        BizAssertUtil.isTrue(!updateBatchById,"更新订单商品失败");
    }

    public Boolean queryRuleServiceFeeFlag(OrderPO orderPO, String paymentCode) {
        if (OrderTypeEnum.isOfflineAll(orderPO.getOrderType())) {
            return Boolean.FALSE;
        }
        StoreBindCategoryExample storeBindCategoryExample = new StoreBindCategoryExample();
        storeBindCategoryExample.setStoreId(orderPO.getStoreId());

        List<StoreBindCategory> categoryList = storeBindCategoryFeignClient.getStoreBindCategoryList(storeBindCategoryExample);
        Set<Integer> goodsCategoryId1 = new HashSet<>();
        Set<Integer> goodsCategoryId2 = new HashSet<>();

        for (StoreBindCategory storeBindCategory : categoryList) {
            goodsCategoryId1.add(storeBindCategory.getGoodsCategoryId1());
            goodsCategoryId2.add(storeBindCategory.getGoodsCategoryId2());
        }

        RuleServiceFeeQueryDTO queryDTO = new RuleServiceFeeQueryDTO();
        queryDTO.setPayWay(paymentCode);
        queryDTO.setIntroduceMerchant(orderPO.getRecommendStoreId().toString());
        queryDTO.setFirstCategory(new ArrayList<>(goodsCategoryId1));
        queryDTO.setSecondCategory(new ArrayList<>(goodsCategoryId2));
        return omsBaseIntegration.query(queryDTO, orderPO.getOrderSn());
    }

    /**
     * 计算渠道服务费率
     *
     * @param orderPO
     * @param paymentCode
     * @return
     */
    public BigDecimal calculateChannelServiceFee(OrderPO orderPO, String paymentCode) {
        BillAccountTypeEnum accountEnum;
        if (null == orderPO.getNewOrder() || !orderPO.getNewOrder()) {
            accountEnum = BillAccountTypeEnum.valueByPay(paymentCode);
            if (ObjectUtils.isEmpty(accountEnum)) {
                accountEnum = BillAccountTypeEnum.valueByPay(paymentCode);
            }
        } else {
            accountEnum = BillAccountTypeEnum.payment2AccountType(paymentCode);
            if (ObjectUtils.isEmpty(accountEnum)) {
                accountEnum = BillAccountTypeEnum.valueByPay(paymentCode);
            }
        }
        BigDecimal channelServiceFee = BigDecimal.ZERO;
        //换货后的订单 不计算支付渠道手续费
        if(orderPO.getExchangeFlag() == null || ExchangeOrderConst.ORDER_EXCHANGE_FLAG_2 != orderPO.getExchangeFlag()) {
            String reteKey = accountEnum.getValue() + "-" + paymentCode;

            try {
                log.info("reteKey:{} ", reteKey);
                String rate = channelFeeRateConfig.getMappedRate().get(reteKey);
                log.info("reteKey:{} rate：{}", reteKey, rate);
                BigDecimal thousandth = new BigDecimal(rate);
                channelServiceFee = orderPO.getOrderAmount().multiply(thousandth).setScale(2, RoundingMode.HALF_UP);
            } catch (Exception e) {
                log.error("渠道费用计算异常:{}", e.getMessage());
                throw new MallException("渠道服务费用计算异常", 999, e);
            }
        }
        return channelServiceFee;
    }

    /**
     * 解析订单收款方信息
     *
     * @param orderPO     订单信息
     * @param paymentCode 最终支付方式
     * @return 收款方信息
     */
    public String parseSellerId(OrderPO orderPO, String paymentCode) {
        String sellerInfo = "";
        // 零元订单，设置指定标识（如果是乡助卡的0元，取银行卡号）
        if (orderPO.getOrderAmount().compareTo(BigDecimal.ZERO) == 0) {
            try {
                sellerInfo = getZeroXzCardSellerInfo(orderPO, paymentCode);
            } catch (Exception e) {
                log.error("零元乡助卡订单设置收款方信息异常，订单号【{}】", orderPO.getOrderSn(), e);
            }
        } else if (PayMethodEnum.BANK_PAY.getValue().equals(paymentCode)) {
            // 银行卡支付设置收款信息
            try {
                JsonResult<AccountCard> bankcardVoResult = accountCardFacade.defaultCard(orderPO.getStoreId());
                sellerInfo = com.gexin.fastjson.JSON.toJSONString(
                        Maps.newHashMap("storeBankAccount", bankcardVoResult.getData().getBankAccountNumber()));
            } catch (Exception e) {
                log.error("BANK_PAY 设置收款信息异常，订单号【{}】", orderPO.getOrderSn(), e);
            }
        } else if (PayMethodEnum.BANK_TRANSFER.getValue().equals(paymentCode)) {
            // 银行卡转账支付
            try {
                sellerInfo = getBankTransferSellerInfo(orderPO);
            } catch (Exception e) {
                log.error("BANK_TRANSFER 设置收款信息异常，订单号【{}】", orderPO.getOrderSn(), e);
            }
        } else if (PayMethodEnum.ENJOY_PAY.getValue().equals(paymentCode)
                || PayMethodEnum.FOLLOW_HEART.getValue().equals(paymentCode)) {
            // 支付方式为ENJOY_PAY，获取引荐商户收款卡信息
            try {
                sellerInfo = this.getEnjoySellerInfo(orderPO);
            } catch (Exception e) {
                log.error("ENJOY_PAY 设置收款信息异常，订单号【{}】", orderPO.getOrderSn(), e);
            }
        } else if (PayMethodEnum.ALIPAY.getValue().equals(paymentCode)
                || PayMethodEnum.WXPAY.getValue().equals(paymentCode)) {
            // 微信，支付宝方式
            try {
                sellerInfo = this.getWxAliSellerInfo(orderPO, paymentCode);
            } catch (Exception e) {
                log.error("WX ALIPAY 设置收款信息异常，订单号【{}】", orderPO.getOrderSn(), e);
            }
        } else if (PayMethodEnum.AGREED_PAY.getValue().equals(paymentCode)) {
            try {
                sellerInfo = this.getAgreedPaySellerInfo(orderPO);
            } catch (Exception e) {
                log.error("AGREED_PAY 设置收款信息异常，订单号【{}】", orderPO.getOrderSn(), e);
            }
        } else {
            log.error("支付成功回调，支付方式异常，支付方式编码：【{}】", paymentCode);
        }
        return sellerInfo;
    }

    /**
     * 设置用呗、随心取收款方信息
     *
     * @param orderPO 订单信息
     * @return 收款信息
     */
    private String getEnjoySellerInfo(OrderPO orderPO) {
        String sellerInfo = "";
        if (orderPO.getNewOrder()) {
            AccountCard accountCard = billOperatinIntegration.detailByBankAccount(AccountConstans.UNI_PLF_STORE_ID,
                    AccountCardTypeEnum.UNI_JS_PLF_SUP);
            sellerInfo = com.gexin.fastjson.JSON
                    .toJSONString(Maps.newHashMap("storeBankAccount", accountCard.getBankAccountNumber()));
        } else {
            StoreContractReceiptInfoVO storeContractRecommend =
                    storeFeignClient.getStoreContractReciptInfo(orderPO.getRecommendStoreId());
            OrderDetailInfo orderDetailInfo = new OrderDetailInfo();
            orderDetailInfo.setMerchantId(String.valueOf(orderPO.getStoreId()));
            orderDetailInfo.setMerchantPaymentCard(storeContractRecommend.getStore().getAcctId());

            QueryBankcardInfoReq req = new QueryBankcardInfoReq();
            req.setCardId("CAPP-" + storeContractRecommend.getStore().getAcctId());
            Result<BankcardVo> bankcardVoResult = customerServiceFeign.info(req);
            if (bankcardVoResult.isSuccess() && Objects.nonNull(bankcardVoResult.getData())) {
                sellerInfo = com.gexin.fastjson.JSON
                        .toJSONString(Maps.newHashMap("storeBankAccount", bankcardVoResult.getData().getCardNo()));
            }
        }
        return sellerInfo;
    }

    /**
     * 设置银行卡转账收款方信息
     *
     * @param orderPO 订单信息
     * @return 收款信息
     */
    private String getBankTransferSellerInfo(OrderPO orderPO) {
        if (Objects.nonNull(orderPO.getNewOrder()) && orderPO.getNewOrder()) {
            AccountCard accountCard = billOperatinIntegration.detailByBankAccount(AccountConstans.UNI_PLF_STORE_ID,
                    AccountCardTypeEnum.UNI_JS_PLF_SUP);
            return com.gexin.fastjson.JSON
                    .toJSONString(Maps.newHashMap("storeBankAccount", accountCard.getBankAccountNumber()));
        } else {
            LambdaQueryWrapper<BzBankTransferPO> bankTransferQuery = Wrappers.lambdaQuery(BzBankTransferPO.class);
            bankTransferQuery.eq(BzBankTransferPO::getPaySn, orderPO.getPaySn()).eq(BzBankTransferPO::getEnabledFlag,
                    OrderConst.ENABLED_FLAG_Y);
            List<BzBankTransferPO> bankTransfers = bzBankTransferService.list(bankTransferQuery);
            return com.gexin.fastjson.JSON
                    .toJSONString(Maps.newHashMap("storeBankAccount", bankTransfers.get(0).getReceiptAccount()));
        }
    }

    /**
     * 设置零元乡助卡支付收款方信息
     *
     * @param orderPO     订单信息
     * @param paymentCode 最终支付方式
     * @return 收款信息
     */
    private String getZeroXzCardSellerInfo(OrderPO orderPO, String paymentCode) {
        String sellerInfo = "";
        Long storeId = orderPO.getStoreId();
        if (orderPO.getOrderType().equals(OrderTypeEnum.ORDER_TYPE_7.getValue())) {
            storeId = orderPO.getRecommendStoreId();
        }
        sellerInfo = JSON.toJSONString(Maps.newHashMap("storeBankAccount",
                com.cfpamf.ms.mallorder.common.constant.CommonConst.KINGDEE_ZERO_ORDER_MARK));
        if (paymentCode.equals(com.cfpamf.ms.mallorder.common.constant.OrderPaymentConst.PAYMENT_CODE_CARD)) {
            JsonResult<AccountCard> accountCardResult = accountCardFacade.defaultCard(storeId);
            if (Objects.nonNull(accountCardResult.getData())) {
                sellerInfo = JSON.toJSONString(
                        Maps.newHashMap("xzkStoreBankAccount", accountCardResult.getData().getBankAccountNumber()));
            }
        }
        return sellerInfo;
    }

    /**
     * 设置微信、阿里收款方信息
     *
     * @param orderPO     订单信息
     * @param paymentCode 最终支付方式
     * @return 收款信息
     */
    private String getWxAliSellerInfo(OrderPO orderPO, String paymentCode) {
        String sellerInfo = "";
        String sellerId = "";
        if ((orderPO.getPayChannel().equals(PayChannelEnum.YZT_WX.getValue())
                || orderPO.getPayChannel().equals(PayChannelEnum.JS_WX.getValue()))
                && (PayMethodEnum.isWxAliPay(paymentCode))) {
            AccountQuery accountQuery = new AccountQuery();
            accountQuery.setStoreId(AccountConstans.UNI_PLF_STORE_ID);
            if (PayMethodEnum.isWxPay(paymentCode)) {
                accountQuery.setAccountType(AccountTypeEnum.WXPAY.getValue());
            } else if (PayMethodEnum.isAliPay(paymentCode)) {
                accountQuery.setAccountType(AccountTypeEnum.ALIPAY.getValue());
            }
            JsonResult<AccountVO> accountVOJsonResult = stmAccountFacade.detailV2(accountQuery);
            sellerId = accountVOJsonResult.getData().getSellerId();
        } else {
            StoreContractReceiptInfoVO storeContract =
                    storeFeignClient.getStoreContractReciptInfo(orderPO.getStoreId());
            String ali;
            String wx;
            // 订单类型为配销订单，则取连锁总店付款账户
            if (OrderConst.ORDER_TYPE_7 == orderPO.getOrderType()) {
                ali = storeContract.getRecommentAliSellerId();
                wx = storeContract.getRecommentWxSellerId();
            } else {
                ali = storeContract.getAliSellerId();
                wx = storeContract.getWxSellerId();
            }
            if (PayMethodEnum.ALIPAY.getValue().equals(paymentCode)) {
                sellerId = ali;
            } else {
                sellerId = wx;
            }
        }
        if (orderPO.getXzCardAmount().compareTo(BigDecimal.ZERO) > 0) {
            try {
                Long storeId = orderPO.getStoreId();
                if (orderPO.getOrderType().equals(OrderTypeEnum.ORDER_TYPE_7.getValue())) {
                    storeId = orderPO.getRecommendStoreId();
                }
                // 存在乡助卡支付，还需要乡助卡收款商家银行卡信息
                JsonResult<AccountCard> accountCardResult = accountCardFacade.defaultCard(storeId);
                if (Objects.nonNull(accountCardResult.getData())) {
                    sellerInfo = com.gexin.fastjson.JSON.toJSONString(Maps.newHashMap("storeBankAccount", sellerId,
                            "xzkStoreBankAccount", accountCardResult.getData().getBankAccountNumber()));
                }
            } catch (Exception e) {
                log.error("查询银行卡信息异常", e);
            }
        } else {
            sellerInfo = com.gexin.fastjson.JSON.toJSONString(Maps.newHashMap("storeBankAccount", sellerId));
        }
        return sellerInfo;
    }

    private String getAgreedPaySellerInfo(OrderPO orderPO) {
        List<OrderOfflinePO> data = orderOfflineService.queryOrderOfflineList(orderPO.getPaySn());
        if (!CollectionUtils.isEmpty(data) && ObjectUtils.isNotEmpty(data.get(0))) {
            return JSON.toJSONString(Maps.newHashMap("storeBankAccount", data.get(0).getReceiptAccount()));
        }
        log.warn("协议支付支付卡号解析异常，paySn:{}", orderPO.getPaySn());
        return null;
    }

    /**
     * @return void
     * @description : 处理付款失败
     */
    @GlobalTransactional
    public void orderPayFail(OrderPO order) {
        // 1.更改订单状态
        OrderPO updateOrderPO = new OrderPO();
        updateOrderPO.setOrderState(OrderStatusEnum.CANCELED.getValue());
        OrderExample orderExample = new OrderExample();
        orderExample.setOrderSn(order.getOrderSn());
        int count = orderMapper.updateByExampleSelective(updateOrderPO, orderExample);
        AssertUtil.isTrue(count == 0, "更新订单失败！");

        // 更新商品的已退数量
        orderProductService.updateProductReturnNumAfterCancel(order.getOrderSn());

        //2.记录退汇日志
        orderLogModel.insertOrderLog(OrderConst.LOG_ROLE_MEMBER, Long.valueOf(order.getMemberId()),
                order.getMemberName(), order.getOrderSn(), order.getOrderState(), OrderConst.ORDER_STATE_0,
                LoanStatusEnum.DEFAULT.getValue(), "订单付款失败，发生退汇", OrderCreateChannel.WEB);

        // 3.更新支付信息表
        OrderPayPO orderPayPO = new OrderPayPO();
        orderPayPO.setPaySn(order.getPaySn());
        orderPayPO.setApiPayState(OrderConst.API_PAY_STATE_0);
        orderPayPO.setCallbackTime(new Date());
        orderPayPO.setTradeSn(order.getOrderSn());
        orderPayPO.setPaymentName(order.getPaymentName());
        orderPayPO.setPaymentCode(order.getPaymentCode());
        count = orderPayMapper.updateByPrimaryKeySelective(orderPayPO);
        AssertUtil.isTrue(count == 0, "更新支付状态失败");

        // 5.增加货品库存，增加商品库存
        orderModel.orderCancelAddGoodsStock(order.getOrderSn(), order.getOrderType(), order.getAreaCode(),
                order.getFinanceRuleCode(), order);


        // 取消不涉及退款，发送取消通知
        orderCreateHelper.addOrderChangeEvent(order, OrderEventEnum.CANCEL, new Date());
    }

    /**
     * 拼团成功，更新该团所有订单状态为可发货
     *
     * @param payPromotionResult 营销模块返回结果
     * @param orderSn            订单号
     */
    private void paySuccessHandleOrderDelivery(JsonResult<OrderPaySuccessReturnVO> payPromotionResult, String orderSn,
                                               Integer orderType) {
        // 返回值校验
        if (Objects.isNull(payPromotionResult) || Objects.isNull(payPromotionResult.getData())
                || !payPromotionResult.getState().equals(200)
                || Objects.isNull(payPromotionResult.getData().getSpellFinish())) {
            return;
        }

        // 为拼团订单，且该订单已拼团成功
        if (orderType.equals(PromotionConst.PROMOTION_TYPE_102) && payPromotionResult.getData().getSpellFinish()) {
            // 查询拼团id
            LambdaQueryWrapper<OrderProductPO> orderProductQuery = new LambdaQueryWrapper<>();
            orderProductQuery.eq(OrderProductPO::getOrderSn, orderSn)
                    .eq(OrderProductPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y).select(OrderProductPO::getSpellTeamId);
            OrderProductPO productPO = orderProductMapper.selectOne(orderProductQuery);
            if (Objects.isNull(productPO.getSpellTeamId())) {
                log.warn("拼团ID不存在，订单号为：{}", orderSn);
                return;
            }

            // 反查该拼团的所有订单号
            LambdaQueryWrapper<OrderProductPO> productsQuery = new LambdaQueryWrapper<>();
            productsQuery.eq(OrderProductPO::getSpellTeamId, productPO.getSpellTeamId())
                    .eq(OrderProductPO::getEnabledFlag, OrderConst.ENABLED_FLAG_Y).select(OrderProductPO::getOrderSn);
            Set<String> orderSns = orderProductMapper.selectList(productsQuery).stream().map(OrderProductPO::getOrderSn)
                    .collect(Collectors.toSet());

            // 更新发货状态
            orderService.setOrdersDeliverable(new ArrayList<>(orderSns));
        }
    }

    /**
     * 支付成功赠送优惠券
     *
     * @param orderSn
     * @param memberId
     * @param memberName
     */
    private void sendCouponToMember(String orderSn, Integer memberId, String memberName) {
        OrderPromotionSendCouponExample sendCouponExample = new OrderPromotionSendCouponExample();
        sendCouponExample.setOrderSn(orderSn);
        List<OrderPromotionSendCouponPO> orderPromotionSendCouponPOS =
                orderPromotionSendCouponMapper.listByExample(sendCouponExample);
        if (CollectionUtils.isEmpty(orderPromotionSendCouponPOS)) {
            // 未赠送优惠券
            return;
        }
        for (OrderPromotionSendCouponPO orderPromotionSendCouponPO : orderPromotionSendCouponPOS) {// 查询优惠券信息
            CouponVO coupon = couponFeignClient.getCouponByCouponId(orderPromotionSendCouponPO.getCouponId());
            if (!coupon.getState().equals(CouponConst.ACTIVITY_STATE_1)) {
                // 优惠券不可用
                log.error("优惠券失效，不赠送，couponId:" + coupon.getCouponId());
                continue;
            }
            if (coupon.getEffectiveTimeType() == CouponConst.EFFECTIVE_TIME_TYPE_1
                    && new Date().after(coupon.getEffectiveEnd())) {
                // 固定起止时间，使用截止时间已过，优惠券不可用
                log.error("优惠券过期，不赠送，couponId:" + coupon.getCouponId());
                continue;
            }

            if (coupon.getReceivedNum().compareTo(coupon.getPublishNum()) >= 0) {
                // 优惠券已领完
                log.error("优惠券已领完，不赠送，couponId:" + coupon.getCouponId());
                continue;
            }

            // 赠送优惠券张数，不能超过发行数量-已领数量
            int sendNum =
                    Integer.min(orderPromotionSendCouponPO.getNumber(), coupon.getPublishNum() - coupon.getReceivedNum());
            for (int i = 0; i < sendNum; i++) {
                // 保存优惠券领取信息
                CouponMember couponMember = new CouponMember();
                couponMember.setCouponId(coupon.getCouponId());
                couponMember.setCouponCode(CouponCode.getKey());
                couponMember.setStoreId(coupon.getStoreId());
                couponMember.setMemberId(memberId);
                couponMember.setMemberName(memberName);
                couponMember.setReceiveTime(new Date());
                couponMember.setUseState(CouponConst.USE_STATE_1);
                if (coupon.getEffectiveTimeType() == CouponConst.EFFECTIVE_TIME_TYPE_2) {
                    couponMember.setEffectiveStart(new Date());
                    couponMember.setEffectiveEnd(TimeUtil.getDateApartDay(coupon.getCycle()));
                } else {
                    couponMember.setEffectiveStart(coupon.getEffectiveStart());
                    couponMember.setEffectiveEnd(coupon.getEffectiveEnd());
                }
                couponMember.setUseType(coupon.getUseType());
                if (coupon.getCouponType() == CouponConst.COUPON_TYPE_3) {
                    BigDecimal randomAmount = RedBagUtils.createRandomKey(coupon.getRandomMin(), coupon.getRandomMax());
                    couponMember.setRandomAmount(randomAmount);
                }
                couponMemberFeignClient.saveCouponMember(couponMember);

                // 记录优惠券领取日志
                CouponUseLog couponUseLog = new CouponUseLog();
                couponUseLog.setCouponCode(couponMember.getCouponCode());
                couponUseLog.setMemberId(memberId);
                couponUseLog.setMemberName(memberName);
                couponUseLog.setStoreId(coupon.getStoreId());
                couponUseLog.setLogType(CouponConst.LOG_TYPE_1);
                couponUseLog.setLogTime(new Date());
                couponUseLog.setLogContent("下单赠送优惠券，订单号：" + orderSn);
                couponUseLogFeignClient.saveCouponUseLog(couponUseLog);
            }

            // 更新优惠券领取数量
            Coupon updateCoupon = new Coupon();
            updateCoupon.setCouponId(coupon.getCouponId());
            updateCoupon.setReceivedNum(sendNum);
            couponFeignClient.updateOrderCoupon(updateCoupon);
        }

    }

    /**
     * 发送付款成功消息通知
     *
     * @param memberId  会员id
     * @param orderSn   订单号
     * @param payAmount 支付金额
     */
    public void sendMsgPaySuccess(Integer memberId, String memberName, Long storeId, String orderSn, BigDecimal payAmount) {
        List<OrderProductPO> orderProductPOS = orderProductModel.getOrderProductListByOrderSn(orderSn);

        //消息通知
        List<MessageSendProperty> messageSendPropertyList = new ArrayList<>();
        messageSendPropertyList.add(new MessageSendProperty("orderSn", orderSn));
        messageSendPropertyList.add(new MessageSendProperty("desc", orderProductPOS.get(0).getGoodsName()));
        messageSendPropertyList.add(new MessageSendProperty("payAmount", payAmount.toString()));
        // 微信消息通知
        List<MessageSendProperty> messageSendPropertyList4Wx = new ArrayList<>();
        String result = String.format("客户订单号%s，金额%s。", orderSn, payAmount);
        messageSendPropertyList4Wx.add(new MessageSendProperty("first", "【订单支付成功通知】"));
        messageSendPropertyList4Wx.add(new MessageSendProperty("keyword1", memberName));
        messageSendPropertyList4Wx.add(new MessageSendProperty("keyword2", "订单支付成功"));
        messageSendPropertyList4Wx.add(new MessageSendProperty("keyword3", result));
        messageSendPropertyList4Wx.add(new MessageSendProperty("keyword4", TimeUtil.getDateTimeString(new Date())));
        messageSendPropertyList4Wx.add(new MessageSendProperty("remark", "服务农村最后一百米"));
        String msgLinkInfo = "{\"orderSn\":\"" + orderSn + "\",\"type\":\"order_news\"}";
        MessageSendVO messageSendVO = new MessageSendVO(messageSendPropertyList, messageSendPropertyList4Wx,
                "paymentTime", memberId, MemberTplConst.PAYMENT_SUCCESS_REMINDER, msgLinkInfo);
        // 发送到mq
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_MEMBER_MSG, messageSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(messageSendVO), e);
        }



        //添加消息通知
        List<MessageSendProperty> messageSendPropertyListStore = new ArrayList<>();
        messageSendPropertyListStore.add(new MessageSendProperty("orderSn", orderSn));
        messageSendPropertyListStore.add(new MessageSendProperty("payAmount", payAmount.toString()));
        // 微信消息通知
        List<MessageSendProperty> messageSendPropertyListStore4Wx = new ArrayList<>();
        String resultStore = String.format("客户订单号%s，金额%s。", orderSn, payAmount);
        messageSendPropertyListStore4Wx.add(new MessageSendProperty("first", "【订单支付成功通知】"));
        messageSendPropertyListStore4Wx.add(new MessageSendProperty("keyword1", orderProductPOS.get(0).getStoreName()));
        messageSendPropertyListStore4Wx.add(new MessageSendProperty("keyword2", "客户下单"));
        messageSendPropertyListStore4Wx.add(new MessageSendProperty("keyword3", resultStore));
        messageSendPropertyListStore4Wx.add(new MessageSendProperty("keyword4", TimeUtil.getDateTimeString(new Date())));
        messageSendPropertyListStore4Wx.add(new MessageSendProperty("remark", "服务农村最后一百米"));
        String msgLinkInfoStore = "{\"type\":\"order_news\",\"orderSn\":\"" + orderSn + "\"}";
        MessageSendVO messageSendVoStore = new MessageSendVO(messageSendPropertyListStore, null,
                storeId, StoreTplConst.NEW_ORDER_REMINDER, msgLinkInfoStore);
        messageSendVoStore.setWxPropertyList(messageSendPropertyListStore4Wx);

        //发送到mq
        try {
            rabbitTemplate.convertAndSend(StarterConfigConst.MQ_EXCHANGE_NAME, StarterConfigConst.MQ_QUEUE_NAME_SELLER_MSG, messageSendVoStore);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(messageSendVoStore), e);
        }
    }

    /**
     * 发送余额变动消息通知
     *
     * @param member       会员信息
     * @param changeAmount 交易金额
     */
    public void sendMsgAccountChange(Member member, BigDecimal changeAmount) {
        // 消息通知
        List<MessageSendProperty> messageSendPropertyList = new ArrayList<>();
        messageSendPropertyList.add(new MessageSendProperty("description", "下单支付"));
        messageSendPropertyList
                .add(new MessageSendProperty("availableBalance", member.getBalanceAvailable().toString()));
        messageSendPropertyList.add(new MessageSendProperty("frozenBalance", member.getBalanceFrozen().toString()));
        // 微信消息通知
        List<MessageSendProperty> messageSendPropertyList4Wx = new ArrayList<>();
        messageSendPropertyList4Wx.add(new MessageSendProperty("first", "您的账户发生了资金变动。"));
        messageSendPropertyList4Wx.add(new MessageSendProperty("keyword1", "下单扣除余额"));
        messageSendPropertyList4Wx.add(new MessageSendProperty("keyword2", changeAmount.toString()));
        messageSendPropertyList4Wx.add(new MessageSendProperty("keyword3", TimeUtil.getDateTimeString(new Date())));
        messageSendPropertyList4Wx.add(new MessageSendProperty("keyword4", member.getBalanceAvailable().toString()));
        String msgLinkInfo = "{\"type\":\"balance_change\"}";
        MessageSendVO messageSendVO = new MessageSendVO(messageSendPropertyList, messageSendPropertyList4Wx,
                "changeTime", member.getMemberId(), MemberTplConst.BALANCE_CHANGE_REMINDER, msgLinkInfo);
        // 发送到mq
        try {
            rabbitTemplate.convertAndSend(MQ_EXCHANGE_NAME, MQ_QUEUE_NAME_MEMBER_MSG, messageSendVO);
        } catch (Exception e) {
            log.info("发消息异常捕获,{}", JSON.toJSONString(messageSendVO), e);
        }
    }

    @GlobalTransactional
    public void doLoanOperation(LoanLendingResult result) {
        // Andy.预售
        OrderPresellPO orderPresellPO =
                orderPresellService.queryBalanceInfoByPayNo(String.valueOf(result.getTradeNo()));
        String orderSn = ObjectUtils.isNotEmpty(orderPresellPO) ? orderPresellPO.getOrderSn() : String.valueOf(result.getTradeNo());
        log.info("信贷放款通知【doLoanOperation】doLoanOperation->orderSn：{}", orderSn);


        // 查询该订单
        OrderExample orderExampleQuery = new OrderExample();
        orderExampleQuery.setOrderSn(orderSn);
        List<OrderPO> orderPOList = orderModel.getOrderList(orderExampleQuery, null);
        OrderPO orderPODb = orderPOList.get(0);

        String successStr = LoanResultPO.LOAN_RESULT_Y, failStr = LoanResultPO.LOAN_RESULT_N;
        // 成功
        if (successStr.equals(result.getResult())) {
            // 根据orderSn更新该订单放款成功
            orderPODb.setLoanPayState(LoanStatusEnum.LENDING_SUCCESS.getValue());
            orderPODb.setLendingSuccessTime(result.getPaySuccessTime());
            orderModel.updateOrder(orderPODb);
            // order_log 放款成功
            orderLogModel.insertOrderLog(OrderConst.LOG_ROLE, OrderConst.LOG_USER_ID, OrderConst.LOG_USER_NAME,
                    orderPODb.getOrderSn(), orderPODb.getOrderState(), OrderConst.ORDER_STATE_60,
                    LoanStatusEnum.LENDING_SUCCESS.getValue(), "放款成功", OrderCreateChannel.WEB);

            // 放款成功，发送MQ消息通知
            orderCreateHelper.addOrderChangeEvent(orderPODb, OrderEventEnum.LENDING_SUCCESS,
                    result.getPaySuccessTime());

            // 查询paySn对应所有的order
            OrderExample orderExampleQueryByPasySn = new OrderExample();
            orderExampleQueryByPasySn.setPaySn(orderPODb.getPaySn());
            List<OrderPO> orderPOListByPaySn = orderModel.getOrderList(orderExampleQueryByPasySn, null);

            // 订单状态都为放款成功, 更新order_pay记录
            boolean allSuccess = true;
            for (OrderPO orderPO : orderPOListByPaySn) {
                // 如果所有订单中包含有未放款成功的订单，那么返回，不继续后续逻辑
                if (!orderPO.getLoanPayState().equals(LoanStatusEnum.LENDING_SUCCESS.getValue())) {
                    allSuccess = false;
                }
            }
            if (allSuccess) {
                OrderPayPO orderPayPO = orderPayService.getOne(Wrappers.lambdaQuery(OrderPayPO.class)
                        .eq(OrderPayPO::getPaySn, orderPODb.getPaySn()).last("limit 1"));
                if (Objects.nonNull(orderPayPO)) {
                    orderPayPO.setLoanSuccess(2);
                    orderPayService.updateById(orderPayPO);
                }
            }
            //放款成功,保存入金银行流水补偿任务
            taskQueueService.saveTaskQueue(result.getTradeNo(), TaskQueueBizTypeEnum.MAKE_UP_BANK_TRX_NO,
                    DateUtils.addMinutes(new Date(), 5), "system");

        } else if (failStr.equals(result.getResult())) {

            // 根据orderSn更新该订单放款失败
            orderPODb.setLoanPayState(LoanStatusEnum.LENDING_FAIL.getValue());
            orderModel.updateOrder(orderPODb);
            // 记录订单日志order_log,放款失败
            orderLogModel.insertOrderLog(OrderConst.LOG_ROLE, OrderConst.LOG_USER_ID, OrderConst.LOG_USER_NAME,
                    orderPODb.getOrderSn(), orderPODb.getOrderState(), OrderConst.ORDER_STATE_70,
                    LoanStatusEnum.LENDING_FAIL.getValue(), "放款失败", OrderCreateChannel.WEB);

            // 查询paySn对应所有的order
            OrderExample orderExampleQueryByPasySn = new OrderExample();
            orderExampleQueryByPasySn.setPaySn(orderPODb.getPaySn());
            List<OrderPO> orderPOListByPaySn = orderModel.getOrderList(orderExampleQueryByPasySn, null);

            // 如果所有的订单都放款失败，则更新order_pay为放款失败
            boolean allFail = true;
            for (OrderPO orderPO : orderPOListByPaySn) {
                if (orderPO.getLoanPayState().equals(LoanStatusEnum.LENDING_SUCCESS.getValue())) {
                    allFail = false;
                }
            }
            if (allFail) {
                OrderPayPO orderPayPO = orderPayService.getOne(Wrappers.lambdaQuery(OrderPayPO.class)
                        .eq(OrderPayPO::getPaySn, orderPODb.getPaySn()).last("limit 1"));
                if (Objects.nonNull(orderPayPO)) {
                    orderPayPO.setLoanSuccess(0);
                    orderPayService.updateById(orderPayPO);
                }
            }
        }

        // 记录放款结果日志
        this.dealCallbackLoanResult(result, orderPODb,successStr);

    }

    /**
     * 记录放款回调结果
     *  @param result  结果信息
     * @param orderPo 对应订单
     * @param successStr
     */
    private void dealCallbackLoanResult(LoanLendingResult result, OrderPO orderPo, String successStr) {
        LoanResultPO resultPo = new LoanResultPO();
        if (successStr.equals(result.getResult())) {
            resultPo.setPayNo(String.valueOf(result.getTradeNo()));
        }
        resultPo.setLoanResult(result.getResult());
        resultPo.setFailureReason(result.getFailureReason());
        resultPo.setCreateBy(TaskConstant.DEFAULT_JOB_NUMBER);
        resultPo.setUpdateBy(TaskConstant.DEFAULT_JOB_NUMBER);
        if (LoanResultPO.LOAN_RESULT_N.equals(result.getResult())) {
            resultPo.setFailureType(LoanFailureTypeEnum.CALL_BACK_FAILURE.getCode());
        }
        resultPo.setEnabledFlag(OrderConst.ENABLED_FLAG_Y);
        LambdaUpdateWrapper<LoanResultPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(LoanResultPO::getPayNo, orderPo.getOrderSn());
        loanResultService.saveOrUpdate(resultPo, updateWrapper);
    }

    /**
     * @param req
     * @return void
     * @description : 微信支付宝回调处理
     */
    @GlobalTransactional
    public void wxAlipayCallBack(PaymentNotifyVO req, PayInfoExtraInfoVO payInfoExtraInfoVO) {
        log.info("微信、支付宝、银行卡汇款，支付成功通知，orderOn：{}", req.getOrderOn());// Andy.预售修订
        String paySn = payService.buildPayNotifyOrderOn(req, payInfoExtraInfoVO, false);
        log.info("微信、支付宝、银行卡汇款，支付成功通知，paySn：{}", paySn); // Andy.预售修订
        OrderPayPO orderpayPO = getOrderPayByPaySn(paySn);
        if (OrderConst.API_PAY_STATE_1.equals(orderpayPO.getApiPayState())) {
            // 订单已支付，告警处理
            log.warn("订单已支付成功：{}，无需处理后续流程。", paySn);
            return;
        }
        OrderExample orderQueryExample = new OrderExample();
        orderQueryExample.setPaySn(paySn);
        List<OrderPO> orderPOList = orderModel.getOrderList(orderQueryExample, null);
        for (OrderPO orderPO : orderPOList) {
            OrderStatusEnum orderStatus = OrderStatusEnum.valueOf(orderPO.getOrderState());

            if (OrderStatusEnum.WAIT_PAY_DEPOSIT != orderStatus && OrderStatusEnum.WAIT_PAY != orderStatus
                    && OrderStatusEnum.DEAL_PAY != orderStatus) {
                log.warn("订单状态无需处理-{}状态为:{} paySn:{}", orderPO.getOrderSn(), orderStatus.getDesc(), orderPO.getPaySn());
                return;
            }
        }
        PayMethodEnum finalPayMethod = PayMethodEnum.getByPaymentCode(req.getPayWay());
        if (Objects.isNull(finalPayMethod)) {
            log.error("支付回调返回未知的payWay:{},orderOn:{},回调金额：{}", req.getPayWay(), req.getOrderOn(), req.getRelPayAmt());
            return;
        }
        PayChannelEnum payChannelEnum = PayChannelSourceEnum.convertPayChannelByPayment(req.getPayChannel());
        //更新新老订单标识
        boolean updateResult = this.updateNewOrderByPaySn(paySn, req.getNewOrder(), payChannelEnum);
        if (!updateResult) {
            throw new MallException("非贷款类支付回调时更新新老订单标识失败:{}", paySn);
        }

        if (1 == req.getPayStatus()) {
            Map<String, String> bankPayTrxNos = req.getBankPayTrxNos();

            // Andy预售
            if (ObjectUtils.isNotEmpty(payInfoExtraInfoVO) && ObjectUtils.isNotEmpty(payInfoExtraInfoVO.getPayNo())
                    && req.getRelPayAmt().compareTo(payService.payAmount(payInfoExtraInfoVO.getPayNo())) != 0) {
                log.error("订单支付金额不一致orderOn:{}，回调金额：{}", req.getOrderOn(), req.getRelPayAmt());
                return;
            }
            if ((ObjectUtils.isEmpty(payInfoExtraInfoVO) || ObjectUtils.isEmpty(payInfoExtraInfoVO.getPayNo()))
                    && req.getRelPayAmt().compareTo(this.getPayAmountDb(req.getOrderOn())) != 0) {
                log.error("订单支付金额不一致orderOn:{}，回调金额：{}", req.getOrderOn(), req.getRelPayAmt());
                return;
            }
            log.info("支付成功处理表结果更新： orderOn:【{}】 payInfoExtraInfoVO:{}", req.getOrderOn(), payInfoExtraInfoVO);
            payService.paySuccessProcess(orderpayPO.getPayId(), req, payInfoExtraInfoVO, bankPayTrxNos);

            String tradeSn = req.getPayCode();
            if (PayMethodEnum.BANK_TRANSFER == finalPayMethod) {
                bankTransferModel.transferSuccess(req);
                tradeSn = req.getPayTradeNo();
            }


            // 修改order 支付成功状态
            OrderExample example = new OrderExample();
            example.setPaySn(paySn);
            List<OrderPO> list = orderModel.getOrderList(example, null);

            for (OrderPO orderPO : list) {
                // 设置账户信息
                this.buildBankPayTrxNoAfterPaySuccess(bankPayTrxNos, orderPO);
                // 支付成功
                this.orderPaySuccess(orderPO, tradeSn, finalPayMethod.getValue(), finalPayMethod.getDesc(),
                        new Date(), ObjectUtils.isNotEmpty(payInfoExtraInfoVO) ? payInfoExtraInfoVO.getPayNo() : null);
            }
            return;
        }
        if (3 == req.getPayStatus()) {
            if (PayMethodEnum.BANK_TRANSFER == finalPayMethod) {
                bankTransferModel.transferExpire(req);
                return;
            }
            if (finalPayMethod.getValue().equals(orderpayPO.getPaymentCode())) {
                LambdaUpdateWrapper<OrderPO> orderPOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                orderPOLambdaUpdateWrapper.set(OrderPO::getOrderState, OrderConst.ORDER_STATE_10);
                orderPOLambdaUpdateWrapper.eq(OrderPO::getPaySn, paySn);
                orderPOLambdaUpdateWrapper.notIn(OrderPO::getOrderState, OrderConst.ORDER_STATE_5, OrderConst.ORDER_STATE_0);
                orderService.update(orderPOLambdaUpdateWrapper);
            }else{
                log.warn("paymentCodeNotEqualsWarn:{}", JSON.toJSONString(req));
            }

            // 预售额外处理，Andy.2022-05-31
            OrderExample example = new OrderExample();
            example.setPaySn(paySn);
            List<OrderPO> list = orderModel.getOrderList(example, null);
            if (list.get(0).getOrderType() == com.cfpamf.ms.mallpromotion.constant.PromotionConst.PROMOTION_TYPE_107) {
                orderLogModel.insertOrderLog(OrderConst.LOG_ROLE, OrderConst.LOG_USER_ID, OrderConst.LOG_USER_NAME,
                        paySn, OrderConst.ORDER_STATE_10, list.get(0).getOrderState(), LoanStatusEnum.APPLY_FAIL.getValue(),
                        "支付失败", OrderCreateChannel.WEB);
                return;
            }
            // order_log 支付失败
            orderLogModel.insertOrderLog(OrderConst.LOG_ROLE, OrderConst.LOG_USER_ID, OrderConst.LOG_USER_NAME, paySn,
                    OrderConst.ORDER_STATE_10, OrderConst.ORDER_STATE_10, LoanStatusEnum.APPLY_FAIL.getValue(), "支付失败", OrderCreateChannel.WEB);
            // 支付失败后，返回，不继续后续业务逻辑
            return;
        }
    }

    public boolean updateNewOrderByPaySn(String paySn, Boolean newOrder, PayChannelEnum payChannel) {
        if (Objects.isNull(payChannel)) {
            return true;
        }
        LambdaUpdateWrapper<OrderPO> updateWrapper = new LambdaUpdateWrapper<>();
        // if (Objects.nonNull(newOrder)) {
        //     updateWrapper.set(OrderPO::getNewOrder, newOrder);
        // }
        updateWrapper.set(OrderPO::getPayChannel, payChannel.getValue());
        updateWrapper.eq(OrderPO::getPaySn, paySn);
        return orderService.update(updateWrapper);
    }

    /**
     * 支付成功后，设置收款信息
     *
     * @param bankPayTrxNos 支付交易和订单对应关系
     * @param orderPO       订单号
     */
    private void buildBankPayTrxNoAfterPaySuccess(Map<String, String> bankPayTrxNos, OrderPO orderPO) {
        if (CollectionUtils.isEmpty(bankPayTrxNos)) {
            return;
        }
        if (PayMethodEnum.WXPAY.getValue().equals(orderPO.getPaymentCode()) && !orderPO.getNewOrder()) {
            orderPO.setBankPayTrxNo(bankPayTrxNos.get(orderPO.getOrderSn()));
        } else {
            orderPO.setBankPayTrxNo(bankPayTrxNos.get(orderPO.getPaySn()));
        }
    }

    /**
     * 获取三方支付实际应支付金额
     *
     * @param paySn
     * @return
     */
    private BigDecimal getPayAmountDb(String paySn) {
        // 查询订单
        OrderExample orderExample = new OrderExample();
        orderExample.setPaySn(paySn);
        List<OrderPO> list = orderModel.getOrderList(orderExample, null);
        // 三方支付实际应支付金额
        BigDecimal payAmount = BigDecimal.ZERO;
        for (OrderPO orderPO : list) {
            BigDecimal orderAmount =
                    orderPO.getOrderAmount().subtract(orderPO.getBalanceAmount()).subtract(orderPO.getPayAmount());
            payAmount = payAmount.add(orderAmount);
        }
        return payAmount;
    }

    /**
     * @param req
     * @return void
     * @description : 用呗支付结果回调处理
     */
    @GlobalTransactional
    public void enjoyPayCallBack(PaymentNotifyVO req, PayInfoExtraInfoVO payInfoExtraInfoVO) {
        log.info("贷款用呗等，支付成功通知，orderOn：{} xid:{}", req.getOrderOn(), RootContext.getXID());// Andy.预售修订
        String orderSn = payService.buildPayNotifyOrderOn(req, payInfoExtraInfoVO, true);
        log.info("贷款用呗等，支付成功通知，orderSn：{} payInfoExtraInfoVO:{}", orderSn, payInfoExtraInfoVO); // Andy.预售修订


        // 通过orderSn查询order, 查询orderPay
        OrderPO orderPODb = orderModel.getOrderByOrderSn(orderSn);
        OrderPayPO orderpayPO = getOrderPayByPaySn(orderPODb.getPaySn());

        this.updateNewOrderByPaySn(orderpayPO.getPaySn(), req.getNewOrder(), null);
        if (orderPODb.getLoanPayState() > LoanStatusEnum.DEAL_APPLY.getValue()) {
            // 订单已支付，告警处理
            log.error("订单放款状态!=申请中，无需处理后续流程。orderSn:{} payInfoExtraInfoVO:{}", orderPODb.getOrderSn(), payInfoExtraInfoVO);
            return;
        }

        // order订单状态判断重复支付，通知结果支付成功处理
        if (PayIntegration.PAY_CALLBACK_SUCCESS == req.getPayStatus()) {
            // Andy预售
            if (ObjectUtils.isNotEmpty(payInfoExtraInfoVO) && ObjectUtils.isNotEmpty(payInfoExtraInfoVO.getPayNo())
                    && req.getRelPayAmt().compareTo(payService.payAmount(payInfoExtraInfoVO.getPayNo())) != 0) {
                log.error("订单支付金额不一致orderOn:{}，回调金额：{}", req.getOrderOn(), req.getRelPayAmt());
                return;
            }
            if ((ObjectUtils.isEmpty(payInfoExtraInfoVO) || ObjectUtils.isEmpty(payInfoExtraInfoVO.getPayNo()))
                    && req.getRelPayAmt().compareTo(this.getOrderAmountDb(orderSn)) != 0) {
                log.error("贷款用呗等，支付成功通知，订单支付金额不一致paySn:{}，回调金额：{}", orderSn, req.getRelPayAmt());
                return;
            }
            log.info("贷款用呗等，支付成功通知，支付成功处理： orderOn:【{}】 payInfoExtraInfoVO:{}", req.getOrderOn(), payInfoExtraInfoVO);
            payService.paySuccessProcess(orderpayPO.getPayId(), req, payInfoExtraInfoVO, null);
            // 子订单支付成功处理
            PayMethodEnum finalPayMethod = PayMethodEnum.getByPaymentCode(req.getPayWay());
            this.orderPaySuccess(orderPODb, req.getPayTradeNo(), finalPayMethod.getValue(), finalPayMethod.getDesc(),
                    new Date(), payInfoExtraInfoVO.getPayNo());

            // 处理0元子订单
            orderService.dealZeroEnjoyPayOrder(orderPODb.getPaySn(), payInfoExtraInfoVO.getPayNo());
            //预付订单，将子单号作为老用户池中的订单号,该注释代码暂时保留
            // String orderNo = orderPODb.getOrderSn();
            // if (PayMethodEnum.isCombinationPay(payInfoExtraInfoVO.getPayMethod())) {
            //     orderNo = payInfoExtraInfoVO.getPayNo();
            // }
            //维护老用户池
            Boolean insertFirstLoanOrder = iBzOldUserPoolService.insertFirstLoanOrder(orderPODb.getUserNo(), orderPODb.getOrderSn());
            BizAssertUtil.isTrue(!insertFirstLoanOrder,"维护老用户池失败");
            // 金融规则订单、付款成功起息：进行放款
            if (PayMethodEnum.isLoanPay(finalPayMethod)) {
                OrderExtendFinancePO financePO = financeService.getByOrderSn(orderPODb.getOrderSn());
                log.info("financePO:{}", JSON.toJSONString(financePO));
                if ((Objects.nonNull(financePO)
                        && financePO.getInterestWay().equals(InterestType.CUSTOMER_CONFIRM.getValue()))
                        || (Objects.isNull(financePO) && SettleModeEnum.BORROW.getCode().equals(orderPODb.getSettleMode()))) {
                    Result<Void> voidResult = null;
                    // Andy.预售
                    BizAssertUtil.isTrue(OrderTypeEnum.isPresell(orderPODb.getOrderType()) &&
                            StringUtils.isEmpty(payInfoExtraInfoVO.getPayNo()), "抱歉，预售订单payNo为空，放款异常，请联系管理员！");
                    try {
                        voidResult =
                                orderModel.doLoanOperate(orderPODb.getOrderSn(), Long.valueOf(orderPODb.getMemberId()), payInfoExtraInfoVO.getPayNo());
                    } catch (Exception exception) {
                        // 系统异常导致放款失败，记录放款任务，通过定时任务补偿发起
                        log.warn("doLoanOperate()方法中调用pay服务失败，服务调用异常", exception);
                        //Andy.预售
                        taskQueueService.saveTaskQueue(OrderTypeEnum.isPresell(orderPODb.getOrderType()) ? Long.valueOf(payInfoExtraInfoVO.getPayNo()) :
                                        Long.valueOf(orderPODb.getOrderSn()),
                                TaskQueueBizTypeEnum.AUTO_LOAN, new Date(), TaskConstant.DEFAULT_JOB_NUMBER);
                        return;
                    }
                    if (!voidResult.isSuccess()) {
                        // 非系统异常导致放款失败，记录日志，人工处理
                        orderModel.loanPayFail(orderPODb, voidResult.getErrorMsg());
                    } else {
                        orderModel.loanPaySuccess(orderPODb, OrderConst.LOG_ROLE_MEMBER,
                                Long.valueOf(orderPODb.getMemberId()), orderPODb.getMemberName());
                    }
                }
                // 计划放款日起息
                if (Objects.nonNull(financePO)
                        && financePO.getInterestWay().equals(InterestType.PLAN_DATE_LOAN.getValue())) {
                    // 执行时间为计划日，执行类型为特定计划放款类型
                    taskQueueService.saveTaskQueue(Long.valueOf(orderPODb.getOrderSn()),
                            TaskQueueBizTypeEnum.PLAN_DATE_LOAN, financePO.getPlanLoanDate(),
                            TaskConstant.DEFAULT_JOB_NUMBER);
                }
                // 下单后N天起息
                if (Objects.nonNull(financePO)
                        && financePO.getInterestWay().equals(InterestType.N_DAYS_AFTER_PAY.getValue())) {
                    log.info("下单后N天起息:{}", financePO.getPlanInterestStartDays());
                    Date executeTime = null;
                    try {
                        if (!StringUtils.isEmpty(req.getChannelNotifyResult())) {
                            JSONObject channelNotifyResult = JSONObject.parseObject(req.getChannelNotifyResult());
                            executeTime = DateUtil.parse(channelNotifyResult.get("planLoanDate").toString());
                            log.info("finalExecuteTime={}", executeTime);
                        }
                    } catch (Exception e) {
                        log.warn("获取信贷下单后N天,计划放款日失败");
                    }

                    if(executeTime == null) {
                        Integer interestStartDays = financePO.getPlanInterestStartDays();
                        AssertUtil.notNull(interestStartDays, "下单后N天起息时指定日期为空");
                        executeTime = DateUtils.addDays(DateUtil.getNow(), interestStartDays);
                    }

                    //设置指定时间时分秒为  03:00:00
                    Date hExecuteTime = DateUtils.setHours(executeTime,3);
                    Date mExecuteTime = DateUtils.setMinutes(hExecuteTime, 0);
                    Date finalExecuteTime = DateUtils.setSeconds(mExecuteTime, 0);

                    // 执行时间为计划日，执行类型为特定计划放款类型
                    taskQueueService.saveTaskQueue(Long.valueOf(orderPODb.getOrderSn()),
                            TaskQueueBizTypeEnum.N_DAYS_AFTER_PAY, finalExecuteTime,
                            TaskConstant.DEFAULT_JOB_NUMBER);
                }
            }
            return;
        }
        // 通知结果支付失败处理
        if (PayIntegration.PAY_CALLBACK_FAIL == req.getPayStatus()) {
            // 1.关闭订单
            // 是否写日志标识
            boolean loggedMark = false;
            //andy.预付定金，未支付定金可以取消订单，且非107取消订单
            if (orderPODb.getOrderType() != com.cfpamf.ms.mallpromotion.constant.PromotionConst.PROMOTION_TYPE_107 ||
                    (orderPODb.getOrderType() == com.cfpamf.ms.mallpromotion.constant.PromotionConst.PROMOTION_TYPE_107
                            && orderPODb.getOrderState().equals(OrderStatusEnum.WAIT_PAY_DEPOSIT.getValue()))) {
                log.info("【enjoyPayCallBack】orderSn:{} , xid:{}", orderPODb.getOrderSn(), RootContext.getXID());
                orderModel.cancelOrder(Collections.singletonList(orderPODb), "用呗支付失败取消订单", null,
                        OrderConst.LOG_ROLE_ADMIN, OrderConst.LOG_USER_ID, OrderConst.LOG_USER_NAME, "用呗支付失败取消订单",
                        OrderConst.RETURN_BY_0);
                // 该处会记录日志
                loggedMark = true;
                log.warn("已付定金的预付订单不可取消，orderState:{} orderSn:{}", orderPODb.getOrderState(), orderPODb.getOrderSn());
            }

            // 2.更新用呗支付状态
            OrderPO updateOrder = new OrderPO();
            updateOrder.setOrderId(orderPODb.getOrderId());
            updateOrder.setLoanPayState(LoanStatusEnum.APPLY_FAIL.getValue());
            orderService.updateById(updateOrder);

            if (PayMethodEnum.isCombinationPay(orderPODb.getPaymentCode())) {
                log.info("组合支付支付失败不关闭【取消】订单无需写订单日志，orderSn：{}", orderPODb.getOrderSn());
                return;
            } else {
                if (!loggedMark) {
                    // 3.记录订单日志
                    orderLogModel.insertOrderLog(OrderConst.LOG_ROLE, OrderConst.LOG_USER_ID, OrderConst.LOG_USER_NAME,
                            orderPODb.getOrderSn(), orderPODb.getOrderState(), OrderConst.ORDER_STATE_0, LoanStatusEnum.APPLY_FAIL.getValue(),
                            "用呗订单支付失败-系统取消", OrderCreateChannel.WEB);
                }
            }

            // 处理0元子订单
            orderService.dealZeroEnjoyPayOrderFail(orderPODb.getPaySn());
            return;
        }
    }

    private BigDecimal getOrderAmountDb(String orderSn) {
        // 查询订单
        OrderExample orderExample = new OrderExample();
        orderExample.setOrderSn(orderSn);
        List<OrderPO> list = orderModel.getOrderList(orderExample, null);
        // 三方支付实际应支付金额
        BigDecimal payAmount = BigDecimal.ZERO;
        for (OrderPO orderPO : list) {
            payAmount = orderPO.getOrderAmount().subtract(orderPO.getBalanceAmount()).subtract(orderPO.getPayAmount());
        }
        return payAmount;
    }

    /**
     * @param
     * @return void
     * @description : 发送钉钉群消息
     */
    public void pushDingTalk(String msg, String atPhone) {
        // 发送钉钉群消息
        try {
            DingTalkClient client = new DefaultDingTalkClient(DING_TALK_URL);
            OapiRobotSendRequest request = new OapiRobotSendRequest();
            request.setMsgtype("text");
            OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
            text.setContent(msg);
            request.setText(text);
            OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
            at.setAtMobiles(Arrays.asList(atPhone));
            request.setAt(at);
            OapiRobotSendResponse response = client.execute(request);
            if (!response.isSuccess()) {
                log.error("钉钉机器人请求失败，请检查配置！失败原因：{}", response.getErrmsg() + response.getErrcode());
            }
        } catch (ApiException | com.taobao.api.ApiException e) {
            log.error("钉钉机器人请求失败，请检查配置！失败原因：{}", e);
        }
    }
}