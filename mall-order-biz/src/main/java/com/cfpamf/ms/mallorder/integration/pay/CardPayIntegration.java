package com.cfpamf.ms.mallorder.integration.pay;

import com.cdfinance.ms.card.facade.model.request.cardUse.*;
import com.cdfinance.ms.card.facade.model.response.cardUse.*;
import com.cdfinance.ms.card.facade.model.vo.PageResultVO;
import com.cdfinance.ms.card.facade.model.vo.PageVO;
import com.cfpamf.ms.mallorder.common.util.AuthUtils;
import com.cfpamf.ms.mallorder.common.util.ExternalApiUtil;
import com.cfpamf.ms.mallorder.integration.facade.CardUseFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @Create 2021-10-20 09:58
 * @Description :乡助卡支付接口
 */
@Slf4j
@Component
public class CardPayIntegration {

    @Autowired
    private CardUseFacade cardUseFacade;

    /**
     * @param userInfo
     * @return java.util.List<com.cdfinance.ms.card.facade.model.response.cardUse.CardUserQueryResponse>
     * @description :获取可用卡列表
     */
    public PageResultVO<CardUserQueryResponse> cardUseList(CardUseRequest pageVO, String userInfo) {
        return ExternalApiUtil.callResultApi(() -> cardUseFacade.cardUseList(pageVO, AuthUtils.encoderUserNo(userInfo)), pageVO,
                "/cardUse/customer/list", "获取可用乡助卡列表");
    }

    /**
     * @param request
     * @param userInfo
     * @return com.cdfinance.ms.card.facade.model.response.cardUse.CardTryPayResponse
     * @description : 卡片支付试算
     */
    public CardTryPayResponse tryPay(CardTryPayRequest request, String userInfo) {
        return ExternalApiUtil.callResultApi(() -> cardUseFacade.tryPay(request, AuthUtils.encoderUserNo(userInfo)), request,
                "/cardUse/pay/try", "乡助卡支付试算");
    }

    /**
     * @param request
     * @param userInfo
     * @return com.cdfinance.ms.card.facade.model.response.cardUse.CardPrePayResponse
     * @description :卡片支付冻结请求
     */
    public CardPrePayResponse frozenPay(CardPrePayRequest request, String userInfo) {
        return ExternalApiUtil.callResultApi(() -> cardUseFacade.frozenPay(request, AuthUtils.encoderUserNo(userInfo)), request,
                "/cardUse/pay/frozen", "乡助卡支付冻结请求");
    }

    /**
     * @param request
     * @param userInfo
     * @return com.cdfinance.ms.card.facade.model.response.cardUse.CardPayResponse
     * @description : 卡片支付成功请求
     */
    public CardPayResponse paySuccess(CardPayRequest request, String userInfo) {
        return ExternalApiUtil.callResultApi(() -> cardUseFacade.paySuccess(request, AuthUtils.encoderUserNo(userInfo)), request,
                "/cardUse/pay/success", "乡助卡支付成功请求");
    }

    /**
     * @param request
     * @param userInfo
    * @return com.cdfinance.ms.card.facade.model.response.cardUse.CardPayResponse
    * @description :卡片取消支付请求
    */
    public CardPayResponse payCancel(CardPayRequest request, String userInfo) {
        return ExternalApiUtil.callResultApi(() -> cardUseFacade.payCancel(request, AuthUtils.encoderUserNo(userInfo)), request,
                "/cardUse/pay/cancel", "乡助卡取消支付请求");
    }

    /**
     * @param request
     * @param userInfo
    * @return com.cdfinance.ms.card.facade.model.response.cardUse.CardPayResponse
    * @description :卡片退款请求
    */
    public CardPayResponse payRefund(CardRefundRequest request, String userInfo) {
        return ExternalApiUtil.callResultApi(() -> cardUseFacade.payRefund(request, AuthUtils.encoderUserNo(userInfo)), request,
                "/cardUse/pay/refund", "乡助卡退款请求");
    }

    /**
     * @param request
     * @param userInfo
    * @return com.cdfinance.ms.card.facade.model.response.cardUse.CardRefundCheckResponse
    * @description :卡片退款校验请求
    */
    public  CardRefundCheckResponse payRefundCheck(CardRefundCheckRequest request, String userInfo) {
        return ExternalApiUtil.callResultApi(() -> cardUseFacade.payRefundCheck(request, AuthUtils.encoderUserNo(userInfo)), request,
                "/cardUse/pay/refund/check", "乡助卡退款请求");
    }

}
