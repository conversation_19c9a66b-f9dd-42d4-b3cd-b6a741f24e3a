package com.cfpamf.ms.mallorder.controller.front;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cdfinance.ms.card.facade.model.response.cardUse.CardUserQueryResponse;
import com.cfpamf.cmis.common.constants.Constants;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.customer.facade.vo.user.UserBaseInfoVo;
import com.cfpamf.ms.loan.facade.request.external.mall.CdmallPreconditionCheckRequest;
import com.cfpamf.ms.loan.facade.vo.external.mall.MallContractCodesVo;
import com.cfpamf.ms.loan.facade.vo.external.mall.MallContractContentVO;
import com.cfpamf.ms.loan.facade.vo.external.mall.OrderInfoVo;
import com.cfpamf.ms.loan.facade.vo.loanBefore.apply.credit.RepaymentVo;
import com.cfpamf.ms.mall.account.api.AccountCardFacade;
import com.cfpamf.ms.mall.account.constant.AccountConstans;
import com.cfpamf.ms.mall.account.enums.AccountCardTypeEnum;
import com.cfpamf.ms.mall.account.vo.AccountCard;
import com.cfpamf.ms.mallmember.api.MemberFeignClient;
import com.cfpamf.ms.mallmember.api.MemberWxsignFeignClient;
import com.cfpamf.ms.mallmember.po.Member;
import com.cfpamf.ms.mallmember.po.MemberWxsign;
import com.cfpamf.ms.mallmember.request.MemberWxsignExample;
import com.cfpamf.ms.mallorder.builder.OrderBuilder;
import com.cfpamf.ms.mallorder.common.enums.*;
import com.cfpamf.ms.mallorder.common.exception.ErrorCodeEnum;
import com.cfpamf.ms.mallorder.common.help.ResultUtils;
import com.cfpamf.ms.mallorder.common.util.BizAssertUtil;
import com.cfpamf.ms.mallorder.constant.DomainUrlConstant;
import com.cfpamf.ms.mallorder.constant.OrderConst;
import com.cfpamf.ms.mallorder.dto.BizUserInfoDTO;
import com.cfpamf.ms.mallorder.dto.OrderSubmitDTO;
import com.cfpamf.ms.mallorder.dto.WxPayV3ShardPrice;
import com.cfpamf.ms.mallorder.enums.OrderPlaceUserRole;
import com.cfpamf.ms.mallorder.enums.OrderTypeEnum;
import com.cfpamf.ms.mallorder.enums.PayWayShowStatusEnum;
import com.cfpamf.ms.mallorder.integration.cust.CustomerIntegration;
import com.cfpamf.ms.mallorder.integration.loan.LoanPayIntegration;
import com.cfpamf.ms.mallorder.integration.pay.PayIntegration;
import com.cfpamf.ms.mallorder.integration.settlement.BillOperatinIntegration;
import com.cfpamf.ms.mallorder.model.BankTransferModel;
import com.cfpamf.ms.mallorder.model.OrderModel;
import com.cfpamf.ms.mallorder.model.OrderPayModel;
import com.cfpamf.ms.mallorder.model.OrderProductModel;
import com.cfpamf.ms.mallorder.po.*;
import com.cfpamf.ms.mallorder.req.*;
import com.cfpamf.ms.mallorder.request.OrderExample;
import com.cfpamf.ms.mallorder.request.OrderProductExample;
import com.cfpamf.ms.mallorder.service.*;
import com.cfpamf.ms.mallorder.v2.service.OrderPresellService;
import com.cfpamf.ms.mallorder.validation.PayValidation;
import com.cfpamf.ms.mallorder.vo.*;
import com.cfpamf.ms.mallpromotion.api.PromotionCommonFeignClient;
import com.cfpamf.ms.mallpromotion.constant.PromotionConst;
import com.cfpamf.ms.mallshop.api.StoreFeignClient;
import com.google.common.collect.Maps;
import com.slodon.bbc.core.constant.MemberWxSignConst;
import com.slodon.bbc.core.constant.WebConst;
import com.slodon.bbc.core.exception.BusinessException;
import com.slodon.bbc.core.exception.MallException;
import com.slodon.bbc.core.response.JsonResult;
import com.slodon.bbc.core.response.PageVO;
import com.slodon.bbc.core.response.SldResponse;
import com.slodon.bbc.core.util.*;
import io.swagger.annotations.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 订单支付controller
 */
@RestController
@RequestMapping("front/orderPay")
@Api(tags = "front-订单支付")
@Slf4j
public class FrontOrderPayController {

    @Resource
    private OrderPayModel orderPayModel;
    @Resource
    private OrderProductModel orderProductModel;
    @Resource
    private OrderModel orderModel;
    @Resource
    private BankTransferModel bankTransferModel;
    @Resource
    private MemberFeignClient memberFeignClient;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private MemberWxsignFeignClient memberWxsignFeignClient;
    @Autowired
    private PayIntegration payIntegration;
    @Resource
    private PromotionCommonFeignClient promotionCommonFeignClient;
    @Autowired
    private IOrderService orderService;
    @Autowired
    private IOrderPayService orderPayService;
    @Resource
    private IOrderExtendService orderExtendService;
    @Autowired
    private IPayMethodService iPayMethodService;
    @Autowired
    private IOrderPayService iOrderPayService;
    @Autowired
    private IPayMethodService payMethodService;
    @Autowired
    private LoanPayIntegration loanPayIntegration;
    @Autowired
    private ITaskQueueService taskQueueService;
    @Autowired
    private AccountCardFacade accountCardFacade;
    @Autowired
    private StoreFeignClient storeFeignClient;
    @Autowired
    private BillOperatinIntegration billOperatinIntegration;

    @Autowired
    private OrderPresellService orderPresellService;

    @Autowired
    private IOrderReturnService orderReturnService;

    @Resource
    private CustomerIntegration customerIntegration;

    @Value("${presell.paymethods.deposit}")
    private String depositPayMethods;
    @Value("${presell.paymethods.remain}")
    private String remainPayMethods;

    /**
     * 查询用呗/随心取可用还款方式
     *
     * @param request
     * @param paySn
     * <AUTHOR>
     * @date 2021/11/03 17:51
     */
    @GetMapping("payMethod/listRepayment")
    @ApiOperation("查询用呗/随心取可用还款方式")
    @ApiImplicitParams({@ApiImplicitParam(
            name = WebConst.USER_HEADER, value = "客户信息 由网关处理", paramType = "header", dataType = "String")
    })
    public JsonResult<List<RepaymentVo>> listRepaymentList(HttpServletRequest request,
                                                           @RequestParam(value = "payMethod", required = true)
                                                           @NotBlank(message = "payMethod 不能为空") String payMethod,
                                                           @RequestParam(value = "paySn", required = true)
                                                           @NotBlank(message = "paySn 不能为空") String paySn,
                                                           @RequestParam(value = "loanPeriod", required = true)
                                                           @NotBlank(message = "loanPeriod 不能为空") Integer loanPeriod,
                                                           @RequestParam(value = "utdid") String utdid,
                                                           @RequestParam(value = "imei") String imei,
                                                           @RequestParam(value = "mobiletype") String mobiletype
    ) {

        Member member = UserUtil.getUser(request, Member.class);

        PayMethodEnum payMethodEnum = PayMethodEnum.getValue(payMethod);
        BizAssertUtil.isTrue(payMethodEnum == null, "未识别的支付方式:" + payMethod);

        BizAssertUtil.isTrue(!PayMethodEnum.isLoanPay(payMethodEnum), "不是信贷支付方式:" + payMethod);

        LambdaQueryWrapper<PayMethodPO> payMethodQuery = new LambdaQueryWrapper();
        payMethodQuery.eq(PayMethodPO::getPayMethodCode, payMethodEnum.getValue());
        payMethodQuery.eq(PayMethodPO::getPayMethodStatus, PayMethodPO.STATUS_ON);

        PayMethodPO enjoyPayMethod = payMethodService.getOne(payMethodQuery);
        BizAssertUtil.isTrue(enjoyPayMethod == null, "支付方式不存在,请检查:" + payMethod);

        OrderPayPO orderPayPO = orderPayModel.getOrderPayByPaySn(paySn);

        LambdaQueryWrapper<OrderPO> orderPOLambdaQueryWrapper = new LambdaQueryWrapper();
        orderPOLambdaQueryWrapper.eq(OrderPO::getPaySn, paySn);
        List<OrderPO> orderPOS = orderService.list(orderPOLambdaQueryWrapper);

        List<OrderInfoVo> orderInfoList = new ArrayList<>(orderPOS.size());
        for (OrderPO orderPO1 : orderPOS) {
            OrderInfoVo orderInfoVo = new OrderInfoVo();
            orderInfoVo.setOrderId(orderPO1.getOrderSn());
            orderInfoVo.setRcmdMerchant(String.valueOf(orderPO1.getRecommendStoreId()));
            orderInfoVo.setOrderAmt(orderPO1.getOrderAmount());
            orderInfoList.add(orderInfoVo);
        }

        CdmallPreconditionCheckRequest preconditionCheckRequest = new CdmallPreconditionCheckRequest();

        preconditionCheckRequest.setOrderInfoList(orderInfoList);

        OrderExtendPO orderExtendPO = orderExtendService
                .getOrderExtendByOrderSn(orderPOS.get(0).getOrderSn());
        preconditionCheckRequest.setBizId(paySn);
        preconditionCheckRequest.setLoanPeriod(loanPeriod);
        preconditionCheckRequest.setCustId(member.getCustNo());
        preconditionCheckRequest.setProductCode(enjoyPayMethod.getLoanCode());
        preconditionCheckRequest.setBranchCode(orderExtendPO.getBranch());
        preconditionCheckRequest.setWithdrawAmout(orderPayPO.getPayAmount());
        preconditionCheckRequest.setUtdid(utdid);
        preconditionCheckRequest.setImei(imei);
        preconditionCheckRequest.setMobiletype(mobiletype);
        preconditionCheckRequest.setIp(WebUtil.getRealIp(request));
        preconditionCheckRequest.setProvince(orderExtendPO.getReceiverProvinceCode());
        preconditionCheckRequest.setCity(orderExtendPO.getReceiverCityCode());
        preconditionCheckRequest.setAddress(orderExtendPO.getReceiverAddress());
        preconditionCheckRequest.setExtendInfo(Maps.newHashMap());
        preconditionCheckRequest.setOrderInfoList(orderInfoList);

        return SldResponse.success(loanPayIntegration.listLoanRepayment(preconditionCheckRequest));
    }

    /**
     * 可用支付方式列表查询<br />
     * 当前支持：支付渠道、门槛金额、店铺
     *
     * @param request
     * @param paySn
     * <AUTHOR>
     * @date 2021/6/26 17:51
     */
    @GetMapping("payMethod/V2")
    @ApiOperation("查询可用支付方式列表")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = WebConst.USER_HEADER,
                    value = "客户信息 由网关处理",
                    paramType = "header",
                    dataType = "String")
    })
    public JsonResult<List<PayMethodVO>> payMethodV2(HttpServletRequest request,
                                                     @RequestParam(value = "paySn") String paySn,
                                                     @RequestParam(value = "channel") OrderCreateChannel channel,
                                                     @RequestParam(value = "utdid", required = false) String utdid,
                                                     @RequestParam(value = "imei", required = false) String imei,
                                                     @RequestParam(value = "mobiletype", required = false) String mobiletype
    ) {
        Member member = UserUtil.getUser(request, Member.class);
        List<PayMethodVO> sortVo = iPayMethodService.getPayMethodVOS(paySn, channel, member, mobiletype, utdid, imei, request);
        return SldResponse.success(sortVo);
    }

    /**
     * 可用支付方式列表查询<br />
     * 当前支持：支付渠道、门槛金额、店铺
     *
     * @param request
     * @param paySn
     * <AUTHOR>
     * @date 2021/6/26 17:51
     */
    @GetMapping("payMethod/V3")
    @ApiOperation(value = "查询可用支付方式列表", tags = "CORE")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = WebConst.USER_HEADER,
                    value = "客户信息 由网关处理",
                    paramType = "header",
                    dataType = "String")
    })
    public JsonResult<List<PayMethodVO3>> payMethodV3(HttpServletRequest request,
                                                      @RequestParam(value = "paySn") String paySn,
                                                      @RequestParam(value = "channel") OrderCreateChannel channel,
                                                      @RequestParam(value = "utdid", required = false) String utdid,
                                                      @RequestParam(value = "imei", required = false) String imei,
                                                      @RequestParam(value = "wifi", required = false) String wifi,
                                                      @RequestParam(value = "mobiletype", required = false) String mobiletype,
                                                      @RequestParam(value = "memberId", required = false) Integer memberId,
                                                      @RequestParam(value = "returnUrl", required = false) String returnUrl,
                                                      @RequestParam(value = "chan_nel", required = false) String chan_nel,
                                                      @RequestParam(value = "ctversion", required = false) String ctversion,
                                                      @RequestParam(value = "checkAuthVersion", required = false) String checkAuthVersion

    ) {
        Member member = UserUtil.getUser(request, Member.class);
        if ("undefined".equals(paySn)) {
            throw new BusinessException("支付单号不能为undefined!");
        }
        List<PayMethodVO3> sortVo = iPayMethodService.getPayMethodVOS3(paySn, channel, member, mobiletype, utdid, imei, request,wifi,returnUrl,chan_nel,ctversion,checkAuthVersion);
        List<PayMethodVO3> payMethodVOS = sortVo.stream().sorted(Comparator.comparing(PayMethodVO3::getShowStatus).thenComparing(PayMethodVO3::getSort).thenComparing(PayMethodVO3::getRecommendSort)).collect(Collectors.toList());
        return SldResponse.success(payMethodVOS);
    }

    @ApiOperation(value = "预览合同")
    @PostMapping("/contractPreview")
    public JsonResult<MallContractContentVO> contractPreview(@RequestBody @NotNull @Valid OrderPayRequest request) {
        return SldResponse.success(iOrderPayService.contractPreview(request));
    }

    @ApiOperation(value = "获取合同列表", produces = Constants.CONTENT_TYPE_JSON)
    @PostMapping("/listLoanContractCode")
    public JsonResult<MallContractCodesVo> listLoanContractCode(@RequestBody @NotNull @Valid OrderPayRequest request) {
        return SldResponse.success(iOrderPayService.listLoanContractCode(request));
    }

    @GetMapping("payInfo")
    @ApiOperation(value = "支付页面信息接口,状态码267==订单已支付")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = WebConst.USER_HEADER, value = "客户信息 由网关处理",
                    paramType = "header", dataType = "String"),
            @ApiImplicitParam(name = "paySn", value = "支付单号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "payFrom", defaultValue = "1", paramType = "query",
                    value = "支付来源，默人1==下单跳转支付，可以使用余额；2==订单列表跳转支付，不可使用余额"),
            @ApiImplicitParam(name = "payType",  paramType = "query",value = "支付类型，1=订金")
    })
    public JsonResult<OrderPayInfoVO> payInfo(HttpServletRequest request,
                                              @RequestParam(value = "paySn") String paySn,
                                              @RequestParam(value = "payFrom", required = false, defaultValue = "1") Integer payFrom,
                                              @RequestParam(value = "payType", required = false) Integer payType) {
        Member member = UserUtil.getUser(request, Member.class);
        OrderPayInfoVO vo = new OrderPayInfoVO();

        //查询支付单是否生成
        OrderPayPO orderPayPO = orderPayModel.getOrderPayByPaySn(paySn);
        if (orderPayPO == null) {
            //未生成订单支付信息，查询mq处理结果
            String mqResult = stringRedisTemplate.opsForValue().get(OrderConst.ORDER_SUBMIT_MQ_REDIS_PREFIX + paySn);
            if (mqResult == null) {
                //redis中的订单入队信息已删除，订单生成失败
                vo.setDealState(OrderConst.ORDER_SUBMIT_DEAL_STATE_2);
                return SldResponse.success(vo);
            }
            //redis中的订单入队信息存在，还在排队处理中
            vo.setDealState(OrderConst.ORDER_SUBMIT_DEAL_STATE_1);
            return SldResponse.success(vo);
        }

        //支付单已生成，可以去支付
        vo.setDealState(OrderConst.ORDER_SUBMIT_DEAL_STATE_3);
        //查询支付单号下所有待支付的订单
        OrderExample orderExample = new OrderExample();
        orderExample.setPaySn(paySn);
        orderExample.setOrderStateIn(OrderConst.ORDER_STATE_5 +","+OrderConst.ORDER_STATE_10);
        orderExample.setMemberId(member.getMemberId());
        List<OrderPO> orderPOList = orderModel.getOrderList(orderExample, null);

        vo.setOrderListAll(JSONObject.toJSONString(orderPOList));
        vo.setPaySn(paySn);
        vo.setPaymentCode(orderPayPO.getPaymentCode());
        vo.setPaymentName(orderPayPO.getPaymentName());
        // 设置支付状态
        if (CollectionUtils.isEmpty(orderPOList)) {
            vo.setPayState(PayStatusEnum.SUCCESS.getValue());
            return SldResponse.success(vo);
        } else {
            vo.setPayState(PayStatusEnum.WAIT_PAY.getValue());
            vo.setChannel(orderPOList.get(0).getChannel());
        }

        // 支付扩展信息
        JSONObject payWayExtraInfo = orderPayPO.getPayWayExtraInfo();
        if (!Objects.isNull(payWayExtraInfo) && !payWayExtraInfo.isEmpty()){
            if (PayMethodEnum.isLoanPay(orderPayPO.getPaymentCode())) {
                OrderPayInfoVO.EnjoyPayExtraInfo enjoyPayExtraInfo = JSON.parseObject(
                        payWayExtraInfo.toJSONString(), OrderPayInfoVO.EnjoyPayExtraInfo.class);
                vo.setEnjoyPayExtraInfo(enjoyPayExtraInfo);
            }
            if (PayMethodEnum.BANK_TRANSFER == PayMethodEnum.getValue(orderPayPO.getPaymentCode())) {
                OrderPayInfoVO.BankTransferInfo bankTransferInfo = JSON.parseObject(
                        payWayExtraInfo.toJSONString(), OrderPayInfoVO.BankTransferInfo.class);
                vo.setBankTransferInfo(bankTransferInfo);
            }
        }

        // 支付倒计时
        OrderPO po = orderPOList.get(0);
        Integer minutes = orderModel.getAutoCancelMinutes(po.getOrderPattern(), po.getOrderType());
//        String value = stringRedisTemplate.opsForValue().get("time_limit_of_auto_cancle_order");
//        int autoCancelDay = value == null ? 24 : Integer.parseInt(value);
//        Date autoCancelTime = TimeUtil.getHourAgoDate(orderPayPO.getCreateTime(), autoCancelDay);
        // 计算时间差毫秒数
        Date now = new Date();
        long createTimeMills = po.getCreateTime().getTime();
        long time = createTimeMills + TimeUnit.MINUTES.toMillis(minutes) - now.getTime();
        if (time > 0){
            // 转换为秒
            vo.setPayTimeLimit(TimeUnit.MILLISECONDS.toSeconds(time));
        }

        for (OrderPO order : orderPOList) {
            vo.setOrderAmount(vo.getOrderAmount().add(order.getOrderAmount()));
            vo.setAlreadyPay(vo.getAlreadyPay().add(order.getBalanceAmount()).add(order.getPayAmount()));
            //查询货品列表
            OrderProductExample orderProductExample = new OrderProductExample();
            orderProductExample.setOrderSn(order.getOrderSn());
            List<OrderProductPO> orderProductPOList = orderProductModel.getOrderProductList(orderProductExample, null);
            orderProductPOList.forEach(orderProduct -> {
                vo.getGoodsNameList().add(orderProduct.getGoodsName());
            });
        }

        vo.setUserMobile(orderPOList.get(0).getUserMobile());
        // 查询扩展表信息
        OrderExtendPO orderExtend = orderExtendService.getOrderExtendByOrderSn(orderPOList.get(0).getOrderSn());
        vo.setReceiveAddress(orderExtend.getReceiverAreaInfo() + orderExtend.getReceiverAddress());
        vo.setReceiverName(orderExtend.getReceiverName());
        List<String> hiddenHosts = Arrays.asList("https://jbbcadmin.slodon.cn", "http://jbbcs-admin.slodon.cn");
        if (hiddenHosts.contains(DomainUrlConstant.SLD_ADMIN_URL)) {
            vo.setReceiverMobile(orderExtend.getReceiverMobile().replaceFirst("(\\d{3})\\d{4}(\\d{4})", "$1****$2"));
        }

        vo.setPayerName(orderExtend.getCustomerName());

        try {
            //获取用户信息
            UserBaseInfoVo userBaseInfoVo = customerIntegration.userBaseInfo(member.getUserNo());
            List<String> loanPlayerList = orderPOList.stream().map(x -> x.getLoanPayer()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(loanPlayerList)
                    && loanPlayerList.contains(LoanPayerEnum.STATION_MASTER.getCode())
                    && StringUtils.isNotEmpty(orderExtend.getStationMaster())) {
                //查询客户中心站长信息
                userBaseInfoVo = customerIntegration.userBaseInfo(orderExtend.getStationMaster());

            }
            if (Objects.nonNull(userBaseInfoVo)
                    && Objects.nonNull(userBaseInfoVo.getCustInfoVo())) {
                vo.setPayerName(userBaseInfoVo.getCustInfoVo().getCustName());
                //身份证号base64编码
                vo.setIdNo(Base64.getEncoder().encodeToString(userBaseInfoVo.getCustInfoVo().getIdNo().getBytes()));
                vo.setUserNo(userBaseInfoVo.getUserNo());
            }
        } catch (Exception e) {
            log.warn("收银台页获取客户中心用户信息失败，paySn = {}", paySn);
        }


        vo.setCanUseBalance(payFrom == 1);//下单跳转支付，可以使用余额
        vo.setOrderList(JSONObject.toJSONString(orderPOList.stream().filter(x -> !StringUtil.isEmpty(x.getFinanceRuleCode())).collect(Collectors.toList())));

        //预付订金特殊处理
        iOrderPayService.dealPreSellResult(orderPOList.get(0).getOrderSn(), payType, vo);
        return SldResponse.success(vo);
    }


    @GetMapping("payMethod")
    @ApiOperation("获取可用的支付方式接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "source", value = "支付发起来源 pc==pc,mbrowser==移动设备浏览器,app==app,wxxcx==微信小程序,wxbrowser==微信内部浏览器",
                    required = true, paramType = "query"),
            @ApiImplicitParam(name = "type", value = "支付发起类型，1==订单支付，2==充值", required = true, paramType = "query")
    })
    public JsonResult<List<PayMethodVO>> payMethod(HttpServletRequest request,
                                                   @RequestParam(value = "source") String source,
                                                   @RequestParam(value = "type") Integer type) {
        List<PayMethodVO> vos = new ArrayList<>();

        // 支付方式列表
        PayMethodVO vo1 = new PayMethodVO();
        vo1.setPayMethod(PayMethodEnum.ALIPAY.getValue());
        vo1.setPayMethodName(PayMethodEnum.ALIPAY.getDesc());
        vo1.setShowStatus(PayWayShowStatusEnum.ENABLE);

        PayMethodVO vo2 = new PayMethodVO();
        vo2.setPayMethod(PayMethodEnum.WXPAY.getValue());
        vo2.setPayMethodName(PayMethodEnum.WXPAY.getDesc());
        vo2.setShowStatus(PayWayShowStatusEnum.ENABLE);

        PayMethodVO vo3 = new PayMethodVO();
        vo3.setPayMethod(PayMethodEnum.ENJOY_PAY.getValue());
        vo3.setPayMethodName(PayMethodEnum.ENJOY_PAY.getDesc());
        vo3.setShowStatus(PayWayShowStatusEnum.ENABLE);

        vos.add(vo1);
        vos.add(vo2);
        vos.add(vo3);

        return SldResponse.success(vos);
    }

    @GetMapping("payPwdCheck")
    @ApiOperation("选择余额支付校验是否设置了支付密码接口")
    @ApiResponses(
            @ApiResponse(code = 200, message = "data:true==设置了支付密码；false==未设置支付密码")
    )
    public JsonResult payPwdCheck(HttpServletRequest request) {
        Member member = UserUtil.getUser(request, Member.class);
        Member memberDb = memberFeignClient.getMemberByMemberId(member.getMemberId());
        return SldResponse.success(!StringUtils.isEmpty(memberDb.getPayPwd()));
    }

    @PostMapping("payWayChange")
    @ApiOperation(value = "更改支付方式")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = WebConst.USER_HEADER, value = "客户信息 由网关处理",
                    paramType = "header", dataType = "String")
    })
    public JsonResult<Boolean> payWayChange(HttpServletRequest request, @RequestBody @NotNull @Valid PayWayChangeRequest payRequest) {
        Member member = UserUtil.getUser(request, Member.class);

        return SldResponse.success(orderPayModel.payWayChange(member, payRequest));
    }

    @GetMapping("setUpSettleChannel")
    @ApiOperation(value = "设置结算方式")
    public JsonResult<Boolean> setUpSettleChannel(@RequestParam @NotNull String paySn) {

        return SldResponse.success(orderPayModel.setUpSettleChannel(paySn));
    }

    @SneakyThrows
    @PostMapping("doPay")
    @ApiOperation(value = "去支付接口")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = WebConst.USER_HEADER, value = "客户信息 由网关处理",
                    paramType = "header", dataType = "String"),
            @ApiImplicitParam(
                    name = "X-Real-IP",
                    value = "请求来源IP",
                    paramType = "header",
                    dataType = "String")
    })
    public JsonResult<PayRequestVO> doPay(HttpServletRequest request,
                                          @RequestBody @NotNull @Valid PayOrderRequest payRequest) {
        Member member = UserUtil.getUser(request, Member.class);
        BizAssertUtil.notNull(member, "member客户信息为空");
        log.info("待支付订单member====>>{}", JSON.toJSONString(member));
        Member memberDb = memberFeignClient.getMemberByMemberId(member.getMemberId());
        // 获取客户经理信息
        BizUserInfoDTO bizUserInfoDTO = OrderBuilder.getUser(request, BizUserInfoDTO.class);
        log.info("{}用呗支付，客户经理信息：{}", payRequest.getPaySn(), JSONObject.toJSONString(bizUserInfoDTO));

        //获取站长信息
        Member stationMaster = OrderBuilder.getStationMasterUser(request);
        log.info("{}用呗支付，站长信息：{}", payRequest.getPaySn(), JSONObject.toJSONString(stationMaster));

        String payType = payRequest.getPayType();

        //余额支付时密码校验
        if (PayMethodEnum.BALANCE == payRequest.getPayMethod() || payType.contains("BALANCE")) {
            //使用余额，密码必传
            AssertUtil.notEmpty(payRequest.getPayPwd(), "请输入支付密码");
            //校验密码
            AssertUtil.isTrue(!Md5.getMd5String(payRequest.getPayPwd()).equals(memberDb.getPayPwd()), "支付密码有误");
        }

        //查询支付信息
        OrderPayPO orderPayPO = orderPayModel.getOrderPayByPaySn(payRequest.getPaySn());

        //查询支付单号下所有待支付的订单
        OrderExample orderExample = new OrderExample();
        orderExample.setPaySn(payRequest.getPaySn());
        orderExample.setMemberId(member.getMemberId());
        orderExample.setOrderStateIn(OrderConst.ORDER_STATE_5 + "," + OrderConst.ORDER_STATE_10);
        List<OrderPO> orderPOList = orderModel.getOrderList(orderExample, null);

        // 订单支付校验
        orderPayModel.orderPayCheck(orderPayPO, member, bizUserInfoDTO, stationMaster, orderPOList, payRequest.getPayMethod());

        // 组合支付，订单支付方式不允许修改。预售Andy修改
        if (!PayMethodEnum.isCombinationPay(orderPayPO.getPaymentCode())) {
            // 修改orderPay支付信息
            OrderPayPO updateOrderPay = new OrderPayPO();
            updateOrderPay.setPaymentCode(payRequest.getPayMethod().getValue());
            updateOrderPay.setPaymentName(payRequest.getPayMethod().getDesc());
            LambdaUpdateWrapper<OrderPayPO> payUpdateWrapper = new LambdaUpdateWrapper<>();
            payUpdateWrapper.eq(OrderPayPO::getPayId, orderPayPO.getPayId());
            AssertUtil.isTrue(!orderPayService.update(updateOrderPay, payUpdateWrapper),
                    String.format("更新支付订单失败,paySn:%s", orderPayPO.getPaySn()));
        }


        OrderPO orderPO1 = orderPOList.get(0);
        boolean isPresell = false;
        BigDecimal needPay = new BigDecimal("0.00");//需要支付的总金额
        //乡助卡需要支付的总金额
        BigDecimal xzCardAmount = BigDecimal.ZERO;
        for (OrderPO orderPO : orderPOList) {
            needPay = needPay.add(orderPO.getOrderAmount().subtract(orderPO.getBalanceAmount()).subtract(orderPO.getPayAmount()));
            xzCardAmount = xzCardAmount.add(orderPO.getXzCardAmount());
            if (!isPresell) {
                isPresell = OrderTypeEnum.isPresell(orderPO.getOrderType());//Andy.预售
            }
        }

        //余额单独支付，校验余额是否充足
        if (PayMethodEnum.BALANCE == payRequest.getPayMethod()) {
            AssertUtil.isTrue(needPay.compareTo(memberDb.getBalanceAvailable()) > 0, "余额不足，请选择其他支付方式");
        }

        //余额组合支付,计算出需要三方支付的金额(余额不足部分)
        if (payType.contains("BALANCE")) {
            needPay = orderPayModel.balancePay(orderPOList, memberDb);
        }

        //Andy.预售，不加此判断
        if (!isPresell && needPay.compareTo(BigDecimal.ZERO) == 0) {
            //余额足够支付,直接跳转支付成功页面
            return SldResponse.success("支付成功");
        }

        /**
         *还需支付金额大于0，执行三方支付
         */

        //修改订单支付方式
        OrderPO orderPO = new OrderPO();
        //预售，订单支付方式不允许修改。预售Andy修改
        if(isPresell) {
            orderPO.setPaymentCode(PayMethodEnum.COMBINATION_PAY.getValue());
            orderPO.setPaymentName(PayMethodEnum.COMBINATION_PAY.getDesc());
        } else {
            orderPO.setPaymentCode(payRequest.getPayMethod().getValue());
            orderPO.setPaymentName(payRequest.getPayMethod().getDesc());
        }
        //变更新老订单标识
        // setNewOrder(orderPOList, orderPO);
        if(isPresell) {
            PayValidation.validPresellRoutingRule(orderPresellService, orderPO1.getOrderType(),orderPO1.getPaySn(),orderPO1.getNewOrder(), orderPO.getNewOrder()); 
        }
        
        orderModel.updateOrderByExample(orderPO, orderExample);

        log.info("当前支付订单，orderSn:{} payMethod：{} newOrder：{} isPresell：{}",orderPO1.getOrderSn(),payRequest.getPayMethod().getValue(),orderPO.getNewOrder(),isPresell);
        //当前订单为预售订单 && 不是新订单，支付限制。Andy 预付，关闭
        if(isPresell) {
            if(ObjectUtils.isEmpty(orderPO.getNewOrder())){
                //Andy,初始化原本newOrder标识
                orderPO.setNewOrder(orderPO1.getNewOrder());
            }
            //Andy 预付，关闭
            PayValidation.validPresellPayMethodIsSupport(orderPresellService, orderPO1.getOrderType(), orderPO1.getPaySn(), orderPO1.getOrderSn(),
                    orderPO.getNewOrder(), payRequest.getPayMethod().getValue(), depositPayMethods, remainPayMethods);
        }

        // 请求来源IP
        String ip = WebUtil.getRealIp(request);

        // 构造三方支付请求参数
        PayRequestVO vo = payIntegration.getToken(payRequest, ip);
        return SldResponse.success(vo);
    }

    /**
     * @param orderPOList
     * @param orderPO
     * @return void
     * @description :实时更新新老订单标识
     */
    private void setNewOrder(List<OrderPO> orderPOList, OrderPO orderPO) {
        //判断微信总开关，未打开则设置成false直接返回
        String openNewOrder = stringRedisTemplate.opsForValue().get("openNewOrder");
        if (StringUtils.isEmpty(openNewOrder)) {
            orderPO.setNewOrder(false);
            return;
        }
        //判断是否开启指定店铺使用，本次子单不存在指定店铺中，则设置成false返回
        String wxWhite = stringRedisTemplate.opsForValue().get("wx_white");
        if (StringUtils.isNotBlank(wxWhite)) {
            List<String> whiteList = Arrays.asList(wxWhite.split(","));
            for (OrderPO po : orderPOList) {
                if (!whiteList.contains(po.getStoreId().toString())) {
                    orderPO.setNewOrder(false);
                    return;
                }
            }
        }
        //微信同一个支付单下，存在部分旧子订单则全置为旧订单
        long count = orderPOList.stream().map(OrderPO::getNewOrder).distinct().count();
        if (count > 1) {
            orderPO.setNewOrder(false);
        }
/*        //满足以上校验，仍为新订单，则直接返回
        if (orderPOList.get(0).getNewOrder()) {
            return;
        }
        //满足以上校验，为旧订单，则任选一个判断是否开通电子账户
        Long storeId = orderPOList.get(0).getStoreId();
        //判断是否开通银联账户，开通且不为配销订单时重新调整为新订单
        AccountCard bankAccount = billOperatinIntegration.detailByBankAccount(storeId.toString(),
                AccountCardTypeEnum.UNI_JS_STORE_HEAD);
        if (Objects.nonNull(bankAccount) && orderPOList.get(0).getOrderType() != OrderTypeEnum.ORDER_TYPE_7.getValue()) {
            orderPO.setNewOrder(true);
        }*/
    }

    @SneakyThrows
    @PostMapping("doTransfer")
    @ApiOperation("去汇款接口")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = WebConst.USER_HEADER, value = "客户信息 由网关处理",
                    paramType = "header", dataType = "String"),
            @ApiImplicitParam(
                    name = "X-Real-IP",
                    value = "请求来源IP",
                    paramType = "header",
                    dataType = "String")
    })
    public JsonResult<PayRequestVO> doTransfer(HttpServletRequest request,
                                               @RequestBody @NotNull @Valid BankTransferOrderRequest payRequest) {
        Member member = UserUtil.getUser(request, Member.class);
        Member memberDb = memberFeignClient.getMemberByMemberId(member.getMemberId());
        AssertUtil.notNull(memberDb, "未获取到您的个人信息");
        // 获取客户经理信息
        BizUserInfoDTO bizUserInfoDTO = OrderBuilder.getUser(request, BizUserInfoDTO.class);
        log.info("{}用呗支付，客户经理信息：{}", payRequest.getPaySn(), JSONObject.toJSONString(bizUserInfoDTO));

        //获取站长信息
        Member stationMaster = OrderBuilder.getStationMasterUser(request);
        log.info("{}用呗支付，站长信息：{}", payRequest.getPaySn(), JSONObject.toJSONString(stationMaster));


        AssertUtil.isTrue(PayMethodEnum.BANK_TRANSFER != payRequest.getPayMethod(), "暂不支持此种支付方式");

        //查询支付状态
        OrderPayPO orderPayPO = orderPayModel.getOrderPayByPaySn(payRequest.getPaySn());

        //查询支付单号下所有待支付的订单
        LambdaQueryWrapper<OrderPO> orderQuery = new LambdaQueryWrapper<>();
        orderQuery.eq(OrderPO::getPaySn, payRequest.getPaySn());
        orderQuery.in(OrderPO::getOrderState, OrderStatusEnum.WAIT_PAY.getValue(), OrderStatusEnum.WAIT_PAY_DEPOSIT.getValue());
        List<OrderPO> orderPOList = orderService.list(orderQuery);

        // 订单支付校验
        orderPayModel.orderPayCheck(orderPayPO, member, bizUserInfoDTO, stationMaster, orderPOList, payRequest.getPayMethod());

        // 代客下单，付款信息不可修改
        if (OrderPlaceUserRole.isValetOrder(orderPOList.get(0).getOrderPlaceUserRoleCode())
                && Objects.nonNull(bizUserInfoDTO)) {
            JSONObject payWayExtraInfo = orderPayPO.getPayWayExtraInfo();
            if (!Objects.isNull(payWayExtraInfo)) {
                OrderPayInfoVO.BankTransferInfo bankTransferInfo = JSON.parseObject(payWayExtraInfo.toJSONString(), OrderPayInfoVO.BankTransferInfo.class);
                if (!bankTransferInfo.getPaymentAccount().equals(payRequest.getPaymentAccount())
                        || !bankTransferInfo.getPaymentName().equals(payRequest.getPaymentName())) {
                    throw new BusinessException("代客下单订单付款银行卡信息信息不可修改，请确认后支付");
                }
            }
        }

        for (OrderPO itemPO : orderPOList) {
            if (!itemPO.getNewOrder()) {
                log.info("存在未开通云直通二级商户的店铺订单,orderSn:{},storeId:{}",itemPO.getOrderSn(),itemPO.getStoreId());
                throw new BusinessException("本单不支持使用银行卡汇款支付方式，请重新下单并直接选择银行卡汇款");
            }
        }

        JsonResult<AccountCard> accountResult = accountCardFacade.detail(AccountConstans.UNI_PLF_STORE_ID, AccountCardTypeEnum.UNI_JS_PLF_LARGEPAYMENT);
        if (null == accountResult || 200 != accountResult.getState() || null == accountResult.getData()) {
            log.error("未找到平台对应的大额订单专用电子账簿,paySn:{}",payRequest.getPaySn());
            throw new BusinessException("系统忙，请稍后再试");
        }

        AccountCard accountCard = accountResult.getData();
        try {
            bankTransferModel.createLargePayment(orderPayPO, orderPOList, payRequest, accountCard);
        } catch (Exception e) {
            log.error("补充银行卡转账汇款的付款人信息出现异常", e);
            throw new BusinessException("系统忙，请稍后再试");
        }
        return SldResponse.success();
    }

    @SneakyThrows
    @PostMapping("doPayByEnjoy")
    @ApiOperation("用呗支付接口")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = WebConst.USER_HEADER, value = "客户信息 由网关处理",
                    paramType = "header", dataType = "String"),
            @ApiImplicitParam(
                    name = "X-Real-IP",
                    value = "请求来源IP",
                    paramType = "header",
                    dataType = "String")
    })
    @Deprecated
    public JsonResult<String> doPayByEnjoy(HttpServletRequest request,
                                           @RequestBody @NotNull @Valid EnjoyPayOrderRequest payRequest) {
        Member member = UserUtil.getUser(request, Member.class);
        BizAssertUtil.notNull(member, "member客户信息为空");
        // 获取客户经理信息
        BizUserInfoDTO bizUserInfoDTO = OrderBuilder.getUser(request, BizUserInfoDTO.class);
        log.info("{}用呗支付，客户经理信息：{}", payRequest.getPaySn(), JSONObject.toJSONString(bizUserInfoDTO));

        //获取站长信息
        Member stationMaster = OrderBuilder.getStationMasterUser(request);
        log.info("{}用呗支付，站长信息：{}", payRequest.getPaySn(), JSONObject.toJSONString(stationMaster));

        // 校验支付方式
        BizAssertUtil.isTrue(!PayMethodEnum.isLoanPay(payRequest.getPayMethod()), "非信贷支付方式");

        //查询支付状态
        OrderPayPO orderPayPO = orderPayModel.getOrderPayByPaySn(payRequest.getPaySn());

        //查询支付单号下所有待支付的订单
        OrderExample orderExample = new OrderExample();
        orderExample.setPaySn(payRequest.getPaySn());
        orderExample.setMemberId(member.getMemberId());
        orderExample.setOrderStateIn(OrderConst.ORDER_STATE_5 + "," + OrderConst.ORDER_STATE_10);
        List<OrderPO> orderPOList = orderModel.getOrderList(orderExample, null);

        // 订单支付校验
        orderPayModel.orderPayCheck(orderPayPO, member, bizUserInfoDTO, stationMaster, orderPOList, payRequest.getPayMethod());

        // 代客下单，客户经理支付，分期信息不可修改
        if (OrderPlaceUserRole.isValetOrder(orderPOList.get(0).getOrderPlaceUserRoleCode())
                && Objects.nonNull(bizUserInfoDTO)) {
            JSONObject payWayExtraInfo = orderPayPO.getPayWayExtraInfo();
            if (!Objects.isNull(payWayExtraInfo)) {
                OrderPayInfoVO.EnjoyPayExtraInfo enjoyPayExtraInfo = JSON.parseObject(payWayExtraInfo.toJSONString(), OrderPayInfoVO.EnjoyPayExtraInfo.class);
                if (!enjoyPayExtraInfo.getLoanPeriod().equals(payRequest.getLoanPeriod())
                        || !enjoyPayExtraInfo.getRepaymentDay().equals(payRequest.getDueDay())
                        || !enjoyPayExtraInfo.getRepaymentMode().equals(payRequest.getRepaymentMode())) {
                    throw new BusinessException("代客下单订单分期信息不可修改，请确认后支付");
                }
            }
        }

        // 用呗支付校验优惠券批次号
        checkCouponBatchByEnjoyPay(payRequest);

        // 构造三方支付请求参数
        return payIntegration.enjoyPay(payRequest, orderPayPO, orderPOList);
    }

    /**
     * 用呗支付时，子单中必须有优惠券批次号，并且子单中批次券号唯一
     */
    public void checkCouponBatchByEnjoyPay(EnjoyPayOrderRequest payRequest) {
        OrderExample orderExample = new OrderExample();
        orderExample.setPaySn(payRequest.getPaySn());
        List<OrderPO> orderPOList = orderModel.getOrderList(orderExample, null);
        for (OrderPO orderPO : orderPOList) {
            OrderProductExample orderProductExample = new OrderProductExample();
            orderProductExample.setOrderSn(orderPO.getOrderSn());
            List<OrderProductPO> orderProductPOList = orderProductModel.getOrderProductList(orderProductExample, null);
            // 从客户中心获取订单使用的优惠券code  从goods获取用呗免息券批次号
            Set<String> couponBatchs = new HashSet<>();
            for (OrderProductPO orderProductPODb : orderProductPOList) {
                //if (StringUtils.isBlank(orderProductPODb.getFinanceRuleCode()))
                //    throw new MallException("该订单中" + orderProductPODb.getGoodsName() + "不支持分期");
                couponBatchs.add(orderProductPODb.getFinanceRuleCode());
            }
            if (couponBatchs.size() > 1) {
                // 同一家店铺的商品优惠券批次号只能一个
                log.error("优惠券批次号,{}", JSON.toJSONString(couponBatchs));
                throw new MallException("该订单中优惠券批次号不唯一,请检查重试!");
            }
        }
    }

    /**
     * 公众号支付/小程序支付，获取openid
     *
     * @param codeSource 1.小程序，2-微信内部浏览器
     * @param memberId   会员id
     * @param code       用户code
     * @return
     */
    private String getOpenId(Integer codeSource, Integer memberId, String code) {
        MemberWxsignExample memberWxsignExample = new MemberWxsignExample();
        memberWxsignExample.setMemberId(memberId);
        if (codeSource == 1) {
            //小程序
            memberWxsignExample.setResource(MemberWxSignConst.XCX);
        } else {
            //微信内部浏览器
            memberWxsignExample.setResource(MemberWxSignConst.GZH);
        }
        List<MemberWxsign> memberWxsignList = memberWxsignFeignClient.getMemberWxsignList(memberWxsignExample);
        if (!CollectionUtils.isEmpty(memberWxsignList)) {
            //有记录
            return memberWxsignList.get(0).getOpenid();
        }

        //没有授权记录，根据code获取openid
        String token_url = "";//获取openid的url
        if (codeSource == 1) {
            //小程序
            token_url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + stringRedisTemplate.opsForValue().get("login_wx_mini_appid") +
                    "&secret=" + stringRedisTemplate.opsForValue().get("login_wx_mini_appsecret") + "&js_code=" + code + "&grant_type=authorization_code";
        } else {
            //微信内部浏览器
            token_url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=" + stringRedisTemplate.opsForValue().get("login_wx_dev_appid") +
                    "&secret=" + stringRedisTemplate.opsForValue().get("login_wx_dev_appsecret") + "&code=" + code + "&grant_type=authorization_code";
        }
        String resp = HttpUtil.get(token_url);
        if (0 == JSONObject.parseObject(resp).getIntValue("errcode")) {
            return JSONObject.parseObject(resp).getString("openid");
        } else {
            log.error("获取openid出错：" + resp);
            throw new MallException("获取openid出错：");
        }
    }

    @ApiOperation("获取信贷产品信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "paymentCode", value = "支付方式code", required = true, paramType = "query"),
            @ApiImplicitParam(name = "paySn", value = "支付单号", required = true, paramType = "query")
    })
    @GetMapping("getLoanProductInfo")
    public JsonResult<LoanProductVO> getLoanProductInfo(@RequestParam("paymentCode") String paymentCode, @RequestParam("paySn") String paySn) {
        LoanProductVO loanProductInfo = payIntegration.getLoanProductInfo(paymentCode, paySn);
        return SldResponse.success(loanProductInfo);
    }

    @ApiOperation("订单分账")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单号", required = true, paramType = "query")
    })
    @GetMapping("profitSharding")
    public JsonResult profitSharding(@RequestParam("orderSn") String orderSn) {
        payIntegration.profitSharding(orderSn);
        return SldResponse.success();
    }

    @ApiOperation("获取可用乡助卡列表")
    @PostMapping("cardUse/list")
    public JsonResult<PageVO<CardUserQueryResponse>> cardUseList(HttpServletRequest request, @RequestBody CardListRequest useRequest) {
        Member member = UserUtil.getUser(request, Member.class);
        //预付订金不能使用乡助卡
        if(useRequest.getPromotionType() != null && PromotionConst.PROMOTION_TYPE_107 == useRequest.getPromotionType()){
            return SldResponse.success("预付订金不能使用乡助卡");
        }
        return iOrderPayService.listCardUser(member.getUserNo(), useRequest);
    }

    @SuppressWarnings("unchecked")
	@ApiOperation("订单分账金额")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单号", required = true, paramType = "query")
    })
    @GetMapping("profitShardingAmount")
    public Result<WxPayV3ShardPrice> profitShardingAmount(@RequestParam("orderSn") String orderSn) {

        OrderPO orderPo = orderService.getByOrderSn(orderSn);
        if(ObjectUtils.isEmpty(orderPo)) {
        	log.info("profitShardingAmount订单分账金额，条件参数：{}",orderSn);
        	OrderPresellVO data= orderPresellService.findByPayNo(orderSn);
        	if(ObjectUtils.isNotEmpty(data)) {
        		WxPayV3ShardPrice shardPriceVo = new WxPayV3ShardPrice();
                shardPriceVo.setCombineOrderNo(data.getPayNo());
                shardPriceVo.setAmount(data.getPayAmount());
                return ResultUtils.buildSuccessResult(shardPriceVo);
        	}
        }

        BizAssertUtil.notNull(orderPo, "订单不存在:" + orderSn);

        //是否存在乡助卡支付
        boolean isCard = orderPo.getXzCardAmount().compareTo(BigDecimal.ZERO) > 0;

        WxPayV3ShardPrice shardPriceVo = new WxPayV3ShardPrice();
        shardPriceVo.setCombineOrderNo(orderPo.getOrderSn());
        shardPriceVo.setAmount(orderPo.getOrderAmount());

        // 分账金额
        if (!isCard) {
            shardPriceVo.setPlatformAmount(
                    orderPo.getThirdpartnarFee()            // 代运营服务费
                            .add(orderPo.getServiceFee())           // 平台服务费
                            .add(orderPo.getBusinessCommission())   // 业务佣金
                            .add(orderPo.getOrderCommission())      // 订单佣金
            );
        }

        shardPriceVo.setOtherAmount(orderPo.getExpressFee());

        String discountDetail = orderPo.getActivityDiscountDetail();

        if (org.apache.commons.lang.StringUtils.isNotBlank(discountDetail)) {
            List<OrderSubmitDTO.PromotionInfo> promotionInfos =
                    com.gexin.fastjson.JSON.parseArray(discountDetail, OrderSubmitDTO.PromotionInfo.class);
            if (CollectionUtils.isNotEmpty(promotionInfos)) {
                for (OrderSubmitDTO.PromotionInfo promotionInfo : promotionInfos) {
                    // 平台优惠券 补差金额
                    if (promotionInfo.getPromotionType() != null
                            && promotionInfo.getPromotionType() == com.cfpamf.ms.mallpromotion.constant.PromotionConst.PROMOTION_TYPE_402
                            && !promotionInfo.getIsStore()
                            && !isCard) {
                        // 补差金额
                        shardPriceVo.setSubsidyAmount(promotionInfo.getDiscount());
                    }
                }
            }
        }

        return ResultUtils.buildSuccessResult(shardPriceVo);
    }

    @ApiOperation("放款失败补偿")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单号", required = true, paramType = "query")
    })
    @GetMapping("loanLendingCompensation")
    public Result<Boolean> loanLendingCompensation(@RequestParam("orderSn") String orderSn) {

        if (StringUtils.isBlank(orderSn)) {
            return ResultUtils.buildSuccessResult(Boolean.TRUE);
        }

        String[] orderSns = orderSn.split(",");

        for (int i = 0, s = orderSns.length; i < s; i++) {

            try {
                OrderPO orderPODb = orderService.getByOrderSn(orderSns[i]);
                if (orderPODb == null) {
                    throw new BusinessException(ErrorCodeEnum.U.DATA_NOT_EXISTS.getCode(), "订单不存在");
                }

                if (orderPODb.getLoanPayState() != LoanStatusEnum.WAIT_LENDING.getValue().intValue()) {
                    throw new BusinessException(ErrorCodeEnum.U.CHECK_FAILURE.getCode(),
                            "订单放款状态为:" + orderPODb.getLoanPayState() + " 不允许放款申请");
                }

                // Andy.预售
                OrderPresellPO orderPresellPO =
                        orderPresellService.queryBalanceInfoByOrderSn(orderPODb.getOrderSn());
                String payNo = OrderTypeEnum.isPresell(orderPODb.getOrderType()) ? orderPresellPO.getPayNo() : null;
                Result<Void> voidResult = orderModel.doLoanOperate(String.valueOf(orderSns[i]), OrderConst.OPT_USER_ID,payNo);

                if (voidResult.isSuccess()) {
                    orderModel.loanPaySuccess(orderPODb, OrderConst.ADMIN_ROLE, OrderConst.USER_ID_SYSTEM, OrderConst.USER_NAME_SYSTEM);
                } else {
                    // 非系统异常导致放款失败，记录日志，人工处理
                    orderModel.loanPayFail(orderPODb, voidResult.getErrorMsg());
                }

                /**
                 * 移除该订单起息补偿任务
                 */
                LambdaQueryWrapper<TaskQueuePO> taskQueueQuery = new LambdaQueryWrapper();
                taskQueueQuery.eq(TaskQueuePO::getBizId, orderSns[i]);
                taskQueueQuery.eq(TaskQueuePO::getBizType, TaskQueueBizTypeEnum.AUTO_LOAN);
                List<TaskQueuePO> taskQueues = taskQueueService.list(taskQueueQuery);
                if (CollectionUtils.isNotEmpty(taskQueues)) {
                    for (TaskQueuePO taskQueue : taskQueues) {
                        taskQueueService.taskSuccess(taskQueue);
                    }
                }
            } catch (Exception ex) {
                log.warn("起息补偿失败 单号:{}", orderSns[i], ex);
            }
        }

        return ResultUtils.buildSuccessResult(Boolean.TRUE);
    }


    @GetMapping("hasDuringRefund")
    @ApiOperation("判断是否有退款中的退订单")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = WebConst.USER_HEADER, value = "客户信息 由网关处理",
                    paramType = "header", dataType = "String"),
            @ApiImplicitParam(name = "paySn", value = "支付单号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "payFrom", defaultValue = "1", paramType = "query",
                    value = "支付来源，默人1==下单跳转支付，可以使用余额；2==订单列表跳转支付，不可使用余额")
    })
    public JsonResult<Boolean> hasDuringRefund(@RequestParam(value = "orderSn") String orderSn) {
        return SldResponse.success(orderReturnService.hasDuringRefund(orderSn));
    }


    @GetMapping("/checkWithHold")
    @ApiOperation("代扣还款检查")
    public JsonResult<MallWithholdCheckResponseVO> checkWithHold(@RequestParam("orderNo")String orderNo){
        MallWithholdCheckResponseVO responseVO = orderPayService.checkWithHold(orderNo);
        return SldResponse.success(responseVO);
    }

}

